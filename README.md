# Beacon MQTT Cloud

基于Go语言开发的MQTT多租户云平台，支持用户分组管理、权限控制和时序数据存储。

## 技术栈

- **Web框架**: Gin
- **数据库**: MongoDB (用户/分组数据) + TDengine (MQTT时序数据) + Redis (缓存)
- **MQTT服务**: Mochi-MQTT (TCP + WebSocket)
- **核心库**: JWT、Casbin、Gomail

## 项目结构

```
beacon/cloud/
├── cmd/
│   ├── server/              # 主程序入口
│   └── mqtt_test_client/    # MQTT测试客户端
├── internal/
│   ├── config/              # 配置管理
│   │   └── config_test.go   # 配置测试
│   ├── middleware/          # 中间件
│   ├── handler/             # HTTP处理器
│   │   └── *_test.go        # 处理器测试
│   ├── service/             # 业务逻辑层
│   │   └── *_test.go        # 服务层测试
│   ├── repository/          # 数据访问层
│   ├── model/               # 数据模型
│   │   └── *_test.go        # 模型验证测试
│   ├── mqtt/                # MQTT服务
│   │   └── *_test.go        # MQTT功能测试
│   └── routes/              # 路由配置
├── pkg/
│   ├── database/            # 数据库连接
│   ├── auth/                # 认证授权
│   └── utils/               # 工具函数
├── tests/
│   └── integration/         # 集成测试
├── configs/                 # 配置文件
└── docs/                    # 项目文档
```

## 快速开始

### 1. 环境要求

- Go 1.23.1+
- MongoDB 4.4+
- TDengine 3.0+
- Redis 6.0+

### 2. 配置文件

复制并修改配置文件：

```bash
cp configs/config.yaml.example configs/config.yaml
```

配置文件示例：

```yaml
MongoDB:
  username: beacon
  password: beacON028
  host: 127.0.0.1
  port: "27017"
  dbname: "beacon"

TaosDB:
  username: root
  password: taosdata
  host: 127.0.0.1
  port: "6041"
  dbname: "beacon"

Redis:
  host: 127.0.0.1
  port: "6379"
  password: "beacON028"
  db: 0

jwt_secret: www.beaconglobaltech.com
```

### 3. 环境变量

支持通过环境变量覆盖配置：

```bash
# 数据库配置
export MONGODB_HOST=127.0.0.1
export MONGODB_PORT=27017
export MONGODB_USERNAME=beacon
export MONGODB_PASSWORD=beacON028
export MONGODB_DBNAME=beacon

export REDIS_HOST=127.0.0.1
export REDIS_PORT=6379
export REDIS_PASSWORD=beacON028
export REDIS_DB=0

export TAOSDB_HOST=127.0.0.1
export TAOSDB_PORT=6041
export TAOSDB_USERNAME=root
export TAOSDB_PASSWORD=taosdata
export TAOSDB_DBNAME=beacon

# JWT配置
export JWT_SECRET=your-jwt-secret

# 服务器配置
export SERVER_PORT=8080
export APP_ENV=development
export DEBUG=true
export LOG_LEVEL=info
```

### 4. 运行程序

```bash
# 安装依赖
go mod tidy

# 运行程序
go run cmd/server/main.go

# 指定配置文件
go run cmd/server/main.go -config /path/to/config.yaml

# 查看帮助
go run cmd/server/main.go -help

# 查看版本
go run cmd/server/main.go -version

# 使用启动脚本（推荐）
./scripts/start_with_admin.sh
```

### 5. 默认管理员账户

系统首次启动时会自动创建默认管理员账户：

- **用户名**: `admin`
- **密码**: `beacON028`
- **邮箱**: `<EMAIL>`
- **角色**: 系统管理员

⚠️ **重要安全提醒**: 首次登录后请立即修改默认密码！

#### 管理员登录示例
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"beacON028"}'
```

#### 修改默认密码
```bash
curl -X POST http://localhost:8080/api/users/change-password \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"old_password":"beacON028","new_password":"YourNewPassword123!"}'
```

### 6. 编译部署

```bash
# 编译
go build -o beacon-server cmd/server/main.go

# 运行
./beacon-server

# 生产环境运行
APP_ENV=production DEBUG=false ./beacon-server
```

## 功能特性

### 多租户架构

- 用户分组管理
- 权限分层控制 (Admin > Group Creator > Group Member)
- 数据隔离

### MQTT集成

- TCP和WebSocket双协议支持
- 自定义认证和权限控制
- 时序数据存储

### 权限管理

- 基于Casbin的权限控制
- JWT认证
- 分组权限继承

### 数据存储

- MongoDB: 用户、分组、配置数据
- TDengine: MQTT时序数据
- Redis: 缓存和会话管理

## 开发指南

### 配置管理

配置系统支持：

1. YAML配置文件
2. 环境变量覆盖
3. 默认值设置
4. 配置验证

### 测试

项目采用标准的Go测试结构，测试文件与被测试代码位于同一包中：

```bash
# 运行所有测试
go test ./...

# 运行单元测试
go test ./internal/... -v

# 运行集成测试
go test ./tests/integration/... -v

# 运行特定模块测试
go test ./internal/config -v          # 配置模块测试
go test ./internal/handler -v         # 处理器测试
go test ./internal/service -v         # 服务层测试
go test ./internal/model -v           # 模型验证测试
go test ./internal/mqtt -v            # MQTT功能测试

# 运行性能测试
go test ./internal/mqtt -bench=. -v   # MQTT性能测试

# 生成测试覆盖率报告
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html

# 使用MQTT测试客户端
go run cmd/mqtt_test_client/main.go
```

#### 测试组织

- **单元测试**: 位于各模块内部，测试单个函数和方法
- **集成测试**: 位于 `tests/integration/`，测试完整的工作流程
- **性能测试**: 包含在相关模块中，测试系统性能指标
- **MQTT客户端测试**: 独立的测试客户端，用于验证MQTT功能

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t beacon-mqtt-cloud .

# 运行容器
docker run -d \
  --name beacon-server \
  -p 8080:8080 \
  -p 1883:1883 \
  -p 8083:8083 \
  -e APP_ENV=production \
  -e DEBUG=false \
  -v /path/to/config.yaml:/app/configs/config.yaml \
  beacon-mqtt-cloud
```

### 系统服务

```bash
# 创建systemd服务文件
sudo cp scripts/beacon-server.service /etc/systemd/system/

# 启动服务
sudo systemctl enable beacon-server
sudo systemctl start beacon-server

# 查看状态
sudo systemctl status beacon-server
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request。
