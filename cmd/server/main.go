package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/handler"
	"beacon/cloud/internal/middleware"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/mqtt"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/routes"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/auth"
	"beacon/cloud/pkg/cache"
	"beacon/cloud/pkg/database"
	"beacon/cloud/pkg/wireguard"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	configPath = flag.String("config", "configs/config.yaml", "配置文件路径")
)

func main() {
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化应用
	app, err := initializeApplication(cfg)
	if err != nil {
		log.Fatalf("❌ 应用初始化失败: %v", err)
	}

	// 启动服务器
	startServer(app)
}

// Application 应用程序结构
type Application struct {
	Config          *config.Config
	DBManager       *database.DatabaseManager
	TDengineManager *database.TDengineManager
	Services        *Services
	Handlers        *Handlers
	Middleware      *Middleware
	Engine          *gin.Engine
}

// Services 服务集合
type Services struct {
	Auth      *service.AuthService
	User      service.UserService
	Monitor   service.MonitorService
	System    service.SystemService
	Group     service.GroupService
	Alert     service.AlertService
	Device    service.DeviceService
	WireGuard wireguard.WireGuardManager
}

// Handlers 处理器集合
type Handlers struct {
	Auth      *handler.AuthHandler
	User      *handler.UserHandler
	Monitor   *handler.MonitorHandler
	System    *handler.SystemHandler
	Group     *handler.GroupHandler
	Device    *handler.DeviceHandler
	WireGuard *handler.WireGuardHandler
}

// Middleware 中间件集合
type Middleware struct {
	Manager *middleware.MiddlewareManager
	Auth    *middleware.AuthMiddleware
}

// initializeApplication 初始化应用程序
func initializeApplication(cfg *config.Config) (*Application, error) {
	ctx := context.Background()

	// 初始化数据库管理器
	dbManager := database.NewDatabaseManager(cfg)
	if err := dbManager.Initialize(ctx); err != nil {
		log.Printf("⚠️ 数据库初始化失败: %v", err)
		// 继续运行，但某些功能可能不可用
	}

	// 初始化Repository层
	userRepo := repository.NewUserRepository(dbManager.GetMongoDB().GetDatabase())
	groupRepo := repository.NewGroupRepository(dbManager.GetMongoDB().GetDatabase())
	memberRepo := repository.NewGroupMemberRepository(dbManager.GetMongoDB().GetDatabase())
	topicRepo := repository.NewTopicRepository(dbManager.GetMongoDB().GetDatabase())
	permissionRepo := repository.NewTopicPermissionRepository(dbManager.GetMongoDB().GetDatabase())
	joinRequestRepo := repository.NewGroupJoinRequestRepository(dbManager.GetMongoDB().GetDatabase())
	alertRepo := repository.NewAlertRuleRepository(dbManager.GetMongoDB().GetDatabase())
	deviceRepo := repository.NewDeviceRepository(dbManager.GetMongoDB().GetDatabase())
	deviceTypeRepo := repository.NewDeviceTypeRepository(dbManager.GetMongoDB().GetDatabase())

	// 初始化缓存管理器
	cacheManager := cache.NewManager(dbManager.GetRedis().GetClient())

	// 初始化服务层
	authService := service.NewAuthService(cfg, dbManager.GetRedis().GetClient())
	userService := service.NewUserService(userRepo, authService, cacheManager)
	groupService := service.NewGroupService(groupRepo, memberRepo, topicRepo, permissionRepo, joinRequestRepo, userService, cacheManager)
	deviceService := service.NewDeviceService(deviceRepo, deviceTypeRepo)

	// 初始化TDengine管理器
	tdengineManager := dbManager.GetTDengine()
	if tdengineManager == nil {
		log.Println("⚠️ TDengine管理器为nil，监控数据存储将不可用")
	}

	// 初始化监控服务
	monitorService := service.NewMonitorService(
		tdengineManager,
		nil, // MQTT管理器稍后设置
		userService,
	)

	// 初始化邮件服务
	emailService := service.NewEmailService(&cfg.Mail, cacheManager)

	// 初始化预警服务
	alertService := service.NewAlertService(
		alertRepo,
		tdengineManager,
		dbManager.GetRedis().GetClient(),
		groupService,
		emailService,
		cfg,
	)

	// 初始化系统管理服务
	systemLogRepo := repository.NewSystemLogRepository(tdengineManager)
	healthManager := database.NewHealthManager()

	systemService := service.NewSystemService(
		systemLogRepo,
		userRepo,
		healthManager,
		cacheManager,
	)

	// 初始化Casbin
	if _, err := auth.InitCasbin(); err != nil {
		log.Printf("⚠️ Casbin初始化失败: %v", err)
	} else {
		log.Println("✅ Casbin权限系统初始化成功")

		// 重新初始化以确保模型文件的修改生效
		if _, err := auth.ReInitCasbin(); err != nil {
			log.Printf("⚠️ Casbin重新初始化失败: %v", err)
		} else {
			log.Println("✅ Casbin权限系统重新初始化成功")
		}
	}

	// 初始化WireGuard管理器
	wgConfig := &wireguard.Config{
		Host:                cfg.WireGuard.Host,
		BasePort:            cfg.WireGuard.BasePort,
		MaxUsers:            cfg.WireGuard.MaxUsers,
		MaxServersPerUser:   cfg.WireGuard.MaxServersPerUser,
		MaxClientsPerServer: cfg.WireGuard.MaxClientsPerServer,
		BaseNetwork:         cfg.WireGuard.BaseNetwork,
		DefaultDNS:          cfg.WireGuard.DefaultDNS,
		DefaultMTU:          cfg.WireGuard.DefaultMTU,
		EncryptionKey:       cfg.WireGuard.EncryptionKey,
	}

	wgManager, err := wireguard.NewWireGuardManager(
		dbManager.GetMongoDB().GetDatabase(),
		dbManager.GetRedis().GetClient(),
		wgConfig,
	)
	if err != nil {
		log.Printf("⚠️ WireGuard管理器初始化失败: %v", err)
	} else {
		log.Println("✅ WireGuard管理器初始化成功")
	}

	// 初始化默认管理员账户
	initDefaultAdminUser(ctx, userService)

	// 初始化处理器
	authHandler := handler.NewAuthHandler(authService)
	userHandler := handler.NewUserHandler(userService, authService, emailService)
	monitorHandler := handler.NewMonitorHandler(monitorService)
	systemHandler := handler.NewSystemHandler(systemService)
	groupHandler := handler.NewGroupHandler(groupService, tdengineManager)
	alertHandler := handler.NewAlertHandler(alertService)
	deviceHandler := handler.NewDeviceHandler(deviceService)
	wireGuardHandler := handler.NewWireGuardHandler(wgManager)

	// 初始化中间件
	middlewareManager := middleware.NewMiddlewareManager(cfg, dbManager.GetRedis().GetClient(), systemService)
	authMiddleware := middleware.NewAuthMiddleware(
		cfg.JWTSecret,
		config.DefaultJWTExpiration,
		config.DefaultRefreshExpiration,
	)

	// 创建Gin引擎（使用默认中间件）
	engine := gin.Default()
	// 添加自定义中间件
	engine.Use(middlewareManager.Logger()) // 只用于存储请求记录到数据库
	engine.Use(middlewareManager.CORS())
	engine.Use(middlewareManager.ErrorHandler())

	// 设置路由
	routes.SetupRoutes(
		engine,
		authMiddleware,
		authHandler,
		userHandler,
		monitorHandler,
		systemHandler,
		groupHandler,
		alertHandler,
		deviceHandler,
		wireGuardHandler,
	)

	// 创建应用程序实例
	app := &Application{
		Config:          cfg,
		DBManager:       dbManager,
		TDengineManager: tdengineManager,
		Services: &Services{
			Auth:      authService,
			User:      userService,
			Monitor:   monitorService,
			System:    systemService,
			Group:     groupService,
			Alert:     alertService,
			Device:    deviceService,
			WireGuard: wgManager,
		},
		Handlers: &Handlers{
			Auth:      authHandler,
			User:      userHandler,
			Monitor:   monitorHandler,
			System:    systemHandler,
			Group:     groupHandler,
			Device:    deviceHandler,
			WireGuard: wireGuardHandler,
		},
		Middleware: &Middleware{
			Manager: middlewareManager,
			Auth:    authMiddleware,
		},
		Engine: engine,
	}

	return app, nil
}

// startServer 启动服务器
func startServer(app *Application) {
	ctx := context.Background()

	// 初始化设备相关数据库索引
	if err := initializeDeviceIndexes(ctx, app); err != nil {
		log.Printf("⚠️ 设备数据库索引初始化失败: %v", err)
	} else {
		log.Println("✅ 设备数据库索引初始化成功")
	}

	// 初始化预定义设备类型
	if err := app.Services.Device.InitializePredefinedDeviceTypes(ctx); err != nil {
		log.Printf("⚠️ 预定义设备类型初始化失败: %v", err)
	} else {
		log.Println("✅ 预定义设备类型初始化成功")
	}

	// 初始化MQTT服务
	if err := mqtt.InitializeMQTT(app.Config, app.DBManager.GetMongoDB(), app.DBManager.GetRedis(), app.DBManager.GetTDengine()); err != nil {
		log.Printf("⚠️ MQTT服务初始化失败: %v", err)
	} else {
		// 设置MQTT服务
		mqttManager := mqtt.GetMQTTManager()
		if mqttManager != nil {
			// 监控Hook现在由HookManager内部自动管理，无需手动创建
			log.Println("✅ MQTT Hook管理器已自动配置")

			// 设置分组服务到HookManager
			if hookManager := mqttManager.GetHookManager(); hookManager != nil {
				hookManager.SetGroupService(app.Services.Group)
				log.Println("✅ 分组服务已设置到MQTT Hook管理器")

				// 设置预警服务到HookManager
				hookManager.SetAlertService(app.Services.Alert)
				log.Println("✅ 预警服务已设置到MQTT Hook管理器")
			}

			// 将MQTT管理器设置为监控服务的管理器
			app.Services.Monitor.SetMQTTManager(mqttManager)
		}

		// 启动MQTT服务
		if err := mqtt.StartMQTT(); err != nil {
			log.Printf("⚠️ MQTT服务启动失败: %v", err)
		}
	}

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         ":" + app.Config.Server.Port,
		Handler:      app.Engine,
		ReadTimeout:  time.Duration(app.Config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(app.Config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(app.Config.Server.IdleTimeout) * time.Second,
	}

	// 优雅关闭处理
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动服务器
	go func() {
		log.Printf("🚀 服务器启动在端口 %s", app.Config.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ 服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	<-quit
	log.Println("🛑 正在关闭服务器...")

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("❌ 服务器关闭失败: %v", err)
	} else {
		log.Println("✅ HTTP服务器已关闭")
	}

	// 关闭MQTT服务
	if err := mqtt.StopMQTT(); err != nil {
		log.Printf("❌ MQTT服务关闭失败: %v", err)
	} else {
		log.Println("✅ MQTT服务已关闭")
	}

	// 关闭数据库连接
	if app.DBManager != nil {
		if err := app.DBManager.Close(ctx); err != nil {
			log.Printf("❌ 数据库连接关闭失败: %v", err)
		} else {
			log.Println("✅ 数据库连接已关闭")
		}
	}

	log.Println("👋 服务器已完全关闭")
}

// initDefaultAdminUser 初始化默认管理员账户
func initDefaultAdminUser(ctx context.Context, userService service.UserService) {
	// 默认管理员账户信息
	adminPassword := "beacON028"
	adminEmail := "<EMAIL>"

	// 检查管理员账户是否已存在
	_, err := userService.GetUserByEmail(ctx, adminEmail)
	if err == nil {
		// 管理员账户已存在
		log.Printf("✅ 管理员账户 '%s' 已存在", adminEmail)
		return
	}

	// 如果错误不是用户不存在，则记录错误
	if err != model.ErrUserNotFound {
		log.Printf("⚠️ 检查管理员账户时发生错误: %v", err)
		return
	}

	// 创建默认管理员账户
	adminUser := &model.CreateUserRequest{
		Email:    adminEmail,
		Password: adminPassword,
		Role:     model.RoleAdmin,
		Profile: model.UserProfile{
			FirstName:   "System",
			LastName:    "Administrator",
			DisplayName: "系统管理员",
			Company:     "Beacon Global Tech",
			Department:  "系统管理",
		},
	}

	// 注册管理员用户
	user, err := userService.RegisterUser(ctx, adminUser)
	if err != nil {
		log.Printf("❌ 创建默认管理员账户失败: %v", err)
		return
	}

	log.Printf("✅ 成功创建默认管理员账户: %s (ID: %s)", adminEmail, user.ID.Hex())
	log.Printf("🔑 管理员密码: %s", adminPassword)
	log.Println("⚠️  请在首次登录后及时修改默认密码！")
}

// initializeDeviceIndexes 初始化设备相关数据库索引
func initializeDeviceIndexes(ctx context.Context, app *Application) error {
	db := app.DBManager.GetMongoDB().GetDatabase()

	// 创建设备集合索引
	deviceCollection := db.Collection("devices")
	deviceIndexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "user_id", Value: 1},
				{Key: "serial_number", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "user_id", Value: 1},
				{Key: "imei_code", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "user_id", Value: 1},
				{Key: "device_type", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "user_id", Value: 1},
				{Key: "status", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "user_id", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
	}

	if _, err := deviceCollection.Indexes().CreateMany(ctx, deviceIndexes); err != nil {
		return fmt.Errorf("创建设备索引失败: %w", err)
	}

	// 创建设备类型集合索引
	deviceTypeCollection := db.Collection("device_types")
	deviceTypeIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "name", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "created_at", Value: -1}},
		},
	}

	if _, err := deviceTypeCollection.Indexes().CreateMany(ctx, deviceTypeIndexes); err != nil {
		return fmt.Errorf("创建设备类型索引失败: %w", err)
	}

	return nil
}
