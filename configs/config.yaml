MongoDB:
  username: beacon
  password: beacON028
  host: 127.0.0.1
  port: "27017"
  dbname: "beacon"

TaosDB:
  username: root
  password: taosdata
  host: 127.0.0.1
  port: "6041"
  dbname: "beacon"

Redis:
  host: 127.0.0.1
  port: "6379"
  password: "beacON028"
  db: 0

mail:
  host: smtp.exmail.qq.com
  port: "465"
  username: <EMAIL>
  password: 147258aA@

jwt_secret: www.beaconglobaltech.com

host: wg.beaconglobaltech.com # 服务器ip或域名，用于指定wireguard server host
NetworkInterface: "ens33" #用于wireguard外部通信的网卡以及网络吞吐量查询的默认网卡

# WireGuard 配置
wireguard:
  host: "wg.beaconglobaltech.com" # WireGuard服务器的公网域名或IP，用于客户端配置的endpoint
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 1
  max_clients_per_server: 20
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420
  encryption_key: "www.beaconglobaltech.com" # 请更改为32字节的密钥

server:
  port: "8080"
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 120

mqtt:
  tcp_port: "1884"
  websocket_port: "8084"
  max_clients: 10000
  buffer_size: 1024

# 预警系统配置
alert:
  frequency_limit:
    max_count: 3        # 最大触发次数
    time_window: 3600   # 时间窗口(秒) - 1小时

middleware:
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"]
    allowed_headers: ["Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"]
    exposed_headers: ["Content-Length"]
    allow_credentials: true
    max_age: 86400

  rate_limit:
    enabled: true
    rate: "1000-M"  # 100 requests per minute
    burst: 10
    skip_paths: ["/health", "/metrics"]

  logging:
    enabled: true
    format: "json"
    level: "info"
    skip_paths: ["/health", "/metrics", "/favicon.ico"]
    request_body: false
    response_body: false

  error_handler:
    enabled: true
    show_details: false


#测试号
# wechat:
#   appid: wx5fce1b68a48a359b
#   secret: bd89f2f41808669d4f7edea990d26ab4
#   template_id: TIPZWCT4h6hrKERN5SObzVSIS9wfW5X7pdyaVJMvBIs

#正式号
wechat:
  appid: wx34ec4a5f3f865c78
  secret: d40781dd9afcfff9bb334e10d5bb3265
  template_id: AbkrDvxHPMhtmon77tcdajRhpLgkcxJ-0nuC--cgWH4

sms:
  access_key_id: LTAI5tH1MLvjnwr2kgJDQBD7
  access_key_secret: ******************************
  sign_name: Beacon云
  sms_code: SMS_479075063

# sms:
#   access_key_id: LTAI5tM7KG3i2HMxMTbBzPN4
#   access_key_secret: ******************************
#   sign_name: 成都睿颖软件
#   sms_code: SMS_483285305
