{"info": {"_postman_id": "wireguard-api-collection", "name": "WireGuard API Collection", "description": "Complete WireGuard VPN management API collection for Beacon Cloud", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "api_base", "value": "{{base_url}}/api/v1/wireguard", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "server_id", "value": "", "type": "string"}, {"key": "client_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        console.log('Access token saved:', response.data.access_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}]}, {"name": "Server Management", "item": [{"name": "Create Server", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('server_id', response.data.id);", "        console.log('Server ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My VPN Server\",\n  \"dns\": [\"*******\", \"*******\"],\n  \"mtu\": 1420\n}"}, "url": {"raw": "{{api_base}}/servers", "host": ["{{api_base}}"], "path": ["servers"]}}}, {"name": "List Servers", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/servers?page=1&limit=10", "host": ["{{api_base}}"], "path": ["servers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "enabled", "value": "true", "disabled": true}]}}}, {"name": "Get Server Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/servers/{{server_id}}", "host": ["{{api_base}}"], "path": ["servers", "{{server_id}}"]}}}, {"name": "Update Server", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Server Name\",\n  \"dns\": [\"*******\", \"*******\"],\n  \"mtu\": 1420,\n  \"enabled\": true\n}"}, "url": {"raw": "{{api_base}}/servers/{{server_id}}", "host": ["{{api_base}}"], "path": ["servers", "{{server_id}}"]}}}, {"name": "Delete Server", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{api_base}}/servers/{{server_id}}", "host": ["{{api_base}}"], "path": ["servers", "{{server_id}}"]}}}]}, {"name": "Client Management", "item": [{"name": "Create Client", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('client_id', response.data.id);", "        console.log('Client ID saved:', response.data.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"server_id\": \"{{server_id}}\",\n  \"name\": \"My Client\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{api_base}}/clients", "host": ["{{api_base}}"], "path": ["clients"]}}}, {"name": "List Clients", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/clients?page=1&limit=10&server_id={{server_id}}", "host": ["{{api_base}}"], "path": ["clients"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "server_id", "value": "{{server_id}}"}, {"key": "enabled", "value": "true", "disabled": true}]}}}, {"name": "Get Client Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/clients/{{client_id}}", "host": ["{{api_base}}"], "path": ["clients", "{{client_id}}"]}}}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Client Name\",\n  \"email\": \"<EMAIL>\",\n  \"enabled\": true\n}"}, "url": {"raw": "{{api_base}}/clients/{{client_id}}", "host": ["{{api_base}}"], "path": ["clients", "{{client_id}}"]}}}, {"name": "Enable Client", "request": {"method": "POST", "header": [], "url": {"raw": "{{api_base}}/clients/{{client_id}}/enable", "host": ["{{api_base}}"], "path": ["clients", "{{client_id}}", "enable"]}}}, {"name": "Disable Client", "request": {"method": "POST", "header": [], "url": {"raw": "{{api_base}}/clients/{{client_id}}/disable", "host": ["{{api_base}}"], "path": ["clients", "{{client_id}}", "disable"]}}}, {"name": "Delete Client", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{api_base}}/clients/{{client_id}}", "host": ["{{api_base}}"], "path": ["clients", "{{client_id}}"]}}}]}, {"name": "Configuration Management", "item": [{"name": "Get Client Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/configs/clients/{{client_id}}", "host": ["{{api_base}}"], "path": ["configs", "clients", "{{client_id}}"]}}}, {"name": "Get Client QR Code", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/configs/clients/{{client_id}}/qr", "host": ["{{api_base}}"], "path": ["configs", "clients", "{{client_id}}", "qr"]}}}]}, {"name": "Statistics", "item": [{"name": "Get Client Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/stats/clients/{{client_id}}", "host": ["{{api_base}}"], "path": ["stats", "clients", "{{client_id}}"]}}}]}]}