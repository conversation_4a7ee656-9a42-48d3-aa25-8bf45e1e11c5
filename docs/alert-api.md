# MQTT预警系统API文档

## 概述

本文档描述了MQTT预警系统的完整API接口，包括预警规则管理、预警记录查询和预警统计等功能。

## 认证

所有API都需要JWT认证，请在请求头中包含：
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## API端点

### 1. 预警规则管理

#### 1.1 创建预警规则

**端点**: `POST /api/groups/alert-rules`

**请求体**:
```json
{
  "group_id": "group123",
  "topic": "sensor/temperature",
  "rule_name": "温度过高预警",
  "rule_type": "threshold",
  "description": "当温度超过35度时触发预警",
  "conditions": [
    {
      "field": "temperature",
      "operator": ">",
      "value": "35",
      "data_type": "number"
    }
  ],
  "level": 3,
  "frequency_limit": {
    "max_count": 3,
    "time_window": 3600
  },
  "notification": {
    "enabled": true,
    "channels": ["email"],
    "recipients": ["<EMAIL>"]
  },
  "enabled": true
}
```

**响应**:
```json
{
  "code": 0,
  "message": "创建预警规则成功",
  "data": {
    "id": "rule123",
    "group_id": "group123",
    "topic": "sensor/temperature",
    "rule_name": "温度过高预警",
    "rule_type": "threshold",
    "description": "当温度超过35度时触发预警",
    "conditions": [...],
    "level": 3,
    "frequency_limit": {...},
    "notification": {...},
    "enabled": true,
    "created_by": "user123",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### 1.2 获取预警规则列表

**端点**: `GET /api/groups/alert-rules`

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页大小，默认20，最大100
- `group_id` (string): 分组ID过滤
- `topic` (string): Topic过滤
- `rule_type` (string): 规则类型过滤
- `enabled` (bool): 启用状态过滤
- `level` (int): 预警级别过滤
- `search` (string): 搜索规则名称或描述

**示例**: `GET /api/groups/alert-rules?group_id=group123&enabled=true&page=1&page_size=10`

#### 1.3 获取预警规则详情

**端点**: `GET /api/groups/alert-rules/{ruleId}`

#### 1.4 更新预警规则

**端点**: `PUT /api/groups/alert-rules/{ruleId}`

**请求体**:
```json
{
  "rule_name": "更新后的规则名称",
  "enabled": false
}
```

#### 1.5 删除预警规则

**端点**: `DELETE /api/groups/alert-rules/{ruleId}`

#### 1.6 测试预警规则

**端点**: `POST /api/groups/alert-rules/{ruleId}/test`

**请求体**:
```json
{
  "temperature": 40,
  "humidity": 60
}
```

**响应**:
```json
{
  "code": 0,
  "message": "测试预警规则成功",
  "data": {
    "triggered": true,
    "message": "预警规则测试通过，触发值: 40"
  }
}
```

### 2. 预警记录管理

#### 2.1 获取预警记录列表

**端点**: `GET /api/groups/alert-records`

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页大小，默认20，最大100
- `group_id` (string): 分组ID过滤
- `topic` (string): Topic过滤
- `rule_id` (string): 规则ID过滤
- `level` (int): 预警级别过滤
- `status` (int): 状态过滤 (0-待处理, 1-已处理, 2-已忽略)
- `start_time` (string): 开始时间，RFC3339格式
- `end_time` (string): 结束时间，RFC3339格式

**示例**: `GET /api/groups/alert-records?group_id=group123&level=3&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z`

**响应**:
```json
{
  "code": 0,
  "message": "获取预警记录成功",
  "data": {
    "records": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "rule_id": "rule123",
        "topic_name": "sensor/temperature",
        "trigger_value": "40",
        "message": "预警规则 [温度过高预警] 被触发，Topic: sensor/temperature, 触发值: 40",
        "level": 3,
        "status": 0,
        "group_id": "group123",
        "rule_type": "threshold"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 3. 预警统计

#### 3.1 获取预警统计信息

**端点**: `GET /api/groups/alert-statistics`

**查询参数**:
- `group_id` (string): 分组ID，可选

**响应**:
```json
{
  "code": 0,
  "message": "获取预警统计成功",
  "data": {
    "total_rules": 10,
    "enabled_rules": 8,
    "total_alerts": 150,
    "alerts_by_level": {
      "1": 20,
      "2": 50,
      "3": 60,
      "4": 15,
      "5": 5
    },
    "alerts_by_status": {
      "0": 30,
      "1": 100,
      "2": 20
    },
    "recent_alerts": [...],
    "top_alert_topics": [
      {
        "topic": "sensor/temperature",
        "count": 50
      }
    ]
  }
}
```

## 预警规则类型

### 1. 阈值预警 (threshold)
监控数值是否超过设定阈值。

**支持的操作符**:
- `>`: 大于
- `<`: 小于
- `>=`: 大于等于
- `<=`: 小于等于
- `==`: 等于
- `!=`: 不等于

### 2. 变化率预警 (rate)
监控数据变化速率（待实现）。

### 3. 缺失数据预警 (missing)
监控数据上报间隔（待实现）。

### 4. 异常值预警 (anomaly)
基于历史数据的异常检测（待实现）。

### 5. 复合条件预警 (composite)
多个条件组合（待实现）。

## 预警级别

1. **信息 (Info)**: 一般性提醒
2. **警告 (Warning)**: 需要关注的情况
3. **错误 (Error)**: 需要处理的问题
4. **严重 (Critical)**: 紧急处理的问题
5. **灾难 (Fatal)**: 系统级严重问题

## 数据类型

- `number`: 数值型，支持整数和浮点数
- `string`: 字符串型
- `bool`: 布尔型

## 频率控制

预警系统支持频率控制，防止预警风暴：

- `max_count`: 时间窗口内最大预警次数
- `time_window`: 时间窗口长度（秒）

默认值：1小时内最多3次预警。

## 错误码

- `0`: 成功
- `400`: 参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误
- `503`: 服务不可用

## 使用示例

### 创建温度预警规则

```bash
curl -X POST "http://localhost:8080/api/groups/alert-rules" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "group123",
    "topic": "sensor/temperature",
    "rule_name": "温度过高预警",
    "rule_type": "threshold",
    "conditions": [
      {
        "field": "temperature",
        "operator": ">",
        "value": "35",
        "data_type": "number"
      }
    ],
    "level": 3,
    "enabled": true
  }'
```

### 查询预警记录

```bash
curl -X GET "http://localhost:8080/api/groups/alert-records?group_id=group123&level=3" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 测试预警规则

```bash
curl -X POST "http://localhost:8080/api/groups/alert-rules/rule123/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "temperature": 40,
    "humidity": 60
  }'
```
