# API测试指南

## 概述

本指南提供了新增API功能的测试方法和示例，帮助开发者快速验证API功能。

## 前置条件

1. **服务运行**: 确保服务器正在运行在 `http://localhost:8080`
2. **JWT Token**: 获取有效的JWT认证token
3. **数据库服务**: 确保MongoDB、TDengine、Redis服务正常运行
4. **MQTT服务**: 确保MQTT服务器正在运行（用于预警测试）

## 测试工具

### 命令行工具
- `curl`: HTTP请求测试
- `mosquitto_pub`: MQTT消息发布
- `jq`: JSON格式化输出

### 安装命令
```bash
# Ubuntu/Debian
sudo apt-get install curl mosquitto-clients jq

# macOS
brew install curl mosquitto jq
```

## 环境变量设置

```bash
export BASE_URL="http://localhost:8080"
export JWT_TOKEN="YOUR_JWT_TOKEN_HERE"
export MQTT_HOST="localhost"
export MQTT_PORT="1883"
```

## Topic数据管理API测试

### 1. 查询Topic数据

#### 基本查询
```bash
curl -X GET "$BASE_URL/api/groups/topic-data?topic=sensor/temperature" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 分页查询
```bash
curl -X GET "$BASE_URL/api/groups/topic-data?topic=sensor/temperature&pagesize=5&page=1" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 时间范围查询
```bash
curl -X GET "$BASE_URL/api/groups/topic-data?topic=sensor/temperature&timerange=1h" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 具体时间查询
```bash
START_TIME=$(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ)
END_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)

curl -X GET "$BASE_URL/api/groups/topic-data?topic=sensor/temperature&start_time=$START_TIME&end_time=$END_TIME" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

### 2. 删除Topic数据

```bash
START_TIME=$(date -u -d '2 hours ago' +%Y-%m-%dT%H:%M:%SZ)
END_TIME=$(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ)

curl -X DELETE "$BASE_URL/api/groups/topic-data?topic=sensor/temperature&start_time=$START_TIME&end_time=$END_TIME" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

## 预警系统API测试

### 1. 创建预警规则

#### 温度预警规则
```bash
curl -X POST "$BASE_URL/api/groups/alert-rules" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "test_group",
    "topic": "sensor/temperature",
    "rule_name": "温度过高预警",
    "rule_type": "threshold",
    "description": "当温度超过35度时触发预警",
    "conditions": [
      {
        "field": "temperature",
        "operator": ">",
        "value": "35",
        "data_type": "number"
      }
    ],
    "level": 3,
    "frequency_limit": {
      "max_count": 3,
      "time_window": 3600
    },
    "notification": {
      "enabled": true,
      "channels": ["email"],
      "recipients": ["<EMAIL>"]
    },
    "enabled": true
  }' | jq
```

#### 字符串类型预警规则
```bash
curl -X POST "$BASE_URL/api/groups/alert-rules" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "test_group",
    "topic": "device/status",
    "rule_name": "设备状态预警",
    "rule_type": "threshold",
    "description": "当设备状态为error时触发预警",
    "conditions": [
      {
        "field": "status",
        "operator": "equals",
        "value": "error",
        "data_type": "string"
      }
    ],
    "level": 4,
    "enabled": true
  }' | jq
```

### 2. 查询预警规则

#### 获取规则列表
```bash
curl -X GET "$BASE_URL/api/groups/alert-rules?group_id=test_group" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 搜索规则
```bash
curl -X GET "$BASE_URL/api/groups/alert-rules?search=温度" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 按级别过滤
```bash
curl -X GET "$BASE_URL/api/groups/alert-rules?level=3" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

### 3. 测试预警规则

```bash
# 首先获取规则ID（从上面的查询结果中获取）
RULE_ID="YOUR_RULE_ID_HERE"

curl -X POST "$BASE_URL/api/groups/alert-rules/$RULE_ID/test" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "temperature": 40,
    "humidity": 60
  }' | jq
```

### 4. 发布MQTT消息触发预警

#### 正常温度消息（不触发预警）
```bash
mosquitto_pub -h $MQTT_HOST -p $MQTT_PORT \
  -t "sensor/temperature" \
  -m '{"temperature": 25, "humidity": 60}' \
  -u test_user -P test_password
```

#### 高温消息（触发预警）
```bash
mosquitto_pub -h $MQTT_HOST -p $MQTT_PORT \
  -t "sensor/temperature" \
  -m '{"temperature": 40, "humidity": 50}' \
  -u test_user -P test_password
```

#### 设备错误状态消息
```bash
mosquitto_pub -h $MQTT_HOST -p $MQTT_PORT \
  -t "device/status" \
  -m '{"status": "error", "device_id": "device001"}' \
  -u test_user -P test_password
```

### 5. 查询预警记录

#### 基本查询
```bash
curl -X GET "$BASE_URL/api/groups/alert-records?group_id=test_group" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 按Topic查询
```bash
curl -X GET "$BASE_URL/api/groups/alert-records?topic=sensor/temperature" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 按级别查询
```bash
curl -X GET "$BASE_URL/api/groups/alert-records?level=3" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

#### 按时间范围查询
```bash
START_TIME=$(date -u -d '1 day ago' +%Y-%m-%dT%H:%M:%SZ)
END_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)

curl -X GET "$BASE_URL/api/groups/alert-records?start_time=$START_TIME&end_time=$END_TIME" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

### 6. 获取预警统计

```bash
curl -X GET "$BASE_URL/api/groups/alert-statistics?group_id=test_group" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq
```

## 完整测试流程

### 自动化测试脚本

我们提供了两个自动化测试脚本：

1. **Topic数据API测试**: `scripts/test-topic-data-api.sh`
2. **预警系统集成测试**: `scripts/test-mqtt-alert-integration.sh`

#### 运行测试脚本

```bash
# 设置执行权限
chmod +x scripts/test-topic-data-api.sh
chmod +x scripts/test-mqtt-alert-integration.sh

# 编辑脚本，设置JWT_TOKEN
vim scripts/test-topic-data-api.sh
vim scripts/test-mqtt-alert-integration.sh

# 运行测试
./scripts/test-topic-data-api.sh
./scripts/test-mqtt-alert-integration.sh
```

## 常见问题排查

### 1. 认证失败 (401)
- 检查JWT token是否有效
- 确认token格式正确
- 验证token是否过期

### 2. 权限不足 (403)
- 确认用户有相应的权限
- 检查Casbin权限配置
- 验证用户是否属于相应的分组

### 3. 服务不可用 (503)
- 检查TDengine服务状态
- 验证MongoDB连接
- 确认Redis服务正常

### 4. 预警不触发
- 检查预警规则是否启用
- 验证MQTT消息格式
- 确认频率限制设置
- 查看服务器日志

### 5. 数据查询为空
- 确认Topic名称正确
- 检查时间范围设置
- 验证是否有相应的数据

## 性能测试

### 并发测试示例

```bash
# 并发查询测试
for i in {1..10}; do
  curl -X GET "$BASE_URL/api/groups/topic-data?topic=sensor/temperature" \
    -H "Authorization: Bearer $JWT_TOKEN" &
done
wait

# 并发MQTT发布测试
for i in {1..50}; do
  mosquitto_pub -h $MQTT_HOST -p $MQTT_PORT \
    -t "sensor/temperature" \
    -m "{\"temperature\": $((30 + RANDOM % 20)), \"sequence\": $i}" \
    -u test_user -P test_password &
done
wait
```

## 监控和日志

### 查看服务器日志
```bash
# 查看应用日志
tail -f /var/log/your-app/app.log

# 查看MQTT相关日志
grep "MQTT\|预警\|Alert" /var/log/your-app/app.log
```

### 监控指标
- API响应时间
- 预警触发频率
- 数据库查询性能
- MQTT消息处理速度

---

## 总结

通过以上测试步骤，您可以全面验证新增API的功能：

1. ✅ Topic数据的查询和删除功能
2. ✅ 预警规则的完整生命周期管理
3. ✅ MQTT消息触发预警的端到端流程
4. ✅ 预警记录的查询和统计功能
5. ✅ 系统的性能和稳定性

建议在生产环境部署前，使用这些测试用例进行充分的验证。
