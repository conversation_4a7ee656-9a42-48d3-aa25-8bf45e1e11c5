# 分组管理系统 API 文档

## 概述

分组管理系统允许用户创建和管理分组，控制MQTT Topic的访问权限。系统支持以下功能：

- 用户最多创建20个分组（管理员无限制）
- 分组内Topic管理（自动添加分组ID前缀）
- 成员权限管理（订阅/发布权限）
- 加入申请审核机制

## 认证

所有API都需要JWT认证，在请求头中包含：
```
Authorization: Bearer <token>
```

## 分组管理

### 创建分组

**POST** `/api/groups`

创建新的分组。

**请求体：**
```json
{
  "name": "IoT设备组",
  "description": "物联网设备管理分组",
  "max_members": 100
}
```

**响应：**
```json
{
  "code": 200,
  "message": "分组创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "group_id": "abc123def456",
    "name": "IoT设备组",
    "description": "物联网设备管理分组",
    "creator_id": "507f1f77bcf86cd799439012",
    "status": "active",
    "max_members": 100,
    "member_count": 1,
    "topic_count": 0,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 获取分组信息

**GET** `/api/groups/{groupId}`

获取指定分组的详细信息。

**响应：**
```json
{
  "code": 200,
  "message": "获取分组信息成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "group_id": "abc123def456",
    "name": "IoT设备组",
    "description": "物联网设备管理分组",
    "creator_id": "507f1f77bcf86cd799439012",
    "status": "active",
    "max_members": 100,
    "member_count": 5,
    "topic_count": 3,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 更新分组信息

**PUT** `/api/groups/{groupId}`

更新分组信息（仅分组创建者可操作）。

**请求体：**
```json
{
  "name": "新的分组名称",
  "description": "更新后的描述",
  "status": "active",
  "max_members": 150
}
```

### 删除分组

**DELETE** `/api/groups/{groupId}`

删除分组（仅分组创建者可操作）。

### 获取分组列表

**GET** `/api/groups`

获取分组列表，支持分页和过滤。

**查询参数：**
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大100）
- `creator_id`: 按创建者过滤
- `status`: 按状态过滤（active/inactive/archived）
- `search`: 按名称搜索

### 获取用户分组列表

**GET** `/api/groups/my`

获取当前用户创建或加入的所有分组。

## 成员管理

### 申请加入分组

**POST** `/api/groups/join`

提交加入分组申请。

**请求体：**
```json
{
  "group_id": "abc123def456",
  "message": "希望加入这个分组学习IoT技术"
}
```

### 审核加入申请

**PUT** `/api/groups/requests/{requestId}/review`

审核加入申请（仅分组创建者可操作）。

**请求体：**
```json
{
  "status": "approved"
}
```

### 移除成员

**DELETE** `/api/groups/{groupId}/members/{userId}`

从分组中移除成员（仅分组创建者可操作）。

### 退出分组

**DELETE** `/api/groups/{groupId}/leave`

退出分组（创建者不能退出）。

### 获取分组成员列表

**GET** `/api/groups/{groupId}/members`

获取分组成员列表。

**响应：**
```json
{
  "code": 200,
  "message": "获取分组成员列表成功",
  "data": {
    "members": [
      {
        "id": "507f1f77bcf86cd799439013",
        "group_id": "abc123def456",
        "user_id": "507f1f77bcf86cd799439012",
        "role": "creator",
        "joined_at": "2024-01-01T00:00:00Z",
        "email": "<EMAIL>",
        "display_name": "分组创建者"
      }
    ],
    "total": 5,
    "page": 1,
    "size": 20
  }
}
```

### 获取加入申请列表

**GET** `/api/groups/requests`

获取当前用户所属分组的加入申请列表（仅分组创建者可查看自己创建的分组的申请）。

**查询参数：**
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大100）
- `status`: 按状态过滤（pending/approved/rejected）

**响应：**
```json
{
  "code": 200,
  "message": "获取加入申请列表成功",
  "data": {
    "requests": [
      {
        "id": "507f1f77bcf86cd799439017",
        "group_id": "abc123def456",
        "user_id": "507f1f77bcf86cd799439015",
        "status": "pending",
        "message": "希望加入这个分组学习IoT技术",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "email": "<EMAIL>",
        "display_name": "申请者",
        "group_name": "IoT设备组"
      }
    ],
    "total": 5,
    "page": 1,
    "size": 20
  }
}
```

## Topic管理

### 创建Topic

**POST** `/api/groups/{groupId}/topics`

在分组内创建Topic（仅分组创建者可操作）。

**请求体：**
```json
{
  "topic_name": "temperature"
}
```

**响应：**
```json
{
  "code": 201,
  "message": "Topic创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439014",
    "group_id": "abc123def456",
    "topic_name": "temperature",
    "full_name": "abc123def456/temperature",
    "creator_id": "507f1f77bcf86cd799439012",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 删除Topic

**DELETE** `/api/groups/{groupId}/topics/{topicId}`

删除Topic（仅分组创建者可操作）。

### 获取分组Topic列表

**GET** `/api/groups/{groupId}/topics`

获取分组内的所有Topic。

## 权限管理

### 设置Topic权限

**POST** `/api/groups/{groupId}/permissions`

为分组成员设置Topic权限（仅分组创建者可操作）。

**请求体：**
```json
{
  "user_id": "507f1f77bcf86cd799439015",
  "full_topic": "abc123def456/temperature",
  "can_pub": true,
  "can_sub": true
}
```

### 移除Topic权限

**DELETE** `/api/groups/{groupId}/permissions/{userId}?fullTopic={fullTopic}`

移除用户的Topic权限（仅分组创建者可操作）。

**查询参数：**
- `fullTopic`: 完整的Topic名称（必需）

**示例：**
```
DELETE /api/groups/abc123def456/permissions/507f1f77bcf86cd799439015?fullTopic=abc123def456/temperature
```

### 获取Topic权限列表

**GET** `/api/groups/{groupId}/permissions?fullTopic={fullTopic}`

获取指定Topic的权限列表。

**查询参数：**
- `fullTopic`: 完整的Topic名称（必需）
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大100）

**示例：**
```
GET /api/groups/abc123def456/permissions?fullTopic=abc123def456/temperature&page=1&page_size=20
```

**响应：**
```json
{
  "code": 200,
  "message": "获取Topic权限列表成功",
  "data": {
    "permissions": [
      {
        "id": "507f1f77bcf86cd799439016",
        "user_id": "507f1f77bcf86cd799439015",
        "group_id": "abc123def456",
        "full_topic": "abc123def456/temperature",
        "can_pub": true,
        "can_sub": true,
        "updated_at": "2024-01-01T00:00:00Z",
        "email": "<EMAIL>",
        "display_name": "用户名"
      }
    ],
    "total": 3,
    "page": 1,
    "size": 20
  }
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 使用示例

### 完整的分组管理流程

1. **创建分组**
```bash
curl -X POST http://localhost:8080/api/groups \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "温度监控组",
    "description": "用于温度传感器数据管理",
    "max_members": 50
  }'
```

2. **创建Topic**
```bash
curl -X POST http://localhost:8080/api/groups/abc123def456/topics \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "topic_name": "temperature"
  }'
```

3. **用户申请加入**
```bash
curl -X POST http://localhost:8080/api/groups/join \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "abc123def456",
    "message": "希望加入温度监控组"
  }'
```

4. **审核申请**
```bash
curl -X PUT http://localhost:8080/api/groups/requests/507f1f77bcf86cd799439017/review \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "approved"
  }'
```

5. **设置权限**
```bash
curl -X POST http://localhost:8080/api/groups/abc123def456/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "507f1f77bcf86cd799439015",
    "full_topic": "abc123def456/temperature",
    "can_pub": false,
    "can_sub": true
  }'
```

6. **查看Topic权限列表**
```bash
curl -X GET "http://localhost:8080/api/groups/abc123def456/permissions?fullTopic=abc123def456/temperature" \
  -H "Authorization: Bearer <token>"
```

7. **移除Topic权限**
```bash
curl -X DELETE "http://localhost:8080/api/groups/abc123def456/permissions/507f1f77bcf86cd799439015?fullTopic=abc123def456/temperature" \
  -H "Authorization: Bearer <token>"
```

## MQTT集成

分组管理系统与MQTT服务器集成，提供以下功能：

1. **认证**：使用用户ID作为MQTT客户端ID
2. **权限验证**：基于分组Topic权限进行发布/订阅控制
3. **Topic前缀**：自动为分组Topic添加分组ID前缀
4. **缓存优化**：使用Redis缓存权限信息，提高验证性能

### MQTT使用示例

```bash
# 订阅Topic（需要订阅权限）
mosquitto_sub -h localhost -p 1883 -i "507f1f77bcf86cd799439015" -t "abc123def456/temperature"

# 发布消息（需要发布权限）
mosquitto_pub -h localhost -p 1883 -i "507f1f77bcf86cd799439015" -t "abc123def456/temperature" -m "25.5"
```
