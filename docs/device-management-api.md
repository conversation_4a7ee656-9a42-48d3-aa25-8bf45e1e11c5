# 设备管理 API 文档 v2.0

## 概述

设备管理系统采用混合管理模式：
- **管理员**：负责设备的创建、更新、删除和全局管理
- **用户**：可以通过序列号和IMEI码将现有设备添加到自己的账户

## 认证和授权

### 认证方式
- **JWT Token**: 所有API都需要在请求头中包含有效的JWT令牌
```
Authorization: Bearer <jwt_token>
```

### 权限级别
- **管理员权限**: 可以访问所有 `/api/admin/*` 端点
- **用户权限**: 可以访问 `/api/user/*` 和部分公共端点

## 数据模型

### Device (设备)
```json
{
  "id": "string",           // 设备ID (ObjectID)
  "device_type": "string",  // 设备类型 (BC-3GM-R, BC-4GM-C, BC-ECR-C, BGTR)
  "device_name": "string",  // 设备名称
  "serial_number": "string", // 序列号 (全局唯一)
  "imei_code": "string",    // IMEI码 (全局唯一)
  "user_id": "string",      // 用户ID (可选，设备分配后才有值)
  "assignment": "string",   // 分配状态: "unassigned" | "assigned"
  "created_at": "datetime", // 创建时间
  "updated_at": "datetime"  // 更新时间
}
```

### DeviceType (设备类型)
```json
{
  "id": "string",
  "name": "string",         // 类型名称
  "description": "string",  // 类型描述
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 请求/响应模型

#### CreateDeviceRequest
```json
{
  "device_type": "string",   // 必需，设备类型
  "device_name": "string",   // 必需，设备名称
  "serial_number": "string", // 必需，序列号
  "imei_code": "string"      // 必需，IMEI码
}
```

#### AssignDeviceRequest
```json
{
  "serial_number": "string", // 必需，序列号
  "imei_code": "string"      // 必需，IMEI码
}
```

#### UpdateDeviceRequest
```json
{
  "device_name": "string",   // 可选，设备名称
  "device_type": "string"    // 可选，设备类型
}
```

## 管理员 API

### 1. 创建设备
**POST** `/api/admin/devices`

创建新设备，设备创建时不分配给任何用户。

**请求体:**
```json
{
  "device_type": "BC-3GM-R",
  "device_name": "测试设备001",
  "serial_number": "SN001",
  "imei_code": "IMEI001"
}
```

**响应:**
```json
{
  "success": true,
  "message": "设备创建成功",
  "data": {
    "device": {
      "id": "507f1f77bcf86cd799439011",
      "device_type": "BC-3GM-R",
      "device_name": "测试设备001",
      "serial_number": "SN001",
      "imei_code": "IMEI001",
      "user_id": null,
      "assignment": "unassigned",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 2. 获取所有设备列表
**GET** `/api/admin/devices`

获取系统中所有设备的列表，包括已分配和未分配的设备。

**查询参数:**
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10
- `assignment` (可选): 过滤分配状态 ("assigned" | "unassigned")

**响应:**
```json
{
  "success": true,
  "message": "获取设备列表成功",
  "data": {
    "devices": [
      {
        "id": "507f1f77bcf86cd799439011",
        "device_type": "BC-3GM-R",
        "device_name": "测试设备001",
        "serial_number": "SN001",
        "imei_code": "IMEI001",
        "user_id": "507f1f77bcf86cd799439012",
        "assignment": "assigned",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 3. 获取设备详情
**GET** `/api/admin/devices/{id}`

获取指定设备的详细信息。

**响应:**
```json
{
  "success": true,
  "message": "获取设备详情成功",
  "data": {
    "device": {
      "id": "507f1f77bcf86cd799439011",
      "device_type": "BC-3GM-R",
      "device_name": "测试设备001",
      "serial_number": "SN001",
      "imei_code": "IMEI001",
      "user_id": "507f1f77bcf86cd799439012",
      "assignment": "assigned",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 4. 更新设备
**PUT** `/api/admin/devices/{id}`

更新设备信息。

**请求体:**
```json
{
  "device_name": "更新后的设备名称",
  "device_type": "BC-4GM-C"
}
```

**响应:**
```json
{
  "success": true,
  "message": "设备更新成功",
  "data": {
    "device": {
      "id": "507f1f77bcf86cd799439011",
      "device_type": "BC-4GM-C",
      "device_name": "更新后的设备名称",
      "serial_number": "SN001",
      "imei_code": "IMEI001",
      "user_id": "507f1f77bcf86cd799439012",
      "assignment": "assigned",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T01:00:00Z"
    }
  }
}
```

### 5. 删除设备
**DELETE** `/api/admin/devices/{id}`

删除指定设备。

**响应:**
```json
{
  "success": true,
  "message": "设备删除成功",
  "data": null
}
```

## 用户 API

### 1. 添加设备到账户
**POST** `/api/user/devices`

用户通过序列号和IMEI码将现有设备添加到自己的账户。

**请求体:**
```json
{
  "serial_number": "SN001",
  "imei_code": "IMEI001"
}
```

**响应:**
```json
{
  "success": true,
  "message": "设备添加成功",
  "data": {
    "device": {
      "id": "507f1f77bcf86cd799439011",
      "device_type": "BC-3GM-R",
      "device_name": "测试设备001",
      "serial_number": "SN001",
      "imei_code": "IMEI001",
      "user_id": "507f1f77bcf86cd799439012",
      "assignment": "assigned",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T01:00:00Z"
    }
  }
}
```

### 2. 获取用户设备列表
**GET** `/api/user/devices`

获取当前用户的设备列表。

**查询参数:**
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10

**响应:**
```json
{
  "success": true,
  "message": "获取用户设备列表成功",
  "data": {
    "devices": [
      {
        "id": "507f1f77bcf86cd799439011",
        "device_type": "BC-3GM-R",
        "device_name": "测试设备001",
        "serial_number": "SN001",
        "imei_code": "IMEI001",
        "user_id": "507f1f77bcf86cd799439012",
        "assignment": "assigned",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T01:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 3. 获取用户设备详情
**GET** `/api/user/devices/{id}`

获取用户指定设备的详细信息。

**响应:**
```json
{
  "success": true,
  "message": "获取设备详情成功",
  "data": {
    "device": {
      "id": "507f1f77bcf86cd799439011",
      "device_type": "BC-3GM-R",
      "device_name": "测试设备001",
      "serial_number": "SN001",
      "imei_code": "IMEI001",
      "user_id": "507f1f77bcf86cd799439012",
      "assignment": "assigned",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T01:00:00Z"
    }
  }
}
```

### 4. 从账户移除设备
**DELETE** `/api/user/devices/{id}`

从用户账户中移除设备，设备状态变为未分配。

**响应:**
```json
{
  "success": true,
  "message": "设备移除成功",
  "data": null
}
```

## 公共 API

### 1. 获取设备类型列表
**GET** `/api/device-types`

获取所有可用的设备类型列表。一次性返回所有设备类型，无需分页。

**响应:**
```json
{
  "success": true,
  "message": "获取设备类型列表成功",
  "data": {
    "device_types": [
      {
        "id": "507f1f77bcf86cd799439013",
        "name": "BC-3GM-R",
        "description": "3G模块设备",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": "507f1f77bcf86cd799439014",
        "name": "BC-4GM-C",
        "description": "4G模块设备",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": "507f1f77bcf86cd799439015",
        "name": "BC-ECR-C",
        "description": "ECR控制器设备",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": "507f1f77bcf86cd799439016",
        "name": "BGTR",
        "description": "GPS追踪器设备",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## 设备类型管理 API (管理员)

### 1. 创建设备类型
**POST** `/api/device-types`

**请求体:**
```json
{
  "name": "NEW-DEVICE-TYPE",
  "description": "新设备类型描述"
}
```

**响应:**
```json
{
  "success": true,
  "message": "设备类型创建成功",
  "data": {
    "device_type": {
      "id": "507f1f77bcf86cd799439015",
      "name": "NEW-DEVICE-TYPE",
      "description": "新设备类型描述",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 2. 更新设备类型
**PUT** `/api/device-types/{id}`

**请求体:**
```json
{
  "name": "UPDATED-DEVICE-TYPE",
  "description": "更新后的设备类型描述"
}
```

### 3. 删除设备类型
**DELETE** `/api/device-types/{id}`

**响应:**
```json
{
  "success": true,
  "message": "设备类型删除成功",
  "data": null
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数无效
- `401`: 未授权（需要登录）
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突（如序列号或IMEI码已存在）
- `500`: 服务器内部错误

### 设备相关错误

- **设备已分配**: 当尝试添加已被其他用户分配的设备时
- **设备未找到**: 当提供的序列号和IMEI码组合不存在时
- **设备类型无效**: 当设备类型不在预定义列表中时
- **全局唯一性冲突**: 当序列号或IMEI码在全局范围内重复时

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 业务流程

### 设备生命周期

1. **设备创建** (管理员)
   - 管理员通过 `/api/admin/devices` 创建设备
   - 设备状态为 `unassigned`，无用户关联

2. **设备分配** (用户)
   - 用户通过 `/api/user/devices` 提供序列号和IMEI码
   - 系统验证设备存在且未分配
   - 设备状态更新为 `assigned`，关联到用户

3. **设备使用** (用户)
   - 用户可以查看和管理自己的设备
   - 用户可以获取设备详情和列表

4. **设备释放** (用户)
   - 用户通过 `/api/user/devices/{id}` 移除设备
   - 设备状态恢复为 `unassigned`，可被其他用户添加

5. **设备管理** (管理员)
   - 管理员可以查看所有设备状态
   - 管理员可以更新设备信息
   - 管理员可以删除设备

### 权限矩阵

| 操作 | 管理员 | 用户 | 说明 |
|------|--------|------|------|
| 创建设备 | ✅ | ❌ | 仅管理员可创建 |
| 查看所有设备 | ✅ | ❌ | 管理员看全部，用户看自己的 |
| 查看用户设备 | ❌ | ✅ | 用户只能看自己的设备 |
| 更新设备信息 | ✅ | ❌ | 仅管理员可更新 |
| 删除设备 | ✅ | ❌ | 仅管理员可删除 |
| 添加设备到账户 | ❌ | ✅ | 用户添加现有设备 |
| 移除设备从账户 | ❌ | ✅ | 用户移除自己的设备 |
| 管理设备类型 | ✅ | ❌ | 仅管理员可管理 |
| 查看设备类型 | ✅ | ✅ | 所有人可查看 |

## 数据验证

### 设备验证规则
- 设备类型必须存在于预定义列表中
- 序列号在全局范围内唯一
- IMEI码在全局范围内唯一
- 设备名称不能为空
- 序列号和IMEI码格式验证

### 设备类型验证规则
- 设备类型名称全局唯一
- 名称不能为空
- 名称格式：大写字母、数字和连字符

## 数据库索引

系统自动创建以下索引以优化查询性能：

### 设备集合索引
- `{serial_number: 1}` (唯一) - 全局序列号唯一性
- `{imei_code: 1}` (唯一) - 全局IMEI码唯一性
- `{user_id: 1}` - 用户设备查询优化
- `{assignment: 1}` - 分配状态查询优化
- `{device_type: 1}` - 设备类型过滤优化
- `{created_at: -1}` - 时间排序优化

### 设备类型集合索引
- `{name: 1}` (唯一) - 设备类型名称唯一性
- `{created_at: -1}` - 时间排序优化

## 性能考虑

### 查询优化
- 使用适当的数据库索引
- 分页查询避免大量数据传输
- 缓存设备类型列表（很少变更）

### 并发控制
- 设备分配操作使用事务确保一致性
- 全局唯一性约束防止重复数据
- 乐观锁定处理并发更新

## 安全考虑

### 数据隔离
- 用户只能访问自己的设备
- 管理员可以访问所有设备
- 严格的权限验证

### 输入验证
- 所有输入参数进行格式验证
- SQL注入防护
- XSS攻击防护

### 审计日志
- 记录所有设备操作
- 包含用户信息和时间戳
- 便于问题追踪和安全审计
```
