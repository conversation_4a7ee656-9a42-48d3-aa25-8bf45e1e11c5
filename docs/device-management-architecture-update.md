# 设备管理架构更新文档

## 概述

根据用户需求，我们对设备管理系统进行了重大架构调整，从多租户模式转换为混合管理模式：
- 设备由管理员统一创建和管理
- 用户可以通过序列号和IMEI码添加现有设备到自己的账户
- 设备序列号和IMEI码在全局范围内唯一

## 主要变更

### 1. 数据模型变更

#### Device模型更新
- `UserID` 字段从必需改为可选 (`*primitive.ObjectID`)
- 新增 `Assignment` 字段，表示设备分配状态
- 新增设备分配状态枚举：
  - `unassigned`: 未分配
  - `assigned`: 已分配

#### 新增请求类型
- `AssignDeviceRequest`: 用户添加设备请求
  ```go
  type AssignDeviceRequest struct {
      SerialNumber string `json:"serial_number" binding:"required"`
      IMEICode     string `json:"imei_code" binding:"required"`
  }
  ```

#### 新增错误类型
- `ErrDeviceAlreadyAssigned`: 设备已被分配
- `ErrDeviceNotAssigned`: 设备未分配
- `ErrDeviceAssignedToOther`: 设备已分配给其他用户
- `ErrCannotAssignOwnDevice`: 不能分配已拥有的设备

### 2. 数据库索引变更

#### 旧索引（已删除）
```javascript
// 用户范围内的唯一性约束
{user_id: 1, serial_number: 1} // 唯一
{user_id: 1, imei_code: 1}     // 唯一
```

#### 新索引
```javascript
// 全局唯一性约束
{serial_number: 1}  // 唯一
{imei_code: 1}      // 唯一
{assignment: 1}     // 普通索引
{user_id: 1}        // 普通索引（用于查询用户设备）
```

### 3. 服务层重构

#### DeviceService接口更新
**管理员方法：**
- `AdminCreateDevice`: 创建设备（不指定用户）
- `AdminGetDevice`: 获取任意设备
- `AdminUpdateDevice`: 更新任意设备
- `AdminDeleteDevice`: 删除任意设备
- `AdminListDevices`: 列出所有设备

**用户方法：**
- `UserAssignDevice`: 用户添加设备
- `UserUnassignDevice`: 用户移除设备
- `UserListDevices`: 列出用户设备
- `UserGetDevice`: 获取用户设备

#### Repository层更新
- 移除用户范围的唯一性检查方法
- 新增全局唯一性检查方法
- 新增设备分配相关方法：
  - `GetBySerialNumberAndIMEI`: 根据序列号和IMEI查找设备
  - `GetUserDevices`: 获取用户设备列表

### 4. 处理器层重构

#### 新的路由结构
**管理员路由（需要管理员权限）：**
- `POST /api/admin/devices` - 创建设备
- `GET /api/admin/devices` - 获取所有设备列表
- `GET /api/admin/devices/:id` - 获取设备详情
- `PUT /api/admin/devices/:id` - 更新设备
- `DELETE /api/admin/devices/:id` - 删除设备

**用户路由（需要用户认证）：**
- `POST /api/user/devices` - 添加设备
- `GET /api/user/devices` - 获取用户设备列表
- `GET /api/user/devices/:id` - 获取用户设备详情
- `DELETE /api/user/devices/:id` - 移除设备

### 5. 权限配置更新

#### Casbin权限策略
```csv
# 管理员权限（已有）
p, admin, /api/*, *
p, admin, /api/admin/*, *

# 用户权限（新增）
p, user, /api/user/devices/*, *
p, user, /api/device-types, GET
```

## 业务流程

### 设备创建流程
1. 管理员通过 `/api/admin/devices` 创建设备
2. 设备创建时不指定用户，状态为 `unassigned`
3. 序列号和IMEI码在全局范围内必须唯一

### 用户添加设备流程
1. 用户通过 `/api/user/devices` 提供序列号和IMEI码
2. 系统查找匹配的未分配设备
3. 如果找到且未分配，则分配给用户
4. 设备状态更新为 `assigned`，设置 `user_id`

### 用户移除设备流程
1. 用户通过 `/api/user/devices/:id` 移除设备
2. 设备状态更新为 `unassigned`，清除 `user_id`
3. 设备可以被其他用户重新添加

## 测试覆盖

### 现有测试更新
- 更新所有设备相关测试使用新的API端点
- 修改测试路由从 `/api/devices` 到 `/api/admin/devices`

### 新增测试
- `TestUserAssignDevice`: 测试用户设备分配流程
  - 管理员创建设备
  - 用户添加设备
  - 验证用户设备列表

## 向后兼容性

### 不兼容变更
- API端点路径变更
- 设备创建不再需要用户ID
- 设备唯一性约束从用户范围改为全局范围

### 迁移建议
1. 更新前端应用使用新的API端点
2. 管理员界面使用 `/api/admin/devices/*` 端点
3. 用户界面使用 `/api/user/devices/*` 端点
4. 更新API文档和集成测试

## 性能影响

### 正面影响
- 减少了复合索引的复杂性
- 简化了设备查询逻辑
- 提高了全局设备管理效率

### 注意事项
- 全局唯一性约束可能影响大规模部署
- 需要监控设备分配/取消分配操作的性能

## 安全考虑

### 权限隔离
- 管理员和用户操作完全分离
- 用户只能操作自己的设备
- 管理员可以查看和管理所有设备

### 数据保护
- 用户无法直接访问其他用户的设备信息
- 设备分配状态防止重复分配
- 序列号和IMEI码的全局唯一性防止冲突
