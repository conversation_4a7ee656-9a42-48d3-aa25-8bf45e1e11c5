# 分组管理系统使用指南

## 系统概述

分组管理系统是一个基于MongoDB的高性能MQTT权限管理解决方案，支持用户创建分组、管理Topic权限，并与MQTT服务器深度集成，提供细粒度的发布/订阅权限控制。

## 核心特性

### 🎯 权限分离设计
- **分组创建者**：拥有分组的完全管理权限
- **分组成员**：根据权限设置进行Topic访问
- **系统管理员**：无分组数量限制，可管理所有分组

### 🚀 高并发性能
- **Redis缓存**：权限信息缓存，毫秒级权限验证
- **MongoDB索引优化**：复合索引支持高效查询
- **批量操作**：支持批量权限设置和成员管理

### 🔒 安全机制
- **JWT认证**：所有API都需要有效的JWT令牌
- **Casbin授权**：基于RBAC的细粒度权限控制
- **Topic前缀隔离**：自动添加分组ID前缀，避免Topic冲突

## 数据库设计

### 核心表结构

#### 1. 分组信息表 (groups)
```javascript
{
  "_id": ObjectId,
  "group_id": "abc123def456",      // 分组唯一业务ID
  "name": "IoT设备组",
  "description": "物联网设备管理分组",
  "creator_id": "507f1f77bcf86cd799439012",
  "status": "active",              // active/inactive/archived
  "max_members": 100,
  "created_at": ISODate,
  "updated_at": ISODate
}
```

**索引：**
- `group_id`: 唯一索引
- `name + creator_id`: 复合唯一索引
- `creator_id`: 普通索引

#### 2. 分组成员表 (group_members)
```javascript
{
  "_id": ObjectId,
  "group_id": "abc123def456",
  "user_id": "507f1f77bcf86cd799439015",
  "role": "member",                // creator/member
  "joined_at": ISODate
}
```

**索引：**
- `group_id + user_id`: 复合唯一索引
- `user_id`: 普通索引

#### 3. Topic信息表 (topics)
```javascript
{
  "_id": ObjectId,
  "group_id": "abc123def456",
  "topic_name": "temperature",     // 原始名称
  "full_name": "abc123def456/temperature", // 完整名称
  "creator_id": "507f1f77bcf86cd799439012",
  "created_at": ISODate
}
```

**索引：**
- `full_name`: 唯一索引
- `topic_name + group_id`: 复合唯一索引
- `group_id`: 普通索引

#### 4. Topic权限表 (topic_permissions)
```javascript
{
  "_id": ObjectId,
  "user_id": "507f1f77bcf86cd799439015",
  "group_id": "abc123def456",
  "full_topic": "abc123def456/temperature",
  "can_pub": true,
  "can_sub": true,
  "updated_at": ISODate
}
```

**索引：**
- `user_id + full_topic`: 复合唯一索引（权限验证核心索引）
- `full_topic`: 普通索引
- `group_id`: 普通索引

#### 5. 加入申请表 (group_join_requests)
```javascript
{
  "_id": ObjectId,
  "group_id": "abc123def456",
  "user_id": "507f1f77bcf86cd799439015",
  "status": "pending",             // pending/approved/rejected
  "message": "申请加入原因",
  "created_at": ISODate,
  "updated_at": ISODate,
  "reviewed_by": "507f1f77bcf86cd799439012",
  "reviewed_at": ISODate
}
```

**索引：**
- `group_id + user_id`: 复合唯一索引
- `user_id`: 普通索引
- `status`: 普通索引

## 使用流程

### 1. 分组创建与管理

#### 创建分组
```bash
# 普通用户最多创建20个分组
curl -X POST http://localhost:8080/api/groups \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "智能家居控制组",
    "description": "用于智能家居设备的统一管理",
    "max_members": 50
  }'
```

#### 管理分组信息
```bash
# 更新分组信息（仅创建者）
curl -X PUT http://localhost:8080/api/groups/abc123def456 \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "智能家居控制组v2",
    "max_members": 80
  }'
```

### 2. Topic管理

#### 创建Topic
```bash
# Topic会自动添加分组ID前缀
curl -X POST http://localhost:8080/api/groups/abc123def456/topics \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "topic_name": "living_room/temperature"
  }'
# 实际创建的Topic: abc123def456/living_room/temperature
```

#### Topic命名规范
- **原始名称**：`living_room/temperature`
- **完整名称**：`{group_id}/living_room/temperature`
- **MQTT使用**：客户端使用完整名称进行发布/订阅

### 3. 成员管理

#### 用户申请加入
```bash
curl -X POST http://localhost:8080/api/groups/join \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "abc123def456",
    "message": "我是智能家居爱好者，希望加入学习交流"
  }'
```

#### 创建者查看申请列表
```bash
# 获取所有分组的加入申请
curl -X GET http://localhost:8080/api/groups/requests \
  -H "Authorization: Bearer <token>"

# 按状态过滤申请
curl -X GET "http://localhost:8080/api/groups/requests?status=pending" \
  -H "Authorization: Bearer <token>"
```

#### 创建者审核申请
```bash
# 批准申请
curl -X PUT http://localhost:8080/api/groups/requests/507f1f77bcf86cd799439017/review \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "approved"
  }'

# 拒绝申请
curl -X PUT http://localhost:8080/api/groups/requests/507f1f77bcf86cd799439017/review \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "rejected"
  }'
```

### 4. 权限管理

#### 设置Topic权限
```bash
# 给用户设置只读权限
curl -X POST http://localhost:8080/api/groups/abc123def456/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "507f1f77bcf86cd799439015",
    "full_topic": "abc123def456/living_room/temperature",
    "can_pub": false,
    "can_sub": true
  }'

# 给用户设置读写权限
curl -X POST http://localhost:8080/api/groups/abc123def456/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "507f1f77bcf86cd799439016",
    "full_topic": "abc123def456/living_room/temperature",
    "can_pub": true,
    "can_sub": true
  }'
```

## MQTT集成

### 认证机制
- **客户端ID**：使用用户的MongoDB ObjectID
- **用户名/密码**：可选，主要依赖客户端ID认证
- **连接验证**：检查用户状态是否为active

### 权限验证流程

1. **连接认证**
   ```
   Client ID: 507f1f77bcf86cd799439015
   → 验证用户ID格式（MongoDB ObjectID）
   → 查询用户状态
   → 允许/拒绝连接
   ```

2. **发布权限验证**
   ```
   Topic: abc123def456/living_room/temperature
   Action: publish
   → 查询Redis缓存
   → 缓存未命中时查询MongoDB
   → 检查can_pub权限
   → 允许/拒绝发布
   ```

3. **订阅权限验证**
   ```
   Topic: abc123def456/living_room/temperature
   Action: subscribe
   → 查询Redis缓存
   → 缓存未命中时查询MongoDB
   → 检查can_sub权限
   → 允许/拒绝订阅
   ```

### 缓存策略

#### Redis缓存键格式
```
topic_permission:{user_id}:{full_topic}
```

#### 缓存更新策略
- **权限设置时**：立即更新缓存
- **权限删除时**：立即清除缓存
- **Topic删除时**：清除相关所有权限缓存
- **用户移除时**：清除用户在该分组的所有权限缓存

#### 缓存过期策略
- **TTL**：1小时
- **LRU淘汰**：内存不足时自动淘汰最少使用的缓存

## 性能优化

### 数据库优化

1. **索引策略**
   - 权限验证核心索引：`user_id + full_topic`
   - 分页查询索引：`created_at`、`updated_at`
   - 业务查询索引：`group_id`、`creator_id`

2. **查询优化**
   - 使用复合索引避免回表查询
   - 分页查询使用limit+skip优化
   - 聚合查询使用pipeline优化

### 缓存优化

1. **预热策略**
   - 系统启动时预加载热点权限数据
   - 用户登录时预加载其权限信息

2. **更新策略**
   - 权限变更时立即更新缓存
   - 使用Redis Pipeline批量更新

### 应用层优化

1. **批量操作**
   - 支持批量设置权限
   - 批量删除权限

2. **异步处理**
   - 权限变更通知异步处理
   - 缓存更新异步执行

## 监控与运维

### 关键指标

1. **业务指标**
   - 分组数量
   - 活跃用户数
   - Topic数量
   - 权限验证QPS

2. **性能指标**
   - 权限验证延迟
   - 缓存命中率
   - 数据库查询延迟

3. **错误指标**
   - 权限验证失败率
   - API错误率
   - 缓存异常率

### 日志记录

```go
// 权限验证日志
log.Printf("权限验证: 用户=%s, Topic=%s, 动作=%s, 结果=%v", 
    userID, fullTopic, action, hasPermission)

// 分组操作日志
log.Printf("分组操作: 操作=%s, 分组=%s, 用户=%s", 
    operation, groupID, userID)
```

### 故障处理

1. **缓存故障**
   - 自动降级到数据库查询
   - 记录缓存异常日志
   - 监控缓存恢复状态

2. **数据库故障**
   - 使用缓存数据继续服务
   - 限制写操作
   - 快速故障转移

## 最佳实践

### 分组设计
- 按业务域划分分组
- 合理设置成员数量上限
- 使用有意义的分组名称和描述

### Topic设计
- 使用层次化Topic结构
- 避免过深的Topic层级
- 使用有意义的Topic名称

### 权限设计
- 遵循最小权限原则
- 定期审查权限设置
- 及时清理无效权限

### 性能优化
- 合理使用缓存
- 避免频繁的权限变更
- 监控系统性能指标
