# WireGuard API 文档

## 基础信息

- **Base URL**: `http://your-domain.com/api/v1/wireguard`
- **认证方式**: JWT Bearer Token
- **Content-Type**: `application/json`
- **权限控制**: 基于Casbin的RBAC权限系统

## 认证

所有API请求都需要在Header中包含JWT令牌：

```
Authorization: Bearer <your_jwt_token>
```

获取JWT令牌：

```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

## 通用响应格式

### 成功响应
```json
{
  "status": "success",
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

## 服务器管理 API

### 1. 创建WireGuard服务器

**端点**: `POST /servers`

**请求参数**:
```json
{
  "name": "string",        // 必填，服务器名称，1-50字符
  "dns": ["string"],       // 可选，DNS服务器列表，默认使用配置中的default_dns
  "mtu": 1420             // 可选，MTU值，范围1280-9000，默认1420
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "服务器创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "user_id": "507f1f77bcf86cd799439012",
    "name": "My VPN Server",
    "public_key": "xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=",
    "private_key_encrypted": "encrypted_private_key_data",
    "listen_port": 51820,
    "endpoint": "vpn.example.com:51820",
    "network": "********/24",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "enabled": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 获取服务器列表

**端点**: `GET /servers`

**查询参数**:
- `page`: 页码，默认1
- `limit`: 每页数量，默认10，最大100
- `enabled`: 过滤启用状态，true/false

**请求示例**:
```
GET /servers?page=1&limit=10&enabled=true
```

**响应示例**:
```json
{
  "status": "success",
  "message": "获取服务器列表成功",
  "data": {
    "servers": [
      {
        "id": "507f1f77bcf86cd799439011",
        "user_id": "507f1f77bcf86cd799439012",
        "name": "My VPN Server",
        "public_key": "xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=",
        "listen_port": 51820,
        "endpoint": "vpn.example.com:51820",
        "network": "********/24",
        "dns": ["*******", "*******"],
        "mtu": 1420,
        "enabled": true,
        "client_count": 3,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 3. 获取服务器详情

**端点**: `GET /servers/{id}`

**路径参数**:
- `id`: 服务器ID (MongoDB ObjectID)

**响应示例**:
```json
{
  "status": "success",
  "message": "获取服务器详情成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "user_id": "507f1f77bcf86cd799439012",
    "name": "My VPN Server",
    "public_key": "xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=",
    "private_key_encrypted": "encrypted_private_key_data",
    "listen_port": 51820,
    "endpoint": "vpn.example.com:51820",
    "network": "********/24",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "enabled": true,
    "clients": [
      {
        "id": "507f1f77bcf86cd799439013",
        "name": "Client 1",
        "public_key": "yUICA6rboUvnH4htodjb6e697QjS4PdgAaS+yUICA6r=",
        "allowed_ips": ["********/32"],
        "enabled": true
      }
    ],
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 4. 更新服务器

**端点**: `PUT /servers/{id}`

**路径参数**:
- `id`: 服务器ID

**请求参数**:
```json
{
  "name": "string",        // 可选，服务器名称
  "dns": ["string"],       // 可选，DNS服务器列表
  "mtu": 1420,            // 可选，MTU值
  "enabled": true         // 可选，启用状态
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "服务器更新成功",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "user_id": "507f1f77bcf86cd799439012",
    "name": "Updated Server Name",
    "public_key": "xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=",
    "listen_port": 51820,
    "endpoint": "vpn.example.com:51820",
    "network": "********/24",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "enabled": true,
    "updated_at": "2024-01-15T11:30:00Z"
  }
}
```

### 5. 删除服务器

**端点**: `DELETE /servers/{id}`

**路径参数**:
- `id`: 服务器ID

**响应示例**:
```json
{
  "status": "success",
  "message": "服务器删除成功",
  "data": {
    "deleted_server_id": "507f1f77bcf86cd799439011",
    "deleted_clients_count": 3
  }
}
```

## 客户端管理 API

### 1. 创建客户端

**端点**: `POST /clients`

**请求参数**:
```json
{
  "server_id": "string",   // 必填，服务器ID
  "name": "string",        // 必填，客户端名称，1-50字符
  "email": "string"        // 必填，客户端邮箱，用于标识
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "客户端创建成功",
  "data": {
    "id": "507f1f77bcf86cd799439013",
    "server_id": "507f1f77bcf86cd799439011",
    "user_id": "507f1f77bcf86cd799439012",
    "name": "My Client",
    "email": "<EMAIL>",
    "public_key": "zVJDA7rboUvnH4htodjb6e697QjS4PdgAaS+zVJDA7r=",
    "private_key_encrypted": "encrypted_private_key_data",
    "allowed_ips": ["********/32"],
    "endpoint": "vpn.example.com:51820",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "persistent_keepalive": 25,
    "enabled": true,
    "created_at": "2024-01-15T10:35:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
}

### 2. 获取客户端列表

**端点**: `GET /clients`

**查询参数**:
- `page`: 页码，默认1
- `limit`: 每页数量，默认10，最大100
- `server_id`: 过滤特定服务器的客户端
- `enabled`: 过滤启用状态，true/false

**请求示例**:
```
GET /clients?page=1&limit=10&server_id=507f1f77bcf86cd799439011&enabled=true
```

**响应示例**:
```json
{
  "status": "success",
  "message": "获取客户端列表成功",
  "data": {
    "clients": [
      {
        "id": "507f1f77bcf86cd799439013",
        "server_id": "507f1f77bcf86cd799439011",
        "server_name": "My VPN Server",
        "name": "My Client",
        "email": "<EMAIL>",
        "public_key": "zVJDA7rboUvnH4htodjb6e697QjS4PdgAaS+zVJDA7r=",
        "allowed_ips": ["********/32"],
        "enabled": true,
        "last_handshake": "2024-01-15T12:00:00Z",
        "transfer_rx": 1048576,
        "transfer_tx": 2097152,
        "created_at": "2024-01-15T10:35:00Z",
        "updated_at": "2024-01-15T10:35:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 3. 获取客户端详情

**端点**: `GET /clients/{id}`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "获取客户端详情成功",
  "data": {
    "id": "507f1f77bcf86cd799439013",
    "server_id": "507f1f77bcf86cd799439011",
    "server_name": "My VPN Server",
    "user_id": "507f1f77bcf86cd799439012",
    "name": "My Client",
    "email": "<EMAIL>",
    "public_key": "zVJDA7rboUvnH4htodjb6e697QjS4PdgAaS+zVJDA7r=",
    "private_key_encrypted": "encrypted_private_key_data",
    "allowed_ips": ["********/32"],
    "endpoint": "vpn.example.com:51820",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "persistent_keepalive": 25,
    "enabled": true,
    "last_handshake": "2024-01-15T12:00:00Z",
    "transfer_rx": 1048576,
    "transfer_tx": 2097152,
    "created_at": "2024-01-15T10:35:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
}
```

### 4. 更新客户端

**端点**: `PUT /clients/{id}`

**路径参数**:
- `id`: 客户端ID

**请求参数**:
```json
{
  "name": "string",        // 可选，客户端名称
  "email": "string",       // 可选，客户端邮箱
  "enabled": true         // 可选，启用状态
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "客户端更新成功",
  "data": {
    "id": "507f1f77bcf86cd799439013",
    "server_id": "507f1f77bcf86cd799439011",
    "name": "Updated Client Name",
    "email": "<EMAIL>",
    "public_key": "zVJDA7rboUvnH4htodjb6e697QjS4PdgAaS+zVJDA7r=",
    "allowed_ips": ["********/32"],
    "enabled": true,
    "updated_at": "2024-01-15T11:35:00Z"
  }
}
```

### 5. 删除客户端

**端点**: `DELETE /clients/{id}`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "客户端删除成功",
  "data": {
    "deleted_client_id": "507f1f77bcf86cd799439013"
  }
}
```

### 6. 启用客户端

**端点**: `POST /clients/{id}/enable`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "客户端启用成功",
  "data": {
    "id": "507f1f77bcf86cd799439013",
    "enabled": true,
    "updated_at": "2024-01-15T11:40:00Z"
  }
}
```

### 7. 禁用客户端

**端点**: `POST /clients/{id}/disable`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "客户端禁用成功",
  "data": {
    "id": "507f1f77bcf86cd799439013",
    "enabled": false,
    "updated_at": "2024-01-15T11:45:00Z"
  }
}

## 配置管理 API

### 1. 获取客户端配置文件

**端点**: `GET /configs/clients/{id}`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "获取客户端配置成功",
  "data": {
    "client_id": "507f1f77bcf86cd799439013",
    "config": "[Interface]\nPrivateKey = yAnz5TF+lXXK9YRZ2j4yiXqbPfHit4+fkwY9kxBdBmk=\nAddress = ********/32\nDNS = *******, *******\nMTU = 1420\n\n[Peer]\nPublicKey = xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=\nEndpoint = vpn.example.com:51820\nAllowedIPs = 0.0.0.0/0\nPersistentKeepalive = 25",
    "filename": "my-client.conf"
  }
}
```

### 2. 获取客户端QR码

**端点**: `GET /configs/clients/{id}/qr`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "获取QR码成功",
  "data": {
    "client_id": "507f1f77bcf86cd799439013",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEA...",
    "config": "[Interface]\nPrivateKey = yAnz5TF+lXXK9YRZ2j4yiXqbPfHit4+fkwY9kxBdBmk=\nAddress = ********/32\nDNS = *******, *******\nMTU = 1420\n\n[Peer]\nPublicKey = xTIBA5rboUvnH4htodjb6e697QjS4PdgAaS+xTIBA5r=\nEndpoint = vpn.example.com:51820\nAllowedIPs = 0.0.0.0/0\nPersistentKeepalive = 25"
  }
}
```

## 统计信息 API

### 1. 获取客户端统计信息

**端点**: `GET /stats/clients/{id}`

**路径参数**:
- `id`: 客户端ID

**响应示例**:
```json
{
  "status": "success",
  "message": "获取客户端统计成功",
  "data": {
    "client_id": "507f1f77bcf86cd799439013",
    "client_name": "My Client",
    "server_name": "My VPN Server",
    "public_key": "zVJDA7rboUvnH4htodjb6e697QjS4PdgAaS+zVJDA7r=",
    "endpoint": "*************:54321",
    "allowed_ips": ["********/32"],
    "latest_handshake": "2024-01-15T12:00:00Z",
    "transfer_rx": 1048576,
    "transfer_tx": 2097152,
    "persistent_keepalive_interval": 25,
    "protocol_version": 1,
    "connected": true,
    "connection_duration": "2h30m15s",
    "last_seen": "2024-01-15T12:00:00Z"
  }
}
```

## 错误代码说明

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `CONFLICT` | 409 | 资源冲突 |
| `VALIDATION_ERROR` | 422 | 数据验证失败 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

## 使用示例

### 完整工作流程示例

```bash
# 1. 登录获取令牌
TOKEN=$(curl -s -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  | jq -r '.data.access_token')

# 2. 创建WireGuard服务器
SERVER_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/wireguard/servers" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"My VPN Server","dns":["*******","*******"],"mtu":1420}')

SERVER_ID=$(echo $SERVER_RESPONSE | jq -r '.data.id')

# 3. 创建客户端
CLIENT_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/wireguard/clients" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"server_id\":\"$SERVER_ID\",\"name\":\"My Client\",\"email\":\"<EMAIL>\"}")

CLIENT_ID=$(echo $CLIENT_RESPONSE | jq -r '.data.id')

# 4. 获取客户端配置
curl -s -X GET "http://localhost:8080/api/v1/wireguard/configs/clients/$CLIENT_ID" \
  -H "Authorization: Bearer $TOKEN"

# 5. 获取QR码
curl -s -X GET "http://localhost:8080/api/v1/wireguard/configs/clients/$CLIENT_ID/qr" \
  -H "Authorization: Bearer $TOKEN"
```

## 注意事项

1. **权限控制**: 用户只能管理自己创建的服务器和客户端
2. **资源限制**:
   - 每个用户最多创建1个服务器（根据配置）
   - 每个服务器最多20个客户端（根据配置）
3. **数据加密**: 私钥在数据库中加密存储
4. **网络隔离**: 每个用户的服务器运行在独立的网络命名空间
5. **配置安全**: 配置文件包含敏感信息，请妥善保管

## 数据字段说明

### 服务器字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `id` | string | 服务器唯一标识符 |
| `user_id` | string | 所属用户ID |
| `name` | string | 服务器名称 |
| `public_key` | string | 服务器公钥 |
| `private_key_encrypted` | string | 加密的服务器私钥 |
| `listen_port` | integer | 监听端口 |
| `endpoint` | string | 服务器端点地址 |
| `network` | string | 分配的网络段 |
| `dns` | array | DNS服务器列表 |
| `mtu` | integer | 最大传输单元 |
| `enabled` | boolean | 是否启用 |
| `client_count` | integer | 客户端数量 |
| `created_at` | string | 创建时间 |
| `updated_at` | string | 更新时间 |

### 客户端字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `id` | string | 客户端唯一标识符 |
| `server_id` | string | 所属服务器ID |
| `user_id` | string | 所属用户ID |
| `name` | string | 客户端名称 |
| `email` | string | 客户端邮箱 |
| `public_key` | string | 客户端公钥 |
| `private_key_encrypted` | string | 加密的客户端私钥 |
| `allowed_ips` | array | 允许的IP地址 |
| `endpoint` | string | 服务器端点 |
| `dns` | array | DNS服务器 |
| `mtu` | integer | MTU值 |
| `persistent_keepalive` | integer | 持久连接间隔 |
| `enabled` | boolean | 是否启用 |
| `last_handshake` | string | 最后握手时间 |
| `transfer_rx` | integer | 接收字节数 |
| `transfer_tx` | integer | 发送字节数 |
| `created_at` | string | 创建时间 |
| `updated_at` | string | 更新时间 |
```
```
