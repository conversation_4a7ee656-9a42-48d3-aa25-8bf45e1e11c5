# WireGuard API 快速参考

## 🔐 认证
```bash
# 获取令牌
curl -X POST /api/auth/login \
  -d '{"email":"<EMAIL>","password":"password"}'

# 使用令牌
curl -H "Authorization: Bearer YOUR_TOKEN" /api/v1/wireguard/servers
```

## 🖥️ 服务器管理

| 操作 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 创建 | POST | `/servers` | 创建新的WireGuard服务器 |
| 列表 | GET | `/servers` | 获取服务器列表 |
| 详情 | GET | `/servers/{id}` | 获取服务器详细信息 |
| 更新 | PUT | `/servers/{id}` | 更新服务器配置 |
| 删除 | DELETE | `/servers/{id}` | 删除服务器 |

### 创建服务器
```bash
curl -X POST /api/v1/wireguard/servers \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My VPN Server",
    "dns": ["*******", "8.8.8.8"],
    "mtu": 1420
  }'
```

### 获取服务器列表
```bash
curl -X GET "/api/v1/wireguard/servers?page=1&limit=10&enabled=true" \
  -H "Authorization: Bearer TOKEN"
```

## 👤 客户端管理

| 操作 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 创建 | POST | `/clients` | 创建新客户端 |
| 列表 | GET | `/clients` | 获取客户端列表 |
| 详情 | GET | `/clients/{id}` | 获取客户端详情 |
| 更新 | PUT | `/clients/{id}` | 更新客户端信息 |
| 删除 | DELETE | `/clients/{id}` | 删除客户端 |
| 启用 | POST | `/clients/{id}/enable` | 启用客户端 |
| 禁用 | POST | `/clients/{id}/disable` | 禁用客户端 |

### 创建客户端
```bash
curl -X POST /api/v1/wireguard/clients \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "SERVER_ID",
    "name": "My Client",
    "email": "<EMAIL>"
  }'
```

### 获取客户端列表
```bash
curl -X GET "/api/v1/wireguard/clients?server_id=SERVER_ID&page=1&limit=10" \
  -H "Authorization: Bearer TOKEN"
```

## 📄 配置管理

| 操作 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 配置文件 | GET | `/configs/clients/{id}` | 获取客户端配置文件 |
| QR码 | GET | `/configs/clients/{id}/qr` | 获取客户端QR码 |

### 获取客户端配置
```bash
curl -X GET /api/v1/wireguard/configs/clients/CLIENT_ID \
  -H "Authorization: Bearer TOKEN"
```

### 获取QR码
```bash
curl -X GET /api/v1/wireguard/configs/clients/CLIENT_ID/qr \
  -H "Authorization: Bearer TOKEN"
```

## 📊 统计信息

| 操作 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 客户端统计 | GET | `/stats/clients/{id}` | 获取客户端连接统计 |

### 获取客户端统计
```bash
curl -X GET /api/v1/wireguard/stats/clients/CLIENT_ID \
  -H "Authorization: Bearer TOKEN"
```

## 📝 请求参数

### 创建服务器参数
```json
{
  "name": "string",        // 必填，服务器名称，1-50字符
  "dns": ["string"],       // 可选，DNS服务器列表
  "mtu": 1420             // 可选，MTU值，1280-9000
}
```

### 创建客户端参数
```json
{
  "server_id": "string",   // 必填，服务器ID
  "name": "string",        // 必填，客户端名称，1-50字符
  "email": "string"        // 必填，客户端邮箱
}
```

### 更新服务器参数
```json
{
  "name": "string",        // 可选，服务器名称
  "dns": ["string"],       // 可选，DNS服务器列表
  "mtu": 1420,            // 可选，MTU值
  "enabled": true         // 可选，启用状态
}
```

### 更新客户端参数
```json
{
  "name": "string",        // 可选，客户端名称
  "email": "string",       // 可选，客户端邮箱
  "enabled": true         // 可选，启用状态
}
```

## 🔍 查询参数

### 分页参数
- `page`: 页码，默认1
- `limit`: 每页数量，默认10，最大100

### 过滤参数
- `enabled`: 过滤启用状态，true/false
- `server_id`: 过滤特定服务器的客户端（仅客户端列表）

## 📋 响应格式

### 成功响应
```json
{
  "status": "success",
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

## ⚠️ 错误代码

| 代码 | HTTP状态 | 描述 |
|------|----------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `CONFLICT` | 409 | 资源冲突 |
| `VALIDATION_ERROR` | 422 | 数据验证失败 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 🚀 快速开始

### 1. 完整工作流程
```bash
# 1. 登录
TOKEN=$(curl -s -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  | jq -r '.data.access_token')

# 2. 创建服务器
SERVER_ID=$(curl -s -X POST "http://localhost:8080/api/v1/wireguard/servers" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"My VPN","dns":["*******"],"mtu":1420}' \
  | jq -r '.data.id')

# 3. 创建客户端
CLIENT_ID=$(curl -s -X POST "http://localhost:8080/api/v1/wireguard/clients" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"server_id\":\"$SERVER_ID\",\"name\":\"My Client\",\"email\":\"<EMAIL>\"}" \
  | jq -r '.data.id')

# 4. 获取配置
curl -s -X GET "http://localhost:8080/api/v1/wireguard/configs/clients/$CLIENT_ID" \
  -H "Authorization: Bearer $TOKEN"
```

### 2. 使用测试脚本
```bash
# 运行完整API测试
./scripts/api-test-examples.sh

# 运行基础功能测试
./scripts/test-wireguard.sh
```

### 3. 导入Postman集合
1. 打开Postman
2. 点击Import
3. 选择文件：`docs/WireGuard-API.postman_collection.json`
4. 设置环境变量：`base_url` = `http://localhost:8080`

## 📚 相关文档

- [完整API文档](./wireguard-api-documentation.md)
- [集成指南](./wireguard-integration-guide.md)
- [部署指南](./wireguard-deployment-guide.md)

## 💡 提示

1. **权限**: 用户只能管理自己创建的资源
2. **限制**: 每用户1个服务器，每服务器20个客户端
3. **安全**: 私钥加密存储，配置文件包含敏感信息
4. **网络**: 每用户独立网络命名空间隔离
