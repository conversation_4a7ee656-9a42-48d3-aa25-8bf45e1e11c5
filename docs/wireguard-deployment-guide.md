# WireGuard 管理系统部署指南

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间
- 网络: 公网IP地址

### 软件要求
- 操作系统: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- Go: 1.21+
- MongoDB: 4.4+
- Redis: 6.0+
- WireGuard: 最新版本

## 安装步骤

### 1. 安装系统依赖

#### Ubuntu/Debian
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 WireGuard
sudo apt install wireguard wireguard-tools -y

# 安装其他依赖
sudo apt install iptables iproute2 -y

# 启用 IP 转发
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### CentOS/RHEL
```bash
# 安装 EPEL 仓库
sudo dnf install epel-release -y

# 安装 WireGuard
sudo dnf install wireguard-tools -y

# 安装其他依赖
sudo dnf install iptables iproute -y

# 启用 IP 转发
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 2. 配置系统权限

```bash
# 创建 WireGuard 用户（可选）
sudo useradd -r -s /bin/false wireguard

# 设置必要的权限
sudo setcap cap_net_admin+ep /usr/bin/wg
sudo setcap cap_net_admin+ep /usr/bin/wg-quick

# 允许应用程序创建网络命名空间
sudo setcap cap_sys_admin,cap_net_admin+ep ./bin/server
```

### 3. 配置数据库

#### MongoDB
```bash
# 创建 WireGuard 数据库集合索引
mongo beacon --eval "
db.wg_servers.createIndex({'user_id': 1});
db.wg_servers.createIndex({'user_id': 1, '_id': 1});
db.wg_clients.createIndex({'user_id': 1});
db.wg_clients.createIndex({'server_id': 1});
db.wg_clients.createIndex({'user_id': 1, 'server_id': 1});
"
```

### 4. 配置应用程序

#### 更新配置文件
编辑 `configs/config.yaml`：

```yaml
# 确保 WireGuard 配置正确
wireguard:
  host: "your-server.com" # 重要：WireGuard服务器的公网域名或IP，客户端将连接到此地址
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 5
  max_clients_per_server: 100
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420
  encryption_key: "your-32-byte-encryption-key-here!!" # 请更改为32字节的密钥

# 更新主机配置
host: your-server.com # 替换为你的服务器域名或IP（用于API访问）
NetworkInterface: "eth0" # 替换为你的网络接口名称
```

#### 生成加密密钥
```bash
# 生成32字节的加密密钥
openssl rand -base64 32
```

#### 重要配置说明

**WireGuard Host 配置**
- `wireguard.host`: 这是客户端配置文件中endpoint参数的域名或IP地址
- 必须是客户端能够访问的公网地址
- 可以是域名（推荐）或公网IP地址
- 示例：`vpn.example.com` 或 `***********`

**配置区别说明**
- `host`: 用于API服务访问的地址
- `wireguard.host`: 用于WireGuard VPN连接的地址
- 通常这两个可以是同一个地址，但也可以分开配置

### 5. 配置防火墙

#### UFW (Ubuntu)
```bash
# 允许 WireGuard 端口范围
sudo ufw allow 51820:52820/udp

# 允许 API 端口
sudo ufw allow 8080/tcp

# 启用防火墙
sudo ufw enable
```

#### firewalld (CentOS)
```bash
# 允许 WireGuard 端口范围
sudo firewall-cmd --permanent --add-port=51820-52820/udp

# 允许 API 端口
sudo firewall-cmd --permanent --add-port=8080/tcp

# 重新加载防火墙
sudo firewall-cmd --reload
```

### 6. 编译和运行

```bash
# 编译应用程序
go build -o bin/server cmd/server/main.go

# 运行应用程序（需要 root 权限）
sudo ./bin/server
```

## 系统集成

### 1. 在现有项目中集成

在你的主应用程序中添加 WireGuard 路由：

```go
package main

import (
    "beacon/cloud/internal/routes"
    "beacon/cloud/pkg/wireguard"
    // ... 其他导入
)

func main() {
    // ... 现有的初始化代码

    // 初始化 WireGuard 管理器
    wgConfig := &wireguard.Config{
        BasePort:            config.WireGuard.BasePort,
        MaxUsers:            config.WireGuard.MaxUsers,
        MaxServersPerUser:   config.WireGuard.MaxServersPerUser,
        MaxClientsPerServer: config.WireGuard.MaxClientsPerServer,
        BaseNetwork:         config.WireGuard.BaseNetwork,
        DefaultDNS:          config.WireGuard.DefaultDNS,
        DefaultMTU:          config.WireGuard.DefaultMTU,
        EncryptionKey:       config.WireGuard.EncryptionKey,
    }

    wgManager, err := wireguard.NewWireGuardManager(db, redisClient, wgConfig)
    if err != nil {
        log.Fatal("Failed to initialize WireGuard manager:", err)
    }

    // 设置路由
    r := gin.Default()
    
    // ... 现有的路由设置
    
    // 添加 WireGuard 路由
    routes.SetupWireGuardRoutes(r, wgManager)

    // ... 启动服务器
}
```

### 2. 配置结构体更新

在你的配置结构体中添加 WireGuard 配置：

```go
type Config struct {
    // ... 现有字段
    
    WireGuard struct {
        BasePort            int      `yaml:"base_port"`
        MaxUsers            int      `yaml:"max_users"`
        MaxServersPerUser   int      `yaml:"max_servers_per_user"`
        MaxClientsPerServer int      `yaml:"max_clients_per_server"`
        BaseNetwork         string   `yaml:"base_network"`
        DefaultDNS          []string `yaml:"default_dns"`
        DefaultMTU          int      `yaml:"default_mtu"`
        EncryptionKey       string   `yaml:"encryption_key"`
    } `yaml:"wireguard"`
}
```

## API 使用示例

### 1. 创建 WireGuard 服务器

```bash
curl -X POST http://localhost:8080/api/v1/wireguard/servers \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My VPN Server",
    "dns": ["*******", "*******"],
    "mtu": 1420
  }'
```

**注意**: endpoint参数会自动从配置文件中的`wireguard.host`获取，无需在API请求中指定。

### 2. 创建客户端

```bash
curl -X POST http://localhost:8080/api/v1/wireguard/clients \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "60f1b2c3d4e5f6789abcdef0",
    "name": "My Phone",
    "allowed_ips": ["0.0.0.0/0"],
    "persistent_keepalive": 25
  }'
```

### 3. 下载客户端配置

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1 \
  -H "Authorization: Bearer your-jwt-token" \
  -o client.conf
```

### 4. 获取 QR 码

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1/qrcode \
  -H "Authorization: Bearer your-jwt-token" \
  -o qrcode.png
```

## 监控和维护

### 1. 日志监控

```bash
# 查看应用程序日志
sudo journalctl -u your-app-service -f

# 查看 WireGuard 日志
sudo dmesg | grep wireguard
```

### 2. 性能监控

```bash
# 查看网络接口状态
sudo wg show

# 查看网络命名空间
sudo ip netns list

# 查看网络流量
sudo iftop -i wg-user-12345678
```

### 3. 备份策略

```bash
# 备份 MongoDB 数据
mongodump --db beacon --collection wg_servers --out /backup/
mongodump --db beacon --collection wg_clients --out /backup/

# 备份配置文件
cp configs/config.yaml /backup/
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保应用程序有足够的权限
   sudo setcap cap_sys_admin,cap_net_admin+ep ./bin/server
   ```

2. **端口冲突**
   ```bash
   # 检查端口使用情况
   sudo netstat -tulpn | grep :51820
   ```

3. **网络命名空间问题**
   ```bash
   # 清理残留的命名空间
   sudo ip netns list
   sudo ip netns del ns-user-12345678
   ```

4. **防火墙问题**
   ```bash
   # 检查防火墙规则
   sudo iptables -L -n
   sudo ufw status
   ```

### 性能优化

1. **调整内核参数**
   ```bash
   echo 'net.core.rmem_max = 26214400' | sudo tee -a /etc/sysctl.conf
   echo 'net.core.rmem_default = 26214400' | sudo tee -a /etc/sysctl.conf
   sudo sysctl -p
   ```

2. **优化 MongoDB 索引**
   ```javascript
   // 在 MongoDB 中执行
   db.wg_clients.createIndex({"user_id": 1, "enabled": 1})
   db.wg_servers.createIndex({"user_id": 1, "enabled": 1})
   ```

## 安全注意事项

1. **定期更新加密密钥**
2. **限制 API 访问权限**
3. **监控异常连接**
4. **定期备份数据**
5. **使用 HTTPS 保护 API**

## 支持和联系

如有问题，请查看项目文档或提交 Issue。
