# WireGuard Host 配置说明

## 概述

WireGuard管理系统现在支持统一的host配置，用于自动设置客户端配置文件中的endpoint参数。这简化了配置管理，确保所有客户端都使用正确的服务器地址。

## 配置结构

### 配置文件 (config.yaml)

```yaml
# API服务器配置
host: wg.beaconglobaltech.com # API服务访问地址

# WireGuard 配置
wireguard:
  host: "wg.beaconglobaltech.com" # WireGuard VPN服务器地址（客户端连接地址）
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 5
  max_clients_per_server: 100
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420
  encryption_key: "your-32-byte-encryption-key-here!!"
```

### 配置说明

1. **`host`**: API服务器的访问地址
   - 用于HTTP API访问
   - 可以是内网地址或公网地址

2. **`wireguard.host`**: WireGuard VPN服务器地址
   - 客户端配置文件中的endpoint参数
   - 必须是客户端能够访问的公网地址
   - 推荐使用域名而不是IP地址

## API 变更

### 创建服务器 API

**之前的请求格式**:
```json
{
  "name": "My VPN Server",
  "endpoint": "vpn.example.com",  // 需要手动指定
  "dns": ["*******", "*******"],
  "mtu": 1420
}
```

**现在的请求格式**:
```json
{
  "name": "My VPN Server",
  // endpoint 自动从配置中获取
  "dns": ["*******", "*******"],
  "mtu": 1420
}
```

### 更新服务器 API

**之前的请求格式**:
```json
{
  "name": "Updated Server Name",
  "endpoint": "new-vpn.example.com",  // 可以更新endpoint
  "dns": ["*******", "*******"],
  "mtu": 1420,
  "enabled": true
}
```

**现在的请求格式**:
```json
{
  "name": "Updated Server Name",
  // endpoint 不能通过API更新，需要修改配置文件
  "dns": ["*******", "*******"],
  "mtu": 1420,
  "enabled": true
}
```

## 客户端配置生成

当生成客户端配置文件时，系统会自动使用 `wireguard.host` 配置作为endpoint：

```ini
[Interface]
PrivateKey = <client-private-key>
Address = ********/32
DNS = *******, *******
MTU = 1420

[Peer]
PublicKey = <server-public-key>
PresharedKey = <preshared-key>
Endpoint = wg.beaconglobaltech.com:51820  # 自动从配置获取
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
```

## 优势

1. **统一管理**: 所有服务器的endpoint都从配置文件统一管理
2. **简化API**: 创建服务器时不需要指定endpoint
3. **一致性**: 确保所有客户端使用相同的服务器地址
4. **易于维护**: 更改服务器地址只需修改配置文件

## 迁移指南

如果你已经在使用之前的版本，需要进行以下迁移：

### 1. 更新配置文件

在 `config.yaml` 中添加 `wireguard.host` 配置：

```yaml
wireguard:
  host: "your-server-domain.com"  # 添加这一行
  # ... 其他配置保持不变
```

### 2. 更新API调用

移除创建服务器API中的 `endpoint` 参数：

```bash
# 之前
curl -X POST /api/v1/wireguard/servers \
  -d '{"name": "Server", "endpoint": "vpn.com", "dns": ["*******"]}'

# 现在
curl -X POST /api/v1/wireguard/servers \
  -d '{"name": "Server", "dns": ["*******"]}'
```

### 3. 重新生成客户端配置

现有的客户端配置文件仍然有效，但建议重新生成以使用新的配置：

```bash
curl -X GET /api/v1/wireguard/configs/clients/{client_id} \
  -H "Authorization: Bearer your-token" \
  -o new-client.conf
```

## 故障排除

### 常见问题

1. **客户端无法连接**
   - 检查 `wireguard.host` 是否为客户端可访问的地址
   - 确认防火墙允许相应端口

2. **配置文件中endpoint错误**
   - 验证 `wireguard.host` 配置是否正确
   - 重新生成客户端配置文件

3. **API返回错误**
   - 确保移除了请求中的 `endpoint` 参数
   - 检查配置文件格式是否正确

### 验证配置

使用以下命令验证配置是否正确：

```bash
# 检查配置文件语法
go run examples/wireguard-integration.go --check-config

# 测试客户端配置生成
curl -X GET /api/v1/wireguard/configs/clients/{client_id}
```

## 安全注意事项

1. **域名解析**: 使用域名时确保DNS解析正确
2. **证书验证**: 如果使用HTTPS，确保证书有效
3. **网络访问**: 确保客户端能够访问配置的host地址
4. **端口开放**: 确保防火墙允许WireGuard端口范围

## 总结

新的host配置机制提供了更好的配置管理体验，简化了API使用，并确保了配置的一致性。建议所有用户迁移到新的配置方式以获得更好的使用体验。
