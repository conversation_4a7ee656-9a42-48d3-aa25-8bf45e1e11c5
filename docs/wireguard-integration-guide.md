# WireGuard 集成指南

## 概述

WireGuard VPN管理功能已成功集成到Beacon Cloud平台中。本指南将帮助你了解如何使用这些新功能。

## 🚀 快速开始

### 1. 配置文件设置

在 `configs/config.yaml` 中添加WireGuard配置：

```yaml
# WireGuard VPN 配置
wireguard:
  host: "your-server-domain.com"  # 重要：客户端连接的服务器地址
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 5
  max_clients_per_server: 100
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420
  encryption_key: "your-32-byte-encryption-key-here!!"
```

### 2. 启动服务器

```bash
# 编译项目
go build -o bin/server cmd/server/main.go

# 启动服务器
./bin/server -config configs/config.yaml
```

### 3. 测试功能

```bash
# 运行集成测试脚本
./scripts/test-wireguard.sh
```

## 📡 API 端点

### 认证

所有WireGuard API都需要JWT认证和适当的权限。

```bash
# 登录获取令牌
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

### 服务器管理

#### 创建服务器

```bash
curl -X POST http://localhost:8080/api/v1/wireguard/servers \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My VPN Server",
    "dns": ["*******", "*******"],
    "mtu": 1420
  }'
```

#### 获取服务器列表

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/servers \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 获取服务器详情

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/servers/{server_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 更新服务器

```bash
curl -X PUT http://localhost:8080/api/v1/wireguard/servers/{server_id} \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Server Name",
    "dns": ["*******", "*******"],
    "mtu": 1420,
    "enabled": true
  }'
```

#### 删除服务器

```bash
curl -X DELETE http://localhost:8080/api/v1/wireguard/servers/{server_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 客户端管理

#### 创建客户端

```bash
curl -X POST http://localhost:8080/api/v1/wireguard/clients \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "server_id_here",
    "name": "Client Name",
    "email": "<EMAIL>"
  }'
```

#### 获取客户端列表

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/clients \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 启用/禁用客户端

```bash
# 启用客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients/{client_id}/enable \
  -H "Authorization: Bearer YOUR_TOKEN"

# 禁用客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients/{client_id}/disable \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 配置管理

#### 获取客户端配置文件

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/{client_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 获取客户端QR码

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/{client_id}/qr \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 统计信息

#### 获取客户端统计

```bash
curl -X GET http://localhost:8080/api/v1/wireguard/stats/clients/{client_id} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 系统要求

### 软件依赖

- Go 1.19+
- MongoDB 4.4+
- Redis 6.0+
- WireGuard内核模块或wireguard-go

### 系统权限

WireGuard管理需要以下系统权限：

```bash
# 添加网络命名空间权限
sudo setcap cap_net_admin+ep /path/to/your/binary

# 或者以root权限运行（不推荐生产环境）
sudo ./bin/server -config configs/config.yaml
```

### 网络配置

确保防火墙允许WireGuard端口范围：

```bash
# 允许WireGuard端口范围（默认从51820开始）
sudo ufw allow 51820:51920/udp
```

## 🛠️ 开发和测试

### 运行测试

```bash
# 运行单元测试
go test ./pkg/wireguard/...

# 运行集成测试
go test ./test/integration/...

# 运行功能测试脚本
./scripts/test-wireguard.sh
```

### 开发环境设置

1. 安装WireGuard工具：
```bash
# Ubuntu/Debian
sudo apt install wireguard-tools

# CentOS/RHEL
sudo yum install wireguard-tools
```

2. 配置开发环境：
```bash
# 复制示例配置
cp configs/config.example.yaml configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

## 🔒 安全注意事项

1. **加密密钥**: 确保使用强随机的32字节加密密钥
2. **网络隔离**: 每个用户的WireGuard服务器运行在独立的网络命名空间中
3. **权限控制**: 所有API都通过JWT和Casbin进行权限控制
4. **数据加密**: 敏感数据在数据库中使用AES加密存储

## 📚 相关文档

- [WireGuard部署指南](./wireguard-deployment-guide.md)
- [WireGuard Host配置说明](./wireguard-host-configuration.md)
- [API文档](./api-documentation.md)

## 🐛 故障排除

### 常见问题

1. **权限错误**
   - 确保程序有网络管理权限
   - 检查WireGuard内核模块是否加载

2. **端口冲突**
   - 检查端口范围是否被其他服务占用
   - 调整base_port配置

3. **网络连接问题**
   - 验证host配置是否正确
   - 检查防火墙设置

4. **数据库连接**
   - 确保MongoDB和Redis服务正常运行
   - 检查连接字符串配置

### 日志查看

```bash
# 查看服务器日志
tail -f logs/server.log

# 查看WireGuard相关日志
journalctl -u wg-quick@*
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进WireGuard集成功能！

## 📄 许可证

本项目遵循与主项目相同的许可证。
