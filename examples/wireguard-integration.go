package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"beacon/cloud/internal/routes"
	"beacon/cloud/pkg/wireguard"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gopkg.in/yaml.v2"
)

// Config 应用程序配置
type Config struct {
	MongoDB struct {
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		DBName   string `yaml:"dbname"`
	} `yaml:"MongoDB"`

	Redis struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Password string `yaml:"password"`
		DB       int    `yaml:"db"`
	} `yaml:"Redis"`

	Server struct {
		Port         string `yaml:"port"`
		ReadTimeout  int    `yaml:"read_timeout"`
		WriteTimeout int    `yaml:"write_timeout"`
		IdleTimeout  int    `yaml:"idle_timeout"`
	} `yaml:"server"`

	Host             string `yaml:"host"`
	NetworkInterface string `yaml:"NetworkInterface"`

	WireGuard struct {
		Host                string   `yaml:"host"`
		BasePort            int      `yaml:"base_port"`
		MaxUsers            int      `yaml:"max_users"`
		MaxServersPerUser   int      `yaml:"max_servers_per_user"`
		MaxClientsPerServer int      `yaml:"max_clients_per_server"`
		BaseNetwork         string   `yaml:"base_network"`
		DefaultDNS          []string `yaml:"default_dns"`
		DefaultMTU          int      `yaml:"default_mtu"`
		EncryptionKey       string   `yaml:"encryption_key"`
	} `yaml:"wireguard"`
}

func main() {
	// 加载配置
	config, err := loadConfig("configs/config.yaml")
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化 MongoDB
	mongoClient, err := initMongoDB(config)
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}
	defer mongoClient.Disconnect(context.Background())

	// 初始化 Redis
	redisClient := initRedis(config)
	defer redisClient.Close()

	// 初始化 WireGuard 管理器
	wgConfig := &wireguard.Config{
		Host:                config.WireGuard.Host,
		BasePort:            config.WireGuard.BasePort,
		MaxUsers:            config.WireGuard.MaxUsers,
		MaxServersPerUser:   config.WireGuard.MaxServersPerUser,
		MaxClientsPerServer: config.WireGuard.MaxClientsPerServer,
		BaseNetwork:         config.WireGuard.BaseNetwork,
		DefaultDNS:          config.WireGuard.DefaultDNS,
		DefaultMTU:          config.WireGuard.DefaultMTU,
		EncryptionKey:       config.WireGuard.EncryptionKey,
	}

	db := mongoClient.Database(config.MongoDB.DBName)
	wgManager, err := wireguard.NewWireGuardManager(db, redisClient, wgConfig)
	if err != nil {
		log.Fatal("Failed to initialize WireGuard manager:", err)
	}

	// 设置 Gin 路由
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 健康检查端点
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 添加 WireGuard 路由
	routes.SetupWireGuardRoutes(r, wgManager)

	// 创建 HTTP 服务器
	srv := &http.Server{
		Addr:         ":" + config.Server.Port,
		Handler:      r,
		ReadTimeout:  time.Duration(config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(config.Server.IdleTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("Server starting on port %s", config.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}

// loadConfig 加载配置文件
func loadConfig(path string) (*Config, error) {
	config := &Config{}

	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	decoder := yaml.NewDecoder(file)
	if err := decoder.Decode(config); err != nil {
		return nil, err
	}

	return config, nil
}

// initMongoDB 初始化 MongoDB 连接
func initMongoDB(config *Config) (*mongo.Client, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	uri := "mongodb://" + config.MongoDB.Username + ":" + config.MongoDB.Password +
		"@" + config.MongoDB.Host + ":" + config.MongoDB.Port + "/" + config.MongoDB.DBName

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}

	// 测试连接
	if err := client.Ping(ctx, nil); err != nil {
		return nil, err
	}

	return client, nil
}

// initRedis 初始化 Redis 连接
func initRedis(config *Config) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.Redis.Host + ":" + config.Redis.Port,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatal("Failed to connect to Redis:", err)
	}

	return rdb
}

// 示例中间件：用户认证
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里应该实现你的认证逻辑
		// 例如验证 JWT token

		// 示例：从 header 获取用户ID
		userID := c.GetHeader("X-User-ID")
		if userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 将用户ID设置到上下文中
		c.Set("user_id", userID)
		c.Next()
	}
}

// 示例：如何在现有项目中集成 WireGuard 路由
func setupRoutesWithAuth(r *gin.Engine, wgManager wireguard.WireGuardManager) {
	// API v1 组
	v1 := r.Group("/api/v1")
	v1.Use(AuthMiddleware()) // 添加认证中间件

	// WireGuard 路由组
	wg := v1.Group("/wireguard")
	{
		// 服务器管理
		servers := wg.Group("/servers")
		{
			servers.POST("", func(c *gin.Context) {
				// 创建服务器的处理逻辑
			})
			servers.GET("", func(c *gin.Context) {
				// 获取服务器列表的处理逻辑
			})
			// ... 其他路由
		}

		// 客户端管理
		clients := wg.Group("/clients")
		{
			clients.POST("", func(c *gin.Context) {
				// 创建客户端的处理逻辑
			})
			clients.GET("", func(c *gin.Context) {
				// 获取客户端列表的处理逻辑
			})
			// ... 其他路由
		}
	}
}
