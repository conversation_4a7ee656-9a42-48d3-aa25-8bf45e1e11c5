package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构体
type Config struct {
	MongoDB          MongoDBConfig    `yaml:"MongoDB"`
	TaosDB           TaosDBConfig     `yaml:"TaosDB"`
	Redis            RedisConfig      `yaml:"Redis"`
	Mail             MailConfig       `yaml:"mail"`
	WeChat           WeChatConfig     `yaml:"wechat"`
	SMS              SMSConfig        `yaml:"sms"`
	JWTSecret        string           `yaml:"jwt_secret"`
	Host             string           `yaml:"host"`
	NetworkInterface string           `yaml:"NetworkInterface"`
	Server           ServerConfig     `yaml:"server"`
	MQTT             MQTTConfig       `yaml:"mqtt"`
	Alert            AlertConfig      `yaml:"alert"`
	Middleware       MiddlewareConfig `yaml:"middleware"`
	WireGuard        WireGuardConfig  `yaml:"wireguard"`
}

// MongoDBConfig MongoDB配置
type MongoDBConfig struct {
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	DBName   string `yaml:"dbname"`
}

// TaosDBConfig TDengine配置
type TaosDBConfig struct {
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	DBName   string `yaml:"dbname"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

// MailConfig 邮件配置
type MailConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

// WeChatConfig 微信配置
type WeChatConfig struct {
	AppID      string `yaml:"appid"`
	Secret     string `yaml:"secret"`
	TemplateID string `yaml:"template_id"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	SignName        string `yaml:"sign_name"`
	SMSCode         string `yaml:"sms_code"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         string `yaml:"port"`
	ReadTimeout  int    `yaml:"read_timeout"`
	WriteTimeout int    `yaml:"write_timeout"`
	IdleTimeout  int    `yaml:"idle_timeout"`
}

// MQTTConfig MQTT配置
type MQTTConfig struct {
	TCPPort       string `yaml:"tcp_port"`
	WebSocketPort string `yaml:"websocket_port"`
	MaxClients    int    `yaml:"max_clients"`
	BufferSize    int    `yaml:"buffer_size"`
}

// AlertConfig 预警系统配置
type AlertConfig struct {
	FrequencyLimit AlertFrequencyLimitConfig `yaml:"frequency_limit"`
}

// AlertFrequencyLimitConfig 预警频率限制配置
type AlertFrequencyLimitConfig struct {
	MaxCount   int `yaml:"max_count"`   // 最大触发次数
	TimeWindow int `yaml:"time_window"` // 时间窗口(秒)
}

// MiddlewareConfig 中间件配置
type MiddlewareConfig struct {
	CORS         CORSConfig         `yaml:"cors"`
	RateLimit    RateLimitConfig    `yaml:"rate_limit"`
	Logging      LoggingConfig      `yaml:"logging"`
	ErrorHandler ErrorHandlerConfig `yaml:"error_handler"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	Enabled          bool     `yaml:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers"`
	ExposedHeaders   []string `yaml:"exposed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
	MaxAge           int      `yaml:"max_age"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled   bool     `yaml:"enabled"`
	Rate      string   `yaml:"rate"`       // 例如: "100-M" (100 requests per minute)
	Burst     int      `yaml:"burst"`      // 突发请求数
	SkipPaths []string `yaml:"skip_paths"` // 跳过限流的路径
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Enabled      bool     `yaml:"enabled"`
	Format       string   `yaml:"format"`        // "json" 或 "text"
	Level        string   `yaml:"level"`         // debug, info, warn, error
	SkipPaths    []string `yaml:"skip_paths"`    // 跳过日志记录的路径
	RequestBody  bool     `yaml:"request_body"`  // 是否记录请求体
	ResponseBody bool     `yaml:"response_body"` // 是否记录响应体
}

// ErrorHandlerConfig 错误处理配置
type ErrorHandlerConfig struct {
	Enabled     bool `yaml:"enabled"`
	ShowDetails bool `yaml:"show_details"` // 是否显示详细错误信息
}

// WireGuardConfig WireGuard配置
type WireGuardConfig struct {
	Host                string   `yaml:"host"` // WireGuard服务器的公网域名或IP
	BasePort            int      `yaml:"base_port"`
	MaxUsers            int      `yaml:"max_users"`
	MaxServersPerUser   int      `yaml:"max_servers_per_user"`
	MaxClientsPerServer int      `yaml:"max_clients_per_server"`
	BaseNetwork         string   `yaml:"base_network"`
	DefaultDNS          []string `yaml:"default_dns"`
	DefaultMTU          int      `yaml:"default_mtu"`
	EncryptionKey       string   `yaml:"encryption_key"`
}

var (
	// AppConfig 全局配置实例
	AppConfig *Config
)

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = getDefaultConfigPath()
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaultValues(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	AppConfig = &config
	return &config, nil
}

// getDefaultConfigPath 获取默认配置文件路径
func getDefaultConfigPath() string {
	// 优先从环境变量获取
	if path := os.Getenv("CONFIG_PATH"); path != "" {
		return path
	}

	// 默认路径
	return filepath.Join("configs", "config.yaml")
}

// setDefaultValues 设置默认配置值
func setDefaultValues(config *Config) {
	if config.Server.Port == "" {
		config.Server.Port = "8080"
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 60
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 60
	}
	if config.Server.IdleTimeout == 0 {
		config.Server.IdleTimeout = 120
	}

	// 设置MQTT默认值
	setMQTTDefaults(&config.MQTT)

	// 设置预警系统默认值
	setAlertDefaults(&config.Alert)

	// 设置中间件默认值
	setMiddlewareDefaults(&config.Middleware)
}

// setMiddlewareDefaults 设置中间件默认配置值
func setMiddlewareDefaults(middleware *MiddlewareConfig) {
	// CORS默认配置
	if len(middleware.CORS.AllowedOrigins) == 0 {
		middleware.CORS.Enabled = true
		middleware.CORS.AllowedOrigins = []string{"*"}
		middleware.CORS.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"}
		middleware.CORS.AllowedHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"}
		middleware.CORS.ExposedHeaders = []string{"Content-Length"}
		middleware.CORS.AllowCredentials = true
		middleware.CORS.MaxAge = 86400 // 24 hours
	}

	// 限流默认配置
	if middleware.RateLimit.Rate == "" {
		middleware.RateLimit.Enabled = true
		middleware.RateLimit.Rate = "100-M" // 100 requests per minute
		middleware.RateLimit.Burst = 10
		middleware.RateLimit.SkipPaths = []string{"/health", "/metrics"}
	}

	// 日志默认配置
	if middleware.Logging.Format == "" {
		middleware.Logging.Enabled = true
		middleware.Logging.Format = "json"
		middleware.Logging.Level = "info"
		middleware.Logging.SkipPaths = []string{"/health", "/metrics", "/favicon.ico"}
		middleware.Logging.RequestBody = false
		middleware.Logging.ResponseBody = false
	}

	// 错误处理默认配置
	middleware.ErrorHandler.Enabled = true
	middleware.ErrorHandler.ShowDetails = false // 生产环境不显示详细错误
}

// setMQTTDefaults 设置MQTT默认配置值
func setMQTTDefaults(mqtt *MQTTConfig) {
	if mqtt.TCPPort == "" {
		mqtt.TCPPort = DefaultMQTTTCPPort
	}
	if mqtt.WebSocketPort == "" {
		mqtt.WebSocketPort = DefaultMQTTWebSocketPort
	}
	if mqtt.MaxClients == 0 {
		mqtt.MaxClients = DefaultMaxClients
	}
	if mqtt.BufferSize == 0 {
		mqtt.BufferSize = DefaultBufferSize
	}
}

// setAlertDefaults 设置预警系统默认配置值
func setAlertDefaults(alert *AlertConfig) {
	if alert.FrequencyLimit.MaxCount == 0 {
		alert.FrequencyLimit.MaxCount = DefaultAlertMaxCount
	}
	if alert.FrequencyLimit.TimeWindow == 0 {
		alert.FrequencyLimit.TimeWindow = DefaultAlertTimeWindow
	}
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	// MongoDB
	if host := os.Getenv("MONGODB_HOST"); host != "" {
		config.MongoDB.Host = host
	}
	if port := os.Getenv("MONGODB_PORT"); port != "" {
		config.MongoDB.Port = port
	}
	if username := os.Getenv("MONGODB_USERNAME"); username != "" {
		config.MongoDB.Username = username
	}
	if password := os.Getenv("MONGODB_PASSWORD"); password != "" {
		config.MongoDB.Password = password
	}
	if dbname := os.Getenv("MONGODB_DBNAME"); dbname != "" {
		config.MongoDB.DBName = dbname
	}

	// Redis
	if host := os.Getenv("REDIS_HOST"); host != "" {
		config.Redis.Host = host
	}
	if port := os.Getenv("REDIS_PORT"); port != "" {
		config.Redis.Port = port
	}
	if password := os.Getenv("REDIS_PASSWORD"); password != "" {
		config.Redis.Password = password
	}

	// TaosDB
	if host := os.Getenv("TAOSDB_HOST"); host != "" {
		config.TaosDB.Host = host
	}
	if port := os.Getenv("TAOSDB_PORT"); port != "" {
		config.TaosDB.Port = port
	}
	if username := os.Getenv("TAOSDB_USERNAME"); username != "" {
		config.TaosDB.Username = username
	}
	if password := os.Getenv("TAOSDB_PASSWORD"); password != "" {
		config.TaosDB.Password = password
	}

	// JWT Secret
	if secret := os.Getenv("JWT_SECRET"); secret != "" {
		config.JWTSecret = secret
	}

	// Server
	if port := os.Getenv("SERVER_PORT"); port != "" {
		config.Server.Port = port
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证必需的配置项
	if config.MongoDB.Host == "" {
		return fmt.Errorf("MongoDB主机地址不能为空")
	}
	if config.MongoDB.Port == "" {
		return fmt.Errorf("MongoDB端口不能为空")
	}
	if config.MongoDB.DBName == "" {
		return fmt.Errorf("MongoDB数据库名不能为空")
	}

	if config.Redis.Host == "" {
		return fmt.Errorf("Redis主机地址不能为空")
	}
	if config.Redis.Port == "" {
		return fmt.Errorf("Redis端口不能为空")
	}

	if config.TaosDB.Host == "" {
		return fmt.Errorf("TaosDB主机地址不能为空")
	}
	if config.TaosDB.Port == "" {
		return fmt.Errorf("TaosDB端口不能为空")
	}

	if config.JWTSecret == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}

	return nil
}

// GetMongoDBConnectionString 获取MongoDB连接字符串
func (c *Config) GetMongoDBConnectionString() string {
	if c.MongoDB.Username != "" && c.MongoDB.Password != "" {
		return fmt.Sprintf("mongodb://%s:%s@%s:%s/%s",
			c.MongoDB.Username, c.MongoDB.Password,
			c.MongoDB.Host, c.MongoDB.Port, c.MongoDB.DBName)
	}
	return fmt.Sprintf("mongodb://%s:%s/%s",
		c.MongoDB.Host, c.MongoDB.Port, c.MongoDB.DBName)
}

// GetRedisConnectionString 获取Redis连接字符串
func (c *Config) GetRedisConnectionString() string {
	return fmt.Sprintf("%s:%s", c.Redis.Host, c.Redis.Port)
}

// GetTaosDBConnectionString 获取TaosDB连接字符串
func (c *Config) GetTaosDBConnectionString() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s",
		c.TaosDB.Username, c.TaosDB.Password,
		c.TaosDB.Host, c.TaosDB.Port, c.TaosDB.DBName)
}
