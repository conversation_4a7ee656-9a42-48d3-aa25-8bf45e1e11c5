package config

import "time"

// AppStartTime 应用启动时间
var AppStartTime = time.Now()

// 应用常量定义
const (
	// 应用信息
	AppName    = "Beacon MQTT Cloud"
	AppVersion = "1.0.0"

	// 默认配置
	DefaultServerPort = "8080"
	DefaultTimeout    = 30 * time.Second

	// 数据库相关
	DefaultMongoDBPort = "27017"
	DefaultRedisPort   = "6379"
	DefaultTaosDBPort  = "6041"

	// MQTT相关
	DefaultMQTTTCPPort       = "1883"
	DefaultMQTTWebSocketPort = "8083"
	DefaultMaxClients        = 1000
	DefaultBufferSize        = 1024

	// 预警系统相关
	DefaultAlertMaxCount   = 3    // 默认最大触发次数
	DefaultAlertTimeWindow = 3600 // 默认时间窗口(秒) - 1小时

	// JWT相关
	DefaultJWTExpiration     = 24 * time.Hour
	DefaultRefreshExpiration = 7 * 24 * time.Hour

	// 缓存相关
	DefaultCacheExpiration = 1 * time.Hour
	DefaultCleanupInterval = 10 * time.Minute

	// 分页相关
	DefaultPageSize = 20
	MaxPageSize     = 100

	// 文件上传相关
	MaxFileSize = 10 << 20 // 10MB

	// 邮件相关
	DefaultSMTPPort    = "587"
	DefaultSMTPSSLPort = "465"
)

// 用户角色常量
const (
	RoleAdmin = "admin"
	RoleUser  = "user"
)

// 分组权限常量
const (
	PermissionOwner  = "owner"  // 分组创建者
	PermissionMember = "member" // 分组成员
)

// MQTT权限常量
const (
	MQTTPermissionPublish   = "publish"   // 发布权限
	MQTTPermissionSubscribe = "subscribe" // 订阅权限
	MQTTPermissionBoth      = "both"      // 发布和订阅权限
)

// 数据库集合名称
const (
	CollectionUsers        = "users"
	CollectionGroups       = "groups"
	CollectionGroupMembers = "group_members"
	CollectionTopics       = "topics"
	CollectionMQTTConfig   = "mqtt_config"
	CollectionSystemConfig = "system_config"
)

// TDengine表名称
const (
	TableMQTTMessages = "mqtt_messages"
	TableMQTTStats    = "mqtt_stats"
)

// Redis键前缀
const (
	RedisKeyUserSession     = "session:user:"
	RedisKeyUserToken       = "token:user:"
	RedisKeyGroupCache      = "cache:group:"
	RedisKeyTopicCache      = "cache:topic:"
	RedisKeyMQTTClientAuth  = "mqtt:auth:"
	RedisKeyMQTTTopicPerm   = "mqtt:topic_perm:"
	RedisKeyMQTTUserGroups  = "mqtt:user_groups:"
	RedisKeyMQTTGroupTopics = "mqtt:group_topics:"
	// 新增高并发优化缓存键
	RedisKeyMQTTAdminPerm  = "mqtt:admin_perm:"  // 管理员权限缓存
	RedisKeyMQTTTopicInfo  = "mqtt:topic_info:"  // 主题信息缓存
	RedisKeyMQTTGroupPerms = "mqtt:group_perms:" // 分组权限缓存
	RedisKeyMQTTUserPerms  = "mqtt:user_perms:"  // 用户权限汇总缓存
	RedisKeyEmailCode      = "email:code:"
	RedisKeySMSCode        = "sms:code:"
)

// HTTP状态码
const (
	StatusSuccess             = 200
	StatusCreated             = 201
	StatusBadRequest          = 400
	StatusUnauthorized        = 401
	StatusForbidden           = 403
	StatusNotFound            = 404
	StatusConflict            = 409
	StatusInternalServerError = 500
)

// 错误码定义
const (
	ErrCodeSuccess       = 0
	ErrCodeInvalidParams = 1001
	ErrCodeUnauthorized  = 1002
	ErrCodeForbidden     = 1003
	ErrCodeNotFound      = 1004
	ErrCodeConflict      = 1005
	ErrCodeInternalError = 1006
	ErrCodeDatabaseError = 1007
	ErrCodeCacheError    = 1008
	ErrCodeMQTTError     = 1009
	ErrCodeEmailError    = 1010
	ErrCodeSMSError      = 1011
)

// 错误消息
var ErrorMessages = map[int]string{
	ErrCodeSuccess:       "操作成功",
	ErrCodeInvalidParams: "参数错误",
	ErrCodeUnauthorized:  "未授权访问",
	ErrCodeForbidden:     "权限不足",
	ErrCodeNotFound:      "资源不存在",
	ErrCodeConflict:      "资源冲突",
	ErrCodeInternalError: "内部服务器错误",
	ErrCodeDatabaseError: "数据库错误",
	ErrCodeCacheError:    "缓存错误",
	ErrCodeMQTTError:     "MQTT服务错误",
	ErrCodeEmailError:    "邮件发送错误",
	ErrCodeSMSError:      "短信发送错误",
}

// 日志级别
const (
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
)

// 环境变量
const (
	EnvProduction  = "production"
	EnvDevelopment = "development"
	EnvTesting     = "testing"
)

// 时间格式
const (
	TimeFormatDefault = "2006-01-02 15:04:05"
	TimeFormatDate    = "2006-01-02"
	TimeFormatTime    = "15:04:05"
	TimeFormatISO     = "2006-01-02T15:04:05Z07:00"
)

// 正则表达式模式
const (
	RegexEmail    = `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	RegexPhone    = `^1[3-9]\d{9}$`
	RegexPassword = `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$`
	RegexUsername = `^[a-zA-Z0-9_]{3,20}$`
)

// MQTT Topic模式
const (
	TopicPatternUserData   = "user/%s/data/%s"  // user/{user_id}/data/{device_id}
	TopicPatternGroupData  = "group/%s/data/%s" // group/{group_id}/data/{device_id}
	TopicPatternSystemData = "system/data/%s"   // system/data/{type}
)
