package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
)

// EnvConfig 环境变量配置
type EnvConfig struct {
	Environment string
	Debug       bool
	LogLevel    string
}

// LoadEnvConfig 加载环境变量配置
func LoadEnvConfig() *EnvConfig {
	return &EnvConfig{
		Environment: getEnvString("APP_ENV", EnvDevelopment),
		Debug:       getEnvBool("DEBUG", true),
		LogLevel:    getEnvString("LOG_LEVEL", LogLevelInfo),
	}
}

// getEnvString 获取字符串类型的环境变量
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数类型的环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔类型的环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// getEnvFloat 获取浮点数类型的环境变量
func getEnvFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

// getEnvStringSlice 获取字符串切片类型的环境变量（逗号分隔）
func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// IsProduction 判断是否为生产环境
func (e *EnvConfig) IsProduction() bool {
	return e.Environment == EnvProduction
}

// IsDevelopment 判断是否为开发环境
func (e *EnvConfig) IsDevelopment() bool {
	return e.Environment == EnvDevelopment
}

// IsTesting 判断是否为测试环境
func (e *EnvConfig) IsTesting() bool {
	return e.Environment == EnvTesting
}

// SetEnvDefaults 设置环境变量默认值
func SetEnvDefaults() {
	envDefaults := map[string]string{
		"APP_ENV":   EnvDevelopment,
		"DEBUG":     "true",
		"LOG_LEVEL": LogLevelInfo,

		// 数据库相关 - 只设置端口默认值，主机地址由配置文件决定
		"MONGODB_PORT": DefaultMongoDBPort,
		"REDIS_PORT":   DefaultRedisPort,
		"TAOSDB_PORT":  DefaultTaosDBPort,

		// 服务器相关
		"SERVER_PORT": DefaultServerPort,
		"SERVER_HOST": "0.0.0.0",

		// MQTT相关
		"MQTT_TCP_PORT":       DefaultMQTTTCPPort,
		"MQTT_WEBSOCKET_PORT": DefaultMQTTWebSocketPort,
		"MQTT_MAX_CLIENTS":    strconv.Itoa(DefaultMaxClients),
		"MQTT_BUFFER_SIZE":    strconv.Itoa(DefaultBufferSize),

		// JWT相关
		"JWT_EXPIRATION":         "24h",
		"JWT_REFRESH_EXPIRATION": "168h", // 7 days

		// 缓存相关
		"CACHE_EXPIRATION":       "1h",
		"CACHE_CLEANUP_INTERVAL": "10m",

		// 邮件相关
		"EMAIL_ENABLED": "true",
		"SMS_ENABLED":   "true",
	}

	for key, value := range envDefaults {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

// ValidateEnv 验证环境变量
func ValidateEnv() error {
	requiredEnvs := []string{
		"MONGODB_HOST",
		"MONGODB_PORT",
		"REDIS_HOST",
		"REDIS_PORT",
		"TAOSDB_HOST",
		"TAOSDB_PORT",
	}

	var missingEnvs []string
	for _, env := range requiredEnvs {
		if os.Getenv(env) == "" {
			missingEnvs = append(missingEnvs, env)
		}
	}

	if len(missingEnvs) > 0 {
		return fmt.Errorf("缺少必需的环境变量: %s", strings.Join(missingEnvs, ", "))
	}

	return nil
}

// ValidateConfig 验证配置完整性
func ValidateConfig(cfg *Config) error {
	if cfg == nil {
		return fmt.Errorf("配置对象为空")
	}

	// 验证JWT密钥
	if cfg.JWTSecret == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}

	// 验证数据库配置
	if cfg.MongoDB.Host == "" || cfg.MongoDB.Port == "" {
		return fmt.Errorf("MongoDB配置不完整")
	}

	if cfg.Redis.Host == "" || cfg.Redis.Port == "" {
		return fmt.Errorf("Redis配置不完整")
	}

	if cfg.TaosDB.Host == "" || cfg.TaosDB.Port == "" {
		return fmt.Errorf("TaosDB配置不完整")
	}

	return nil
}

// GetDatabaseConfig 从环境变量获取数据库配置
func GetDatabaseConfig() map[string]interface{} {
	return map[string]interface{}{
		"mongodb": map[string]string{
			"host":     getEnvString("MONGODB_HOST", "127.0.0.1"),
			"port":     getEnvString("MONGODB_PORT", DefaultMongoDBPort),
			"username": getEnvString("MONGODB_USERNAME", ""),
			"password": getEnvString("MONGODB_PASSWORD", ""),
			"dbname":   getEnvString("MONGODB_DBNAME", "beacon"),
		},
		"redis": map[string]interface{}{
			"host":     getEnvString("REDIS_HOST", "127.0.0.1"),
			"port":     getEnvString("REDIS_PORT", DefaultRedisPort),
			"password": getEnvString("REDIS_PASSWORD", ""),
			"db":       getEnvInt("REDIS_DB", 0),
		},
		"taosdb": map[string]string{
			"host":     getEnvString("TAOSDB_HOST", "127.0.0.1"),
			"port":     getEnvString("TAOSDB_PORT", DefaultTaosDBPort),
			"username": getEnvString("TAOSDB_USERNAME", "root"),
			"password": getEnvString("TAOSDB_PASSWORD", "taosdata"),
			"dbname":   getEnvString("TAOSDB_DBNAME", "beacon"),
		},
	}
}

// GetServerConfig 从环境变量获取服务器配置
func GetServerConfig() map[string]interface{} {
	return map[string]interface{}{
		"host":          getEnvString("SERVER_HOST", "0.0.0.0"),
		"port":          getEnvString("SERVER_PORT", DefaultServerPort),
		"read_timeout":  getEnvInt("SERVER_READ_TIMEOUT", 60),
		"write_timeout": getEnvInt("SERVER_WRITE_TIMEOUT", 60),
		"idle_timeout":  getEnvInt("SERVER_IDLE_TIMEOUT", 120),
	}
}

// GetMQTTConfig 从环境变量获取MQTT配置
func GetMQTTConfig() map[string]interface{} {
	return map[string]interface{}{
		"tcp_port":       getEnvString("MQTT_TCP_PORT", DefaultMQTTTCPPort),
		"websocket_port": getEnvString("MQTT_WEBSOCKET_PORT", DefaultMQTTWebSocketPort),
		"max_clients":    getEnvInt("MQTT_MAX_CLIENTS", DefaultMaxClients),
		"buffer_size":    getEnvInt("MQTT_BUFFER_SIZE", DefaultBufferSize),
	}
}
