package handler

import (
	"net/http"
	"strconv"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/service"

	"github.com/gin-gonic/gin"
)

// AlertHandler 预警处理器
type AlertHandler struct {
	alertService service.AlertService
}

// NewAlertHandler 创建预警处理器
func NewAlertHandler(alertService service.AlertService) *AlertHandler {
	return &AlertHandler{
		alertService: alertService,
	}
}

// CreateAlertRule 创建预警规则
func (h *AlertHandler) CreateAlertRule(c *gin.Context) {
	var req model.CreateAlertRuleRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.J<PERSON>(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	rule, err := h.alertService.CreateAlertRule(c.Request.Context(), userID.(string), &req)
	if err != nil {
		switch err {
		case model.ErrAlertRuleExists:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeInvalidParams,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "创建预警规则失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "创建预警规则成功",
		"data":    rule.ToResponse(),
	})
}

// GetAlertRule 获取预警规则详情
func (h *AlertHandler) GetAlertRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "预警规则ID不能为空",
		})
		return
	}

	rule, err := h.alertService.GetAlertRule(c.Request.Context(), ruleID)
	if err != nil {
		if err == model.ErrAlertRuleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "获取预警规则失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取预警规则成功",
		"data":    rule,
	})
}

// UpdateAlertRule 更新预警规则
func (h *AlertHandler) UpdateAlertRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "预警规则ID不能为空",
		})
		return
	}

	var req model.UpdateAlertRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	rule, err := h.alertService.UpdateAlertRule(c.Request.Context(), userID.(string), ruleID, &req)
	if err != nil {
		switch err {
		case model.ErrAlertRuleNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "更新预警规则失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "更新预警规则成功",
		"data":    rule.ToResponse(),
	})
}

// DeleteAlertRule 删除预警规则
func (h *AlertHandler) DeleteAlertRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "预警规则ID不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.alertService.DeleteAlertRule(c.Request.Context(), userID.(string), ruleID)
	if err != nil {
		switch err {
		case model.ErrAlertRuleNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "删除预警规则失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "删除预警规则成功",
	})
}

// ListAlertRules 获取预警规则列表
func (h *AlertHandler) ListAlertRules(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	filter := repository.AlertRuleFilter{
		GroupID:  c.Query("group_id"),
		Topic:    c.Query("topic"),
		RuleType: model.AlertRuleType(c.Query("rule_type")),
		Search:   c.Query("search"),
	}

	// 解析enabled参数
	if enabledStr := c.Query("enabled"); enabledStr != "" {
		if enabled, err := strconv.ParseBool(enabledStr); err == nil {
			filter.Enabled = &enabled
		}
	}

	// 解析level参数
	if levelStr := c.Query("level"); levelStr != "" {
		if level, err := strconv.Atoi(levelStr); err == nil {
			filter.Level = model.AlertLevel(level)
		}
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	rules, total, err := h.alertService.ListAlertRules(c.Request.Context(), filter, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取预警规则列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取预警规则列表成功",
		"data": gin.H{
			"rules": rules,
			"total": total,
			"page":  page,
			"size":  pageSize,
		},
	})
}

// TestAlertRule 测试预警规则
func (h *AlertHandler) TestAlertRule(c *gin.Context) {
	ruleID := c.Param("ruleId")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "预警规则ID不能为空",
		})
		return
	}

	var testData map[string]interface{}
	if err := c.ShouldBindJSON(&testData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "测试数据格式错误: " + err.Error(),
		})
		return
	}

	triggered, message, err := h.alertService.TestAlertRule(c.Request.Context(), ruleID, testData)
	if err != nil {
		if err == model.ErrAlertRuleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "测试预警规则失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "测试预警规则成功",
		"data": gin.H{
			"triggered": triggered,
			"message":   message,
		},
	})
}

// GetAlertRecords 获取预警记录列表
func (h *AlertHandler) GetAlertRecords(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	query := model.AlertRecordQuery{
		GroupID:  c.Query("group_id"),
		Topic:    c.Query("topic"),
		RuleID:   c.Query("rule_id"),
		Page:     page,
		PageSize: pageSize,
	}

	// 解析level参数
	if levelStr := c.Query("level"); levelStr != "" {
		if level, err := strconv.Atoi(levelStr); err == nil {
			query.Level = model.AlertLevel(level)
		}
	}

	// 解析时间参数
	if timeRangeStr := c.Query("time_range"); timeRangeStr != "" {
		query.TimeRange = timeRangeStr
	}

	records, total, err := h.alertService.GetAlertRecords(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取预警记录失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取预警记录成功",
		"data": gin.H{
			"records": records,
			"total":   total,
			"page":    page,
			"size":    pageSize,
		},
	})
}
