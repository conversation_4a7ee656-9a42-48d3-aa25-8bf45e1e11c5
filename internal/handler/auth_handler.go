package handler

import (
	"beacon/cloud/internal/config"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/auth"
	"net/http"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *service.AuthService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *service.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 刷新令牌
	tokenPair, err := h.authService.RefreshTokens(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "刷新令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "令牌刷新成功",
		"data":    tokenPair,
	})
}

// RevokeTokenRequest 撤销令牌请求
type RevokeTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// RevokeToken 撤销令牌
func (h *AuthHandler) RevokeToken(c *gin.Context) {
	var req RevokeTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 撤销令牌
	err := h.authService.RevokeToken(req.Token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "撤销令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "令牌撤销成功",
	})
}

// RevokeAllTokens 撤销用户的所有令牌
func (h *AuthHandler) RevokeAllTokens(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	// 撤销用户的所有令牌
	err := h.authService.RevokeAllUserTokens(userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "撤销所有令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "所有令牌撤销成功",
	})
}

// ValidateToken 验证令牌
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// 从Authorization头获取令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "未提供认证令牌",
		})
		return
	}

	// 提取令牌
	tokenString, err := auth.ExtractTokenFromHeader(authHeader)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": err.Error(),
		})
		return
	}

	// 验证令牌
	claims, err := h.authService.ValidateTokenWithBlacklist(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "令牌验证失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "令牌验证成功",
		"data": gin.H{
			"user_id":    claims.UserID,
			"username":   claims.Username,
			"role":       claims.Role,
			"token_type": claims.TokenType,
			"expires_at": claims.ExpiresAt.Time,
			"issued_at":  claims.IssuedAt.Time,
		},
	})
}

// GetUserInfo 获取当前用户信息
func (h *AuthHandler) GetUserInfo(c *gin.Context) {
	// 从上下文获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	username, _ := c.Get("username")
	role, _ := c.Get("role")
	claims, _ := c.Get("claims")

	userClaims := claims.(*auth.Claims)

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取用户信息成功",
		"data": gin.H{
			"user_id":    userID,
			"username":   username,
			"role":       role,
			"expires_at": userClaims.ExpiresAt.Time,
			"issued_at":  userClaims.IssuedAt.Time,
		},
	})
}

// CheckTokenStatus 检查令牌状态
func (h *AuthHandler) CheckTokenStatus(c *gin.Context) {
	// 从Authorization头获取令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "未提供认证令牌",
		})
		return
	}

	// 提取令牌
	tokenString, err := auth.ExtractTokenFromHeader(authHeader)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": err.Error(),
		})
		return
	}

	// 获取令牌声明（不验证过期时间）
	claims, err := h.authService.GetJWTManager().GetTokenClaims(tokenString)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "无效的令牌: " + err.Error(),
		})
		return
	}

	// 检查令牌是否过期
	isExpired := h.authService.GetJWTManager().IsTokenExpired(tokenString)

	// 检查令牌是否在黑名单中
	isBlacklisted := false
	isUserRevoked := false
	
	if !isExpired {
		isBlacklisted, _ = h.authService.IsTokenBlacklisted(tokenString)
		isUserRevoked, _ = h.authService.IsUserTokenRevoked(claims.UserID, claims.IssuedAt.Time)
	}

	status := "valid"
	if isExpired {
		status = "expired"
	} else if isBlacklisted {
		status = "blacklisted"
	} else if isUserRevoked {
		status = "revoked"
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "令牌状态检查完成",
		"data": gin.H{
			"status":        status,
			"is_expired":    isExpired,
			"is_blacklisted": isBlacklisted,
			"is_revoked":    isUserRevoked,
			"user_id":       claims.UserID,
			"username":      claims.Username,
			"role":          claims.Role,
			"token_type":    claims.TokenType,
			"expires_at":    claims.ExpiresAt.Time,
			"issued_at":     claims.IssuedAt.Time,
		},
	})
}
