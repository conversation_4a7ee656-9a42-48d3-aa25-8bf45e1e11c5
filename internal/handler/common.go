package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// APIResponse 通用API响应
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// ListResponse 列表响应
type ListResponse struct {
	Items    interface{} `json:"items"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(message string, data interface{}) APIResponse {
	return APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(message, error string) APIResponse {
	return APIResponse{
		Success: false,
		Message: message,
		Error:   error,
	}
}

// SuccessResponse 发送成功响应
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	c.JSO<PERSON>(http.StatusOK, NewSuccessResponse(message, data))
}

// ErrorResponse 发送错误响应
func ErrorResponse(c *gin.Context, statusCode int, message string, error interface{}) {
	errorStr := ""
	if error != nil {
		errorStr = error.(string)
	}
	c.JSON(statusCode, NewErrorResponse(message, errorStr))
}
