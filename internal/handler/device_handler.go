package handler

import (
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// DeviceHandler 设备处理器
type DeviceHandler struct {
	deviceService service.DeviceService
}

// NewDeviceHandler 创建设备处理器
func NewDeviceHandler(deviceService service.DeviceService) *DeviceHandler {
	return &DeviceHandler{
		deviceService: deviceService,
	}
}

// AdminCreateDevice 管理员创建设备
// @Summary 管理员创建设备
// @Description 管理员创建新设备
// @Tags 管理员设备管理
// @Accept json
// @Produce json
// @Param request body model.CreateDeviceRequest true "设备信息"
// @Success 201 {object} APIResponse{data=model.DeviceResponse}
// @Failure 400 {object} APIResponse
// @Failure 409 {object} APIResponse
// @Router /api/admin/devices [post]
func (h *DeviceHandler) AdminCreateDevice(c *gin.Context) {
	var req model.CreateDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	device, err := h.deviceService.AdminCreateDevice(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case model.ErrInvalidDeviceType:
			c.JSON(http.StatusBadRequest, NewErrorResponse("设备类型无效", err.Error()))
		case model.ErrSerialNumberExists:
			c.JSON(http.StatusConflict, NewErrorResponse("序列号已存在", err.Error()))
		case model.ErrIMEICodeExists:
			c.JSON(http.StatusConflict, NewErrorResponse("IMEI码已存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("创建设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusCreated, NewSuccessResponse("设备创建成功", device.ToResponse()))
}

// CreateDeviceType 创建设备类型
// @Summary 创建设备类型
// @Description 创建新的设备类型（管理员权限）
// @Tags 设备类型管理
// @Accept json
// @Produce json
// @Param request body model.CreateDeviceTypeRequest true "设备类型信息"
// @Success 201 {object} APIResponse{data=model.DeviceTypeResponse}
// @Failure 400 {object} APIResponse
// @Failure 409 {object} APIResponse
// @Router /api/device-types [post]
func (h *DeviceHandler) CreateDeviceType(c *gin.Context) {
	var req model.CreateDeviceTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	deviceType, err := h.deviceService.CreateDeviceType(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case model.ErrDeviceTypeAlreadyExists:
			c.JSON(http.StatusConflict, NewErrorResponse("设备类型已存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("创建设备类型失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusCreated, NewSuccessResponse("设备类型创建成功", deviceType.ToResponse()))
}

// UpdateDeviceType 更新设备类型
// @Summary 更新设备类型
// @Description 更新设备类型信息（管理员权限）
// @Tags 设备类型管理
// @Accept json
// @Produce json
// @Param id path string true "设备类型ID"
// @Param request body model.UpdateDeviceTypeRequest true "更新信息"
// @Success 200 {object} APIResponse{data=model.DeviceTypeResponse}
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/device-types/{id} [put]
func (h *DeviceHandler) UpdateDeviceType(c *gin.Context) {
	deviceTypeID := c.Param("id")
	if deviceTypeID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备类型ID不能为空", ""))
		return
	}

	var req model.UpdateDeviceTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	deviceType, err := h.deviceService.UpdateDeviceType(c.Request.Context(), deviceTypeID, &req)
	if err != nil {
		switch err {
		case model.ErrDeviceTypeNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备类型不存在", err.Error()))
		case model.ErrDeviceTypeAlreadyExists:
			c.JSON(http.StatusConflict, NewErrorResponse("设备类型名称已存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("更新设备类型失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备类型更新成功", deviceType.ToResponse()))
}

// DeleteDeviceType 删除设备类型
// @Summary 删除设备类型
// @Description 删除设备类型（管理员权限）
// @Tags 设备类型管理
// @Produce json
// @Param id path string true "设备类型ID"
// @Success 200 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/device-types/{id} [delete]
func (h *DeviceHandler) DeleteDeviceType(c *gin.Context) {
	deviceTypeID := c.Param("id")
	if deviceTypeID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备类型ID不能为空", ""))
		return
	}

	err := h.deviceService.DeleteDeviceType(c.Request.Context(), deviceTypeID)
	if err != nil {
		switch err {
		case model.ErrDeviceTypeNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备类型不存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("删除设备类型失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备类型删除成功", nil))
}

// ListDeviceTypes 获取设备类型列表
// @Summary 获取设备类型列表
// @Description 获取所有设备类型列表，一次性返回所有类型无需分页
// @Tags 设备类型管理
// @Produce json
// @Success 200 {object} APIResponse{data=object}
// @Router /api/device-types [get]
func (h *DeviceHandler) ListDeviceTypes(c *gin.Context) {
	deviceTypes, err := h.deviceService.ListAllDeviceTypes(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取设备类型列表失败", err.Error()))
		return
	}

	// 转换为响应格式
	var deviceTypeResponses []model.DeviceTypeResponse
	for _, deviceType := range deviceTypes {
		deviceTypeResponses = append(deviceTypeResponses, deviceType.ToResponse())
	}

	response := map[string]interface{}{
		"device_types": deviceTypeResponses,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取设备类型列表成功", response))
}

// AdminGetDevice 管理员获取设备详情
// @Summary 管理员获取设备详情
// @Description 管理员根据ID获取设备详情
// @Tags 管理员设备管理
// @Produce json
// @Param id path string true "设备ID"
// @Success 200 {object} APIResponse{data=model.DeviceResponse}
// @Failure 404 {object} APIResponse
// @Router /api/admin/devices/{id} [get]
func (h *DeviceHandler) AdminGetDevice(c *gin.Context) {
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备ID不能为空", ""))
		return
	}

	device, err := h.deviceService.AdminGetDevice(c.Request.Context(), deviceID)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("获取设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取设备成功", device.ToResponse()))
}

// AdminUpdateDevice 管理员更新设备
// @Summary 管理员更新设备
// @Description 管理员更新设备信息
// @Tags 管理员设备管理
// @Accept json
// @Produce json
// @Param id path string true "设备ID"
// @Param request body model.UpdateDeviceRequest true "更新信息"
// @Success 200 {object} APIResponse{data=model.DeviceResponse}
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/devices/{id} [put]
func (h *DeviceHandler) AdminUpdateDevice(c *gin.Context) {
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备ID不能为空", ""))
		return
	}

	var req model.UpdateDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	device, err := h.deviceService.AdminUpdateDevice(c.Request.Context(), deviceID, &req)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", err.Error()))
		case model.ErrSerialNumberExists:
			c.JSON(http.StatusConflict, NewErrorResponse("序列号已存在", err.Error()))
		case model.ErrIMEICodeExists:
			c.JSON(http.StatusConflict, NewErrorResponse("IMEI码已存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("更新设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备更新成功", device.ToResponse()))
}

// AdminDeleteDevice 管理员删除设备
// @Summary 管理员删除设备
// @Description 管理员删除设备
// @Tags 管理员设备管理
// @Param id path string true "设备ID"
// @Success 200 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/devices/{id} [delete]
func (h *DeviceHandler) AdminDeleteDevice(c *gin.Context) {
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备ID不能为空", ""))
		return
	}

	err := h.deviceService.AdminDeleteDevice(c.Request.Context(), deviceID)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("删除设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备删除成功", nil))
}

// AdminListDevices 管理员获取设备列表
// @Summary 管理员获取设备列表
// @Description 管理员获取所有设备列表
// @Tags 管理员设备管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param device_type query string false "设备类型"
// @Param status query string false "设备状态"
// @Param assignment query string false "分配状态"
// @Param search query string false "搜索关键词"
// @Success 200 {object} APIResponse{data=object}
// @Router /api/admin/devices [get]
func (h *DeviceHandler) AdminListDevices(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 构建过滤器
	filter := repository.DeviceFilter{
		DeviceType: c.Query("device_type"),
		Search:     c.Query("search"),
	}

	if status := c.Query("status"); status != "" {
		filter.Status = model.DeviceStatus(status)
	}

	if assignment := c.Query("assignment"); assignment != "" {
		filter.Assignment = model.DeviceAssignment(assignment)
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	devices, total, err := h.deviceService.AdminListDevices(c.Request.Context(), filter, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取设备列表失败", err.Error()))
		return
	}

	// 转换为响应格式
	var deviceResponses []model.DeviceResponse
	for _, device := range devices {
		deviceResponses = append(deviceResponses, device.ToResponse())
	}

	response := map[string]interface{}{
		"devices": deviceResponses,
		"total":   total,
		"page":    page,
		"size":    pageSize,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取设备列表成功", response))
}

// UserAssignDevice 用户添加设备
// @Summary 用户添加设备
// @Description 用户通过序列号和IMEI码添加设备
// @Tags 用户设备管理
// @Accept json
// @Produce json
// @Param request body model.AssignDeviceRequest true "设备信息"
// @Success 200 {object} APIResponse{data=model.DeviceResponse}
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Failure 409 {object} APIResponse
// @Router /api/user/devices [post]
func (h *DeviceHandler) UserAssignDevice(c *gin.Context) {
	var req model.AssignDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", "用户未登录"))
		return
	}

	device, err := h.deviceService.UserAssignDevice(c.Request.Context(), userID.(string), &req)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", "请检查序列号和IMEI码是否正确"))
		case model.ErrDeviceAlreadyAssigned:
			c.JSON(http.StatusConflict, NewErrorResponse("设备已被分配", "该设备已被其他用户添加"))
		case model.ErrCannotAssignOwnDevice:
			c.JSON(http.StatusConflict, NewErrorResponse("设备已添加", "该设备已在您的设备列表中"))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("添加设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备添加成功", device.ToResponse()))
}

// UserUnassignDevice 用户移除设备
// @Summary 用户移除设备
// @Description 用户移除自己的设备
// @Tags 用户设备管理
// @Param id path string true "设备ID"
// @Success 200 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/user/devices/{id} [delete]
func (h *DeviceHandler) UserUnassignDevice(c *gin.Context) {
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备ID不能为空", ""))
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", "用户未登录"))
		return
	}

	err := h.deviceService.UserUnassignDevice(c.Request.Context(), userID.(string), deviceID)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", "设备不在您的设备列表中"))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("移除设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("设备移除成功", nil))
}

// UserListDevices 用户获取设备列表
// @Summary 用户获取设备列表
// @Description 用户获取自己的设备列表
// @Tags 用户设备管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Success 200 {object} APIResponse{data=object}
// @Router /api/user/devices [get]
func (h *DeviceHandler) UserListDevices(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", "用户未登录"))
		return
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	devices, total, err := h.deviceService.UserListDevices(c.Request.Context(), userID.(string), pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取设备列表失败", err.Error()))
		return
	}

	// 转换为响应格式
	var deviceResponses []model.DeviceResponse
	for _, device := range devices {
		deviceResponses = append(deviceResponses, device.ToResponse())
	}

	response := map[string]interface{}{
		"devices": deviceResponses,
		"total":   total,
		"page":    page,
		"size":    pageSize,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取设备列表成功", response))
}

// UserGetDevice 用户获取设备详情
// @Summary 用户获取设备详情
// @Description 用户获取自己设备的详情
// @Tags 用户设备管理
// @Produce json
// @Param id path string true "设备ID"
// @Success 200 {object} APIResponse{data=model.DeviceResponse}
// @Failure 404 {object} APIResponse
// @Router /api/user/devices/{id} [get]
func (h *DeviceHandler) UserGetDevice(c *gin.Context) {
	deviceID := c.Param("id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("设备ID不能为空", ""))
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", "用户未登录"))
		return
	}

	device, err := h.deviceService.UserGetDevice(c.Request.Context(), userID.(string), deviceID)
	if err != nil {
		switch err {
		case model.ErrDeviceNotFound:
			c.JSON(http.StatusNotFound, NewErrorResponse("设备不存在", "设备不在您的设备列表中"))
		default:
			c.JSON(http.StatusInternalServerError, NewErrorResponse("获取设备失败", err.Error()))
		}
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取设备成功", device.ToResponse()))
}
