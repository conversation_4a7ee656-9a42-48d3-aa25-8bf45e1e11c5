package handler

import (
	"net/http"
	"strconv"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/database"

	"github.com/gin-gonic/gin"
)

// GroupHandler 分组处理器
type GroupHandler struct {
	groupService    service.GroupService
	tdengineManager *database.TDengineManager
}

// NewGroupHandler 创建分组处理器
func NewGroupHandler(groupService service.GroupService, tdengineManager *database.TDengineManager) *GroupHandler {
	return &GroupHandler{
		groupService:    groupService,
		tdengineManager: tdengineManager,
	}
}

// CreateGroup 创建分组
func (h *GroupHandler) CreateGroup(c *gin.Context) {
	var req model.CreateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	group, err := h.groupService.CreateGroup(c.Request.Context(), userID.(string), &req)
	if err != nil {
		switch err {
		case model.ErrGroupLimitReached:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrGroupAlreadyExists:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "创建分组失败",
			})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "分组创建成功",
		"data":    group.ToResponse(),
	})
}

// GetGroup 获取分组信息
func (h *GroupHandler) GetGroup(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	group, err := h.groupService.GetGroup(c.Request.Context(), groupID)
	if err != nil {
		if err == model.ErrGroupNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "获取分组信息失败",
			})
		}
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取分组信息成功",
		"data":    group,
	})
}

// UpdateGroup 更新分组信息
func (h *GroupHandler) UpdateGroup(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	var req model.UpdateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	group, err := h.groupService.UpdateGroup(c.Request.Context(), userID.(string), groupID, &req)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrGroupAlreadyExists:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "更新分组失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "分组更新成功",
		"data":    group.ToResponse(),
	})
}

// DeleteGroup 删除分组
func (h *GroupHandler) DeleteGroup(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.DeleteGroup(c.Request.Context(), userID.(string), groupID)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "删除分组失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "分组删除成功",
	})
}

// ListGroups 获取分组列表
func (h *GroupHandler) ListGroups(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	filter := repository.GroupFilter{
		CreatorID: c.Query("creator_id"),
		Status:    model.GroupStatus(c.Query("status")),
		Search:    c.Query("search"),
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	groups, total, err := h.groupService.ListGroups(c.Request.Context(), filter, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取分组列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取分组列表成功",
		"data": gin.H{
			"groups": groups,
			"total":  total,
			"page":   page,
			"size":   pageSize,
		},
	})
}

// GetUserGroups 获取用户的分组列表
func (h *GroupHandler) GetUserGroups(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	groups, err := h.groupService.GetUserGroups(c.Request.Context(), userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取用户分组列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取用户分组列表成功",
		"data":    groups,
	})
}

// JoinGroup 申请加入分组
func (h *GroupHandler) JoinGroup(c *gin.Context) {
	var req model.JoinGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	joinRequest, err := h.groupService.JoinGroup(c.Request.Context(), userID.(string), &req)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrGroupInactive:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrAlreadyGroupMember:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		case model.ErrJoinRequestAlreadyExists:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		case model.ErrGroupMemberLimitReached:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "申请加入分组失败",
			})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "加入申请提交成功",
		"data":    joinRequest.ToResponse(),
	})
}

// ReviewJoinRequest 审核加入申请
func (h *GroupHandler) ReviewJoinRequest(c *gin.Context) {
	requestID := c.Param("requestId")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "申请ID不能为空",
		})
		return
	}

	var req model.ReviewJoinRequestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.ReviewJoinRequest(c.Request.Context(), userID.(string), requestID, &req)
	if err != nil {
		switch err {
		case model.ErrJoinRequestNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrJoinRequestAlreadyProcessed:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		case model.ErrGroupMemberLimitReached:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "审核加入申请失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "审核完成",
	})
}

// RemoveMember 移除分组成员
func (h *GroupHandler) RemoveMember(c *gin.Context) {
	groupID := c.Param("groupId")
	memberUserID := c.Param("userId")

	if groupID == "" || memberUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID和用户ID不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.RemoveMember(c.Request.Context(), userID.(string), groupID, memberUserID)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrCannotRemoveCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrNotGroupMember:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "移除成员失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "成员移除成功",
	})
}

// LeaveGroup 退出分组
func (h *GroupHandler) LeaveGroup(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.LeaveGroup(c.Request.Context(), userID.(string), groupID)
	if err != nil {
		switch err {
		case model.ErrNotGroupMember:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrCannotLeaveAsCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "退出分组失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "退出分组成功",
	})
}

// ListGroupMembers 获取分组成员列表
func (h *GroupHandler) ListGroupMembers(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	members, total, err := h.groupService.ListGroupMembers(c.Request.Context(), groupID, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取分组成员列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取分组成员列表成功",
		"data": gin.H{
			"members": members,
			"total":   total,
			"page":    page,
			"size":    pageSize,
		},
	})
}

// ListJoinRequests 获取用户所属分组的加入申请列表
func (h *GroupHandler) ListJoinRequests(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	filter := repository.JoinRequestFilter{
		Status: model.JoinRequestStatus(c.Query("status")),
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	requests, total, err := h.groupService.ListUserGroupJoinRequests(c.Request.Context(), userID.(string), filter, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取加入申请列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取加入申请列表成功",
		"data": gin.H{
			"requests": requests,
			"total":    total,
			"page":     page,
			"size":     pageSize,
		},
	})
}

// CreateTopic 创建Topic
func (h *GroupHandler) CreateTopic(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	var req model.CreateTopicRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	topic, err := h.groupService.CreateTopic(c.Request.Context(), userID.(string), groupID, &req)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrTopicAlreadyExists:
			c.JSON(http.StatusConflict, gin.H{
				"code":    config.ErrCodeConflict,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "创建Topic失败",
			})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "Topic创建成功",
		"data":    topic.ToResponse(),
	})
}

// DeleteTopic 删除Topic
func (h *GroupHandler) DeleteTopic(c *gin.Context) {
	groupID := c.Param("groupId")
	topicID := c.Param("topicId")

	if groupID == "" || topicID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID和TopicID不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.DeleteTopic(c.Request.Context(), userID.(string), groupID, topicID)
	if err != nil {
		switch err {
		case model.ErrTopicNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "删除Topic失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "Topic删除成功",
	})
}

// ListGroupTopics 获取分组Topic列表
func (h *GroupHandler) ListGroupTopics(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	topics, total, err := h.groupService.ListGroupTopics(c.Request.Context(), groupID, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "获取Topic列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取Topic列表成功",
		"data": gin.H{
			"topics": topics,
			"total":  total,
			"page":   page,
			"size":   pageSize,
		},
	})
}

// SetTopicPermission 设置Topic权限
func (h *GroupHandler) SetTopicPermission(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID不能为空",
		})
		return
	}

	var req model.SetTopicPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.SetTopicPermission(c.Request.Context(), userID.(string), groupID, &req)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrNotGroupMember:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrTopicNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "设置Topic权限失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "Topic权限设置成功",
	})
}

// RemoveTopicPermission 移除Topic权限
func (h *GroupHandler) RemoveTopicPermission(c *gin.Context) {
	groupID := c.Param("groupId")
	memberUserID := c.Param("userId")
	fullTopic := c.Query("fullTopic")

	if groupID == "" || memberUserID == "" || fullTopic == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID、用户ID和Topic名称不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未认证的用户",
		})
		return
	}

	err := h.groupService.RemoveTopicPermission(c.Request.Context(), userID.(string), groupID, memberUserID, fullTopic)
	if err != nil {
		switch err {
		case model.ErrGroupNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrNotGroupCreator:
			c.JSON(http.StatusForbidden, gin.H{
				"code":    config.ErrCodeForbidden,
				"message": err.Error(),
			})
		case model.ErrTopicNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		case model.ErrTopicPermissionDenied:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": "权限不存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "移除Topic权限失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "Topic权限移除成功",
	})
}

// ListTopicPermissions 获取Topic权限列表
func (h *GroupHandler) ListTopicPermissions(c *gin.Context) {
	groupID := c.Param("groupId")
	fullTopic := c.Query("fullTopic")

	if groupID == "" || fullTopic == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "分组ID和Topic名称不能为空",
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 100 {
		pageSize = 100
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	permissions, total, err := h.groupService.ListTopicPermissions(c.Request.Context(), groupID, fullTopic, pagination)
	if err != nil {
		switch err {
		case model.ErrTopicNotFound:
			c.JSON(http.StatusNotFound, gin.H{
				"code":    config.ErrCodeNotFound,
				"message": err.Error(),
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    config.ErrCodeInternalError,
				"message": "获取Topic权限列表失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "获取Topic权限列表成功",
		"data": gin.H{
			"permissions": permissions,
			"total":       total,
			"page":        page,
			"size":        pageSize,
		},
	})
}

// QueryTopicData 查询Topic数据
func (h *GroupHandler) QueryTopicData(c *gin.Context) {
	// 获取topic参数（必填）
	topic := c.Query("topic")
	if topic == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "topic参数不能为空",
		})
		return
	}

	// 检查TDengine管理器是否可用
	if h.tdengineManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "TDengine服务不可用",
		})
		return
	}

	// 解析可选参数
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pagesize", "10"))
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))

	// 限制pageSize范围
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 解析时间范围参数
	var startTime, endTime time.Time
	var err error

	if timeRangeStr := c.Query("timerange"); timeRangeStr != "" {
		// 解析时间范围，支持格式如 "1d", "2h", "30m"
		duration, parseErr := time.ParseDuration(timeRangeStr)
		if parseErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    1001,
				"message": "时间范围格式错误，支持格式如: 1d, 2h, 30m",
			})
			return
		}
		endTime = time.Now()
		startTime = endTime.Add(-duration)
	} else {
		// 默认1天
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour)
	}

	// 如果提供了具体的开始和结束时间，优先使用
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    config.ErrCodeInvalidParams,
				"message": "开始时间格式错误，请使用RFC3339格式",
			})
			return
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    config.ErrCodeInvalidParams,
				"message": "结束时间格式错误，请使用RFC3339格式",
			})
			return
		}
	}

	// 构建查询参数
	query := database.TopicDataQuery{
		Topic:     topic,
		PageSize:  pageSize,
		Page:      page,
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 查询数据
	messages, total, err := h.tdengineManager.QueryTopicData(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "查询Topic数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "查询Topic数据成功",
		"data": gin.H{
			"messages":   messages,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   endTime.Format(time.RFC3339),
		},
	})
}

// DeleteTopicData 删除Topic数据
func (h *GroupHandler) DeleteTopicData(c *gin.Context) {
	// 获取topic参数（必填）
	topic := c.Query("topic")
	if topic == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "topic参数不能为空",
		})
		return
	}

	// 检查TDengine管理器是否可用
	if h.tdengineManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "TDengine服务不可用",
		})
		return
	}

	// 获取时间范围参数（必填）
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "开始时间和结束时间不能为空",
		})
		return
	}

	// 解析时间参数
	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "开始时间格式错误，请使用RFC3339格式",
		})
		return
	}

	endTime, err := time.Parse(time.RFC3339, endTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    config.ErrCodeInvalidParams,
			"message": "结束时间格式错误，请使用RFC3339格式",
		})
		return
	}

	// 构建删除参数
	query := database.TopicDataDeleteQuery{
		Topic:     topic,
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 执行删除操作
	deletedCount, err := h.tdengineManager.DeleteTopicData(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    config.ErrCodeInternalError,
			"message": "删除Topic数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "删除Topic数据成功",
		"data": gin.H{
			"topic":         topic,
			"deleted_count": deletedCount,
			"start_time":    startTime.Format(time.RFC3339),
			"end_time":      endTime.Format(time.RFC3339),
		},
	})
}
