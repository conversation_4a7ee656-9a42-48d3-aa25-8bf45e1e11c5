package handler

import (
	"strconv"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/middleware"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/service"

	"github.com/gin-gonic/gin"
)

// successResponse 返回成功响应 - 在本文件中实现以避免导入循环
func successResponse(c *gin.Context, message string, data interface{}) {
	c.JSON(200, gin.H{
		"code":    0,
		"message": message,
		"data":    data,
	})
}

// MonitorHandler 监控处理器
type MonitorHandler struct {
	monitorService service.MonitorService
}

// NewMonitorHandler 创建监控处理器
func NewMonitorHandler(monitorService service.MonitorService) *MonitorHandler {
	return &MonitorHandler{
		monitorService: monitorService,
	}
}

// GetDashboard 获取监控面板数据
// @Summary 获取监控面板数据
// @Description 获取MQTT监控面板的综合数据，包括概览、连接、消息、性能等信息
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} APIResponse{data=model.DashboardData}
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /api/monitor/dashboard [get]
func (h *MonitorHandler) GetDashboard(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		middleware.AbortWithError(c, config.ErrCodeUnauthorized, "未授权访问", "用户未登录")
		return
	}

	// 获取监控面板数据
	data, err := h.monitorService.GetDashboardData(c.Request.Context(), userID.(string))
	if err != nil {
		middleware.AbortWithError(c, config.ErrCodeInternalError, "获取监控面板数据失败", err.Error())
		return
	}

	successResponse(c, "获取监控面板数据成功", data)
}

// GetPerformanceHistory 获取性能历史数据
// @Summary 获取性能历史数据
// @Description 获取MQTT服务器的历史性能数据
// @Tags 监控
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Param interval query string false "时间间隔 (1m,5m,15m,30m,1h,6h,12h,1d)"
// @Param limit query int false "限制数量" default(100)
// @Success 200 {object} APIResponse{data=[]model.PerformanceMetrics}
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /api/monitor/performance/history [get]
func (h *MonitorHandler) GetPerformanceHistory(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		middleware.AbortWithError(c, config.ErrCodeUnauthorized, "未授权访问", "用户未登录")
		return
	}

	// 解析查询参数
	query := &model.MonitorQuery{
		Interval: c.Query("interval"),
		Limit:    100, // 默认限制
	}

	// 解析时间参数
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			query.StartTime = startTime
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			query.EndTime = endTime
		}
	}

	// 解析限制参数
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			query.Limit = limit
		}
	}

	// 获取性能历史数据
	history, err := h.monitorService.GetPerformanceHistory(c.Request.Context(), userID.(string), query)
	if err != nil {
		middleware.AbortWithError(c, config.ErrCodeForbidden, "获取性能历史失败", err.Error())
		return
	}

	successResponse(c, "获取性能历史成功", history)
}
