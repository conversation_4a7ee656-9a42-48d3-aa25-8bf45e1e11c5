package handler

import (
	"net/http"

	"beacon/cloud/internal/mqtt"

	"github.com/gin-gonic/gin"
)

// MQTTHandler MQTT相关的HTTP处理器
type MQTTHandler struct{}

// NewMQTTHandler 创建新的MQTT处理器
func NewMQTTHandler() *MQTTHandler {
	return &MQTTHandler{}
}

// GetMQTTStatus 获取MQTT服务状态
// @Summary 获取MQTT服务状态
// @Description 获取MQTT服务的运行状态和统计信息
// @Tags MQTT
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Router /api/mqtt/status [get]
func (h *MQTTHandler) GetMQTTStatus(c *gin.Context) {
	stats := mqtt.GetMQTTStats()

	SuccessResponse(c, "获取MQTT状态成功", stats)
}

// GetMQTTHealth MQTT健康检查
// @Summary MQTT健康检查
// @Description 检查MQTT服务是否健康运行
// @Tags MQTT
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/mqtt/health [get]
func (h *MQTTHandler) GetMQTTHealth(c *gin.Context) {
	if err := mqtt.HealthCheckMQTT(); err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "MQTT服务健康检查失败", err.Error())
		return
	}

	SuccessResponse(c, "MQTT服务健康", map[string]interface{}{
		"status":  "healthy",
		"running": mqtt.IsMQTTRunning(),
	})
}

// RestartMQTT 重启MQTT服务
// @Summary 重启MQTT服务
// @Description 重启MQTT服务（需要管理员权限）
// @Tags MQTT
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/mqtt/restart [post]
func (h *MQTTHandler) RestartMQTT(c *gin.Context) {
	manager := mqtt.GetMQTTManager()
	if manager == nil {
		ErrorResponse(c, http.StatusInternalServerError, "MQTT管理器未初始化", nil)
		return
	}

	if err := manager.Restart(); err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "重启MQTT服务失败", err.Error())
		return
	}

	SuccessResponse(c, "MQTT服务重启成功", nil)
}

// GetMQTTConfig 获取MQTT配置
// @Summary 获取MQTT配置
// @Description 获取当前MQTT服务配置信息
// @Tags MQTT
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Router /api/mqtt/config [get]
func (h *MQTTHandler) GetMQTTConfig(c *gin.Context) {
	manager := mqtt.GetMQTTManager()
	if manager == nil {
		ErrorResponse(c, http.StatusInternalServerError, "MQTT管理器未初始化", nil)
		return
	}

	config := manager.GetConfig()
	SuccessResponse(c, "获取MQTT配置成功", config)
}
