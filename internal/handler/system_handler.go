package handler

import (
	"net/http"
	"strconv"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/internal/service"

	"github.com/gin-gonic/gin"
)

// SystemHandler 系统管理处理器
type SystemHandler struct {
	systemService service.SystemService
}

// NewSystemHandler 创建系统管理处理器
func NewSystemHandler(systemService service.SystemService) *SystemHandler {
	return &SystemHandler{
		systemService: systemService,
	}
}

// GetLogs 获取系统日志
// @Summary 获取系统日志
// @Description 获取系统日志列表，支持分页和过滤
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param level query string false "日志级别"
// @Param module query string false "模块名称"
// @Param action query string false "操作动作"
// @Param user_id query string false "用户ID"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param search query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} APIResponse{data=object{list=[]model.SystemLogResponse,total=int64}}
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /api/system/logs [get]
func (h *SystemHandler) GetLogs(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		ErrorResponse(c, http.StatusUnauthorized, "未授权访问", "用户未登录")
		return
	}

	// 解析查询参数
	filter := &model.SystemLogFilter{
		Level:  model.LogLevel(c.Query("level")),
		Module: c.Query("module"),
		Action: c.Query("action"),
		UserID: c.Query("user_id"),
		Search: c.Query("search"),
		Page:   1,
		Limit:  20,
	}

	if page, err := strconv.Atoi(c.Query("page")); err == nil && page > 0 {
		filter.Page = page
	}
	if limit, err := strconv.Atoi(c.Query("limit")); err == nil && limit > 0 {
		filter.Limit = limit
	}

	// 解析时间参数
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", startTime); err == nil {
			filter.StartTime = t
		}
	}
	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", endTime); err == nil {
			filter.EndTime = t
		}
	}

	logs, total, err := h.systemService.GetLogs(c.Request.Context(), userID.(string), filter)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "获取系统日志失败", err.Error())
		return
	}

	SuccessResponse(c, "操作成功", gin.H{
		"list":  logs,
		"total": total,
	})
}

// GetLogStatistics 获取日志统计
// @Summary 获取日志统计
// @Description 获取指定时间范围内的日志统计信息
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间"
// @Param end_time query string true "结束时间"
// @Success 200 {object} APIResponse{data=object}
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /api/system/logs/statistics [get]
func (h *SystemHandler) GetLogStatistics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		ErrorResponse(c, http.StatusUnauthorized, "未授权访问", "用户未登录")
		return
	}

	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		ErrorResponse(c, http.StatusBadRequest, "参数错误", "开始时间和结束时间不能为空")
		return
	}

	startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "参数错误", "开始时间格式错误")
		return
	}

	endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "参数错误", "结束时间格式错误")
		return
	}

	stats, err := h.systemService.GetLogStatistics(c.Request.Context(), userID.(string), startTime, endTime)
	if err != nil {
		if err == model.ErrPermissionDenied {
			ErrorResponse(c, http.StatusForbidden, "权限不足", "只有管理员可以查看日志统计")
		} else {
			ErrorResponse(c, http.StatusInternalServerError, "获取日志统计失败", err.Error())
		}
		return
	}

	SuccessResponse(c, "操作成功", stats)
}

// CleanupLogs 清理历史日志
// @Summary 清理历史日志
// @Description 清理指定时间之前的历史日志
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param before query string true "清理时间点"
// @Success 200 {object} APIResponse{data=object{count=int64}}
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /api/system/logs/cleanup [delete]
func (h *SystemHandler) CleanupLogs(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		ErrorResponse(c, http.StatusUnauthorized, "未授权访问", "用户未登录")
		return
	}

	beforeStr := c.Query("before")
	if beforeStr == "" {
		ErrorResponse(c, http.StatusBadRequest, "参数错误", "清理时间点不能为空")
		return
	}

	before, err := time.Parse("2006-01-02 15:04:05", beforeStr)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "参数错误", "时间格式错误")
		return
	}

	count, err := h.systemService.CleanupLogs(c.Request.Context(), userID.(string), before)
	if err != nil {
		if err == model.ErrPermissionDenied {
			ErrorResponse(c, http.StatusForbidden, "权限不足", "只有管理员可以清理历史日志")
		} else {
			ErrorResponse(c, http.StatusInternalServerError, "清理历史日志失败", err.Error())
		}
		return
	}

	SuccessResponse(c, "操作成功", gin.H{
		"count": count,
	})
}
