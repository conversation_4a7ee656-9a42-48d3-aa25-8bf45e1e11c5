package handler

import (
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/auth"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService  service.UserService
	authService  *service.AuthService
	emailService service.EmailService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService, authService *service.AuthService, emailService service.EmailService) *UserHandler {
	return &UserHandler{
		userService:  userService,
		authService:  authService,
		emailService: emailService,
	}
}

// RegisterSendCode 发送注册验证码
// @Summary 发送注册验证码
// @Description 发送注册验证码并缓存用户信息
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body model.RegisterSendCodeRequest true "注册信息"
// @Success 200 {object} APIResponse{data=string}
// @Failure 400 {object} APIResponse
// @Failure 409 {object} APIResponse
// @Router /api/auth/register/send-code [post]
func (h *UserHandler) RegisterSendCode(c *gin.Context) {
	var req model.RegisterSendCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	// 缓存用户注册信息
	if err := h.userService.CacheUserRegistrationInfo(c.Request.Context(), &req); err != nil {
		if err == model.ErrUserAlreadyExists {
			c.JSON(http.StatusConflict, NewErrorResponse("邮箱已被使用", err.Error()))
			return
		}
		c.JSON(http.StatusBadRequest, NewErrorResponse("缓存用户信息失败", err.Error()))
		return
	}

	// 发送验证码
	code, err := h.emailService.SendVerificationCode(c.Request.Context(), req.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse("发送验证码失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("验证码已发送到您的邮箱", gin.H{
		"email": req.Email,
		"code":  code, // 开发环境下返回验证码，生产环境应移除
	}))
}

// RegisterVerify 验证注册验证码并完成注册
// @Summary 验证注册验证码并完成注册
// @Description 验证邮箱验证码并从缓存中获取用户信息完成注册
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body model.RegisterVerifyRequest true "验证信息"
// @Success 201 {object} APIResponse{data=model.UserResponse}
// @Failure 400 {object} APIResponse
// @Failure 409 {object} APIResponse
// @Router /api/auth/register/verify [post]
func (h *UserHandler) RegisterVerify(c *gin.Context) {
	var req model.RegisterVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	// 验证验证码
	if err := h.emailService.VerifyCode(c.Request.Context(), req.Email, req.Code); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("验证码错误或已过期", err.Error()))
		return
	}

	// 从缓存中获取用户信息并注册
	user, err := h.userService.RegisterUserFromCache(c.Request.Context(), req.Email)
	if err != nil {
		if err == model.ErrUserAlreadyExists {
			c.JSON(http.StatusConflict, NewErrorResponse("用户已存在", err.Error()))
			return
		}
		c.JSON(http.StatusBadRequest, NewErrorResponse("注册失败", err.Error()))
		return
	}

	c.JSON(http.StatusCreated, NewSuccessResponse("注册成功", user.ToResponse()))
}

// ForgotPassword 忘记密码
// @Summary 忘记密码
// @Description 支持发送验证码或验证验证码后重置密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body model.ForgotPasswordRequest true "忘记密码信息"
// @Success 200 {object} APIResponse{data=string} "发送验证码成功或密码重置成功"
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/auth/forgot-password [post]
func (h *UserHandler) ForgotPassword(c *gin.Context) {
	var req model.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	if req.SendCode {
		// 发送验证码模式
		// 检查用户是否存在
		user, err := h.userService.GetUserByEmail(c.Request.Context(), req.Email)
		if err != nil {
			if err == model.ErrUserNotFound {
				c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
				return
			}
			c.JSON(http.StatusInternalServerError, NewErrorResponse("查询用户失败", err.Error()))
			return
		}

		// 检查用户状态
		if !user.IsActive() {
			c.JSON(http.StatusForbidden, NewErrorResponse("账户状态异常，无法重置密码", ""))
			return
		}

		// 发送验证码
		code, err := h.emailService.SendVerificationCode(c.Request.Context(), req.Email)
		if err != nil {
			c.JSON(http.StatusInternalServerError, NewErrorResponse("发送验证码失败", err.Error()))
			return
		}

		c.JSON(http.StatusOK, NewSuccessResponse("验证码已发送到您的邮箱", gin.H{
			"email": req.Email,
			"code":  code, // 开发环境下返回验证码，生产环境应移除
		}))
	} else {
		// 验证验证码并重置密码模式
		// 验证验证码
		if err := h.emailService.VerifyCode(c.Request.Context(), req.Email, req.Code); err != nil {
			c.JSON(http.StatusBadRequest, NewErrorResponse("验证码错误或已过期", err.Error()))
			return
		}

		// 获取用户
		user, err := h.userService.GetUserByEmail(c.Request.Context(), req.Email)
		if err != nil {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}

		// 生成新密码
		newPassword, err := h.userService.ResetPassword(c.Request.Context(), user.ID.Hex())
		if err != nil {
			c.JSON(http.StatusInternalServerError, NewErrorResponse("重置密码失败", err.Error()))
			return
		}

		// 发送新密码到邮箱
		if err := h.emailService.SendResetPassword(c.Request.Context(), req.Email, newPassword); err != nil {
			c.JSON(http.StatusInternalServerError, NewErrorResponse("发送新密码失败", err.Error()))
			return
		}

		c.JSON(http.StatusOK, NewSuccessResponse("密码重置成功，新密码已发送到您的邮箱", gin.H{
			"email": req.Email,
		}))
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body model.LoginRequest true "登录信息"
// @Success 200 {object} APIResponse{data=model.LoginResponse}
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Router /api/auth/login [post]
func (h *UserHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	loginResp, err := h.userService.LoginUser(c.Request.Context(), &req)
	if err != nil {
		if err == model.ErrUserNotFound || err == model.ErrPasswordMismatch {
			c.JSON(http.StatusUnauthorized, NewErrorResponse("用户名或密码错误", ""))
			return
		}
		if err == model.ErrUserInactive || err == model.ErrUserBanned {
			c.JSON(http.StatusForbidden, NewErrorResponse("账户状态异常", err.Error()))
			return
		}
		c.JSON(http.StatusBadRequest, NewErrorResponse("登录失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("登录成功", loginResp))
}

// GetProfile 获取当前用户资料
// @Summary 获取用户资料
// @Description 获取当前登录用户的资料信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} APIResponse{data=model.UserResponse}
// @Failure 401 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/users/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := h.getCurrentUserID(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", ""))
		return
	}

	user, err := h.userService.GetUserProfile(c.Request.Context(), userID)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取用户资料失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取成功", user.ToResponse()))
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新当前登录用户的资料信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.UpdateUserRequest true "更新信息"
// @Success 200 {object} APIResponse{data=model.UserResponse}
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Router /api/users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := h.getCurrentUserID(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", ""))
		return
	}

	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	user, err := h.userService.UpdateUserProfile(c.Request.Context(), userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("更新失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("更新成功", user.ToResponse()))
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前登录用户的密码
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.ChangePasswordRequest true "密码信息"
// @Success 200 {object} APIResponse
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Router /api/users/change-password [post]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID := h.getCurrentUserID(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", ""))
		return
	}

	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	err := h.userService.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		if err == model.ErrPasswordMismatch {
			c.JSON(http.StatusBadRequest, NewErrorResponse("原密码错误", ""))
			return
		}
		c.JSON(http.StatusBadRequest, NewErrorResponse("修改密码失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("密码修改成功", nil))
}

// GetMQTTInfo 获取MQTT连接信息
// @Summary 获取MQTT连接信息
// @Description 获取当前用户的MQTT连接信息，包括ClientID等
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} APIResponse{data=object}
// @Failure 401 {object} APIResponse
// @Router /api/users/mqtt-info [get]
func (h *UserHandler) GetMQTTInfo(c *gin.Context) {
	userID := h.getCurrentUserID(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("未授权", ""))
		return
	}

	user, err := h.userService.GetUserProfile(c.Request.Context(), userID)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取用户信息失败", err.Error()))
		return
	}

	mqttInfo := gin.H{
		"client_id": user.ID.Hex(),
		"user_id":   user.ID.Hex(),
		"email":     user.Email,
		"role":      user.Role,
		"status":    user.Status,
		"note":      "使用UserID作为MQTT ClientID进行连接，无需用户名密码认证",
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取MQTT连接信息成功", mqttInfo))
}

// ListUsers 获取用户列表（管理员）
// @Summary 获取用户列表
// @Description 获取用户列表，仅管理员可访问
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param role query string false "角色过滤"
// @Param status query string false "状态过滤"
// @Param search query string false "搜索关键词"
// @Success 200 {object} APIResponse{data=ListResponse}
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Router /api/admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 检查管理员权限
	if !h.isAdmin(c) {
		c.JSON(http.StatusForbidden, NewErrorResponse("权限不足", ""))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	filter := repository.UserFilter{
		Role:   model.UserRole(c.Query("role")),
		Status: model.UserStatus(c.Query("status")),
		Search: c.Query("search"),
	}

	pagination := repository.Pagination{
		Page:     page,
		PageSize: pageSize,
	}

	users, total, err := h.userService.ListUsers(c.Request.Context(), filter, pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取用户列表失败", err.Error()))
		return
	}

	// 转换为响应格式
	userResponses := make([]model.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToResponse()
	}

	response := ListResponse{
		Items:    userResponses,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取成功", response))
}

// GetUserByID 获取指定用户信息（管理员）
// @Summary 获取用户信息
// @Description 获取指定用户的详细信息，仅管理员可访问
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "用户ID"
// @Success 200 {object} APIResponse{data=model.UserResponse}
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/users/{id} [get]
func (h *UserHandler) GetUserByID(c *gin.Context) {
	// 检查管理员权限
	if !h.isAdmin(c) {
		c.JSON(http.StatusForbidden, NewErrorResponse("权限不足", ""))
		return
	}

	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("用户ID不能为空", ""))
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("获取用户信息失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取成功", user.ToResponse()))
}

// UpdateUserStatus 更新用户状态（管理员）
// @Summary 更新用户状态
// @Description 更新指定用户的状态，仅管理员可访问
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "用户ID"
// @Param request body model.UpdateUserStatusRequest true "状态信息"
// @Success 200 {object} APIResponse
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/users/{id}/status [put]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	// 检查管理员权限
	if !h.isAdmin(c) {
		c.JSON(http.StatusForbidden, NewErrorResponse("权限不足", ""))
		return
	}

	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("用户ID不能为空", ""))
		return
	}

	var req model.UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	err := h.userService.UpdateUserStatus(c.Request.Context(), userID, req.Status)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("更新用户状态失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("用户状态更新成功", nil))
}

// DeleteUser 删除用户（管理员）
// @Summary 删除用户
// @Description 删除指定用户，仅管理员可访问
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "用户ID"
// @Success 200 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// 检查管理员权限
	if !h.isAdmin(c) {
		c.JSON(http.StatusForbidden, NewErrorResponse("权限不足", ""))
		return
	}

	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("用户ID不能为空", ""))
		return
	}

	// 防止管理员删除自己
	currentUserID := h.getCurrentUserID(c)
	if userID == currentUserID {
		c.JSON(http.StatusBadRequest, NewErrorResponse("不能删除自己", ""))
		return
	}

	err := h.userService.DeleteUser(c.Request.Context(), userID)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("删除用户失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("用户删除成功", nil))
}

// AdminUpdateUser 管理员更新用户信息
// @Summary 管理员更新用户信息
// @Description 管理员更新指定用户的信息，仅管理员可访问
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "用户ID"
// @Param request body model.AdminUpdateUserRequest true "更新信息"
// @Success 200 {object} APIResponse{data=model.UserResponse}
// @Failure 400 {object} APIResponse
// @Failure 401 {object} APIResponse
// @Failure 403 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /api/admin/users/{id} [put]
func (h *UserHandler) AdminUpdateUser(c *gin.Context) {
	// 检查管理员权限
	if !h.isAdmin(c) {
		c.JSON(http.StatusForbidden, NewErrorResponse("权限不足", ""))
		return
	}

	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse("用户ID不能为空", ""))
		return
	}

	var req model.AdminUpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数无效", err.Error()))
		return
	}

	user, err := h.userService.AdminUpdateUser(c.Request.Context(), userID, &req)
	if err != nil {
		if err == model.ErrUserNotFound {
			c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", ""))
			return
		}
		if err == model.ErrUserAlreadyExists {
			c.JSON(http.StatusConflict, NewErrorResponse("邮箱已被使用", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, NewErrorResponse("更新用户信息失败", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("用户信息更新成功", user.ToResponse()))
}

// RefreshToken功能已在auth_handler.go中实现

// getCurrentUserID 获取当前用户ID
func (h *UserHandler) getCurrentUserID(c *gin.Context) string {
	if claims, exists := c.Get("claims"); exists {
		if userClaims, ok := claims.(*auth.Claims); ok {
			return userClaims.UserID
		}
	}
	return ""
}

// isAdmin 检查是否为管理员
func (h *UserHandler) isAdmin(c *gin.Context) bool {
	if claims, exists := c.Get("claims"); exists {
		if userClaims, ok := claims.(*auth.Claims); ok {
			return userClaims.Role == string(model.RoleAdmin)
		}
	}
	return false
}

// 通用响应类型已移至common.go
