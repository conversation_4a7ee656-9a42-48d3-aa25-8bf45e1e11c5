package handler

import (
	"net/http"

	"beacon/cloud/pkg/wireguard"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// WireGuardHandler WireGuard API 处理器
type WireGuardHandler struct {
	wgManager wireguard.WireGuardManager
}

// NewWireGuardHandler 创建新的 WireGuard 处理器
func NewWireGuardHandler(wgManager wireguard.WireGuardManager) *WireGuardHandler {
	return &WireGuardHandler{
		wgManager: wgManager,
	}
}

// CreateServer 创建服务器
func (h *WireGuardHandler) CreateServer(c *gin.Context) {
	userID := getUserIDFromContext(c)

	var req wireguard.CreateServerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	server, err := h.wgManager.CreateUserServer(c.Request.Context(), userID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, server)
}

// ListServers 获取服务器列表
func (h *WireGuardHandler) ListServers(c *gin.Context) {
	userID := getUserIDFromContext(c)

	servers, err := h.wgManager.ListUserServers(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, servers)
}

// GetServer 获取服务器详情
func (h *WireGuardHandler) GetServer(c *gin.Context) {
	userID := getUserIDFromContext(c)

	serverID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
		return
	}

	server, err := h.wgManager.GetUserServer(c.Request.Context(), userID, serverID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
		return
	}

	c.JSON(http.StatusOK, server)
}

// UpdateServer 更新服务器
func (h *WireGuardHandler) UpdateServer(c *gin.Context) {
	userID := getUserIDFromContext(c)

	serverID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
		return
	}

	var req wireguard.UpdateServerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.wgManager.UpdateUserServer(c.Request.Context(), userID, serverID, &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server updated successfully"})
}

// DeleteServer 删除服务器
func (h *WireGuardHandler) DeleteServer(c *gin.Context) {
	userID := getUserIDFromContext(c)

	serverID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
		return
	}

	if err := h.wgManager.DeleteUserServer(c.Request.Context(), userID, serverID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server deleted successfully"})
}

// RestartServer 重启服务器
func (h *WireGuardHandler) RestartServer(c *gin.Context) {
	userID := getUserIDFromContext(c)

	serverID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
		return
	}

	if err := h.wgManager.ApplyServerConfig(c.Request.Context(), userID, serverID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server restarted successfully"})
}

// CreateClient 创建客户端
func (h *WireGuardHandler) CreateClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	var req wireguard.CreateClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	client, err := h.wgManager.CreateClient(c.Request.Context(), userID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, client)
}

// ListClients 获取客户端列表
func (h *WireGuardHandler) ListClients(c *gin.Context) {
	userID := getUserIDFromContext(c)

	var serverID primitive.ObjectID
	if serverIDStr := c.Query("server_id"); serverIDStr != "" {
		var err error
		serverID, err = primitive.ObjectIDFromHex(serverIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
			return
		}
	}

	clients, err := h.wgManager.ListClients(c.Request.Context(), userID, serverID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, clients)
}

// GetClient 获取客户端详情
func (h *WireGuardHandler) GetClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	client, err := h.wgManager.GetClient(c.Request.Context(), userID, clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Client not found"})
		return
	}

	c.JSON(http.StatusOK, client)
}

// UpdateClient 更新客户端
func (h *WireGuardHandler) UpdateClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	var req wireguard.UpdateClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.wgManager.UpdateClient(c.Request.Context(), userID, clientID, &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client updated successfully"})
}

// DeleteClient 删除客户端
func (h *WireGuardHandler) DeleteClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	if err := h.wgManager.DeleteClient(c.Request.Context(), userID, clientID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client deleted successfully"})
}

// EnableClient 启用客户端
func (h *WireGuardHandler) EnableClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	if err := h.wgManager.EnableClient(c.Request.Context(), userID, clientID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client enabled successfully"})
}

// DisableClient 禁用客户端
func (h *WireGuardHandler) DisableClient(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	if err := h.wgManager.DisableClient(c.Request.Context(), userID, clientID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client disabled successfully"})
}

// getUserIDFromContext 从上下文获取用户ID的辅助函数
func getUserIDFromContext(c *gin.Context) primitive.ObjectID {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		panic("user_id not found in context")
	}

	userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
	if err != nil {
		panic("invalid user_id format")
	}

	return userID
}

// GetClientConfig 获取客户端配置
func (h *WireGuardHandler) GetClientConfig(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	config, err := h.wgManager.GenerateClientConfig(c.Request.Context(), userID, clientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Header("Content-Type", "text/plain")
	c.Header("Content-Disposition", "attachment; filename=client.conf")
	c.String(http.StatusOK, config)
}

// GetClientQRCode 获取客户端 QR 码
func (h *WireGuardHandler) GetClientQRCode(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	qrCode, err := h.wgManager.GenerateQRCode(c.Request.Context(), userID, clientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Header("Content-Type", "image/png")
	c.Data(http.StatusOK, "image/png", qrCode)
}

// GetClientStats 获取客户端统计
func (h *WireGuardHandler) GetClientStats(c *gin.Context) {
	userID := getUserIDFromContext(c)

	clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	stats, err := h.wgManager.GetClientStats(c.Request.Context(), userID, clientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}
