package middleware

import (
	"beacon/cloud/internal/config"
	"beacon/cloud/pkg/auth"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
type AuthMiddleware struct {
	jwtManager *auth.JWTManager
}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware(secretKey string, accessTokenDuration, refreshTokenDuration time.Duration) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: auth.NewJWTManager(secretKey, accessTokenDuration, refreshTokenDuration),
	}
}

// JWTAuth JWT认证中间件
func (m *AuthMiddleware) JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "未提供认证令牌",
			})
			return
		}

		// 提取令牌
		tokenString, err := auth.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": err.Error(),
			})
			return
		}

		// 验证令牌
		claims, err := m.jwtManager.VerifyToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "无效的认证令牌: " + err.Error(),
			})
			return
		}

		// 检查令牌类型
		if claims.TokenType != "access" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "无效的令牌类型，需要访问令牌",
			})
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

// OptionalJWTAuth 可选的JWT认证中间件，不强制要求认证
func (m *AuthMiddleware) OptionalJWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有认证令牌，继续处理请求
			c.Next()
			return
		}

		// 提取令牌
		tokenString, err := auth.ExtractTokenFromHeader(authHeader)
		if err != nil {
			// 令牌格式错误，但不阻止请求
			c.Next()
			return
		}

		// 验证令牌
		claims, err := m.jwtManager.VerifyToken(tokenString)
		if err != nil {
			// 令牌无效，但不阻止请求
			c.Next()
			return
		}

		// 检查令牌类型
		if claims.TokenType != "access" {
			// 令牌类型错误，但不阻止请求
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("claims", claims)
		c.Set("authenticated", true)

		c.Next()
	}
}

// RequireRole 要求特定角色的中间件
func (m *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		role, exists := c.Get("role")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "未认证的用户",
			})
			return
		}

		// 检查用户角色是否在允许的角色列表中
		userRole := role.(string)
		for _, r := range roles {
			if r == userRole {
				c.Next()
				return
			}
		}

		// 用户角色不在允许的角色列表中
		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
			"code":    config.ErrCodeForbidden,
			"message": "权限不足",
		})
	}
}

// RefreshToken 刷新令牌处理函数
func (m *AuthMiddleware) RefreshToken(c *gin.Context) {
	// 从请求头获取Authorization
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "未提供刷新令牌",
		})
		return
	}

	// 提取令牌
	tokenString, err := auth.ExtractTokenFromHeader(authHeader)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": err.Error(),
		})
		return
	}

	// 刷新令牌
	tokenPair, err := m.jwtManager.RefreshAccessToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    config.ErrCodeUnauthorized,
			"message": "刷新令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    config.ErrCodeSuccess,
		"message": "令牌刷新成功",
		"data":    tokenPair,
	})
}

// GetJWTManager 获取JWT管理器
func (m *AuthMiddleware) GetJWTManager() *auth.JWTManager {
	return m.jwtManager
}

// JWTAuthForPath 为特定路径提供JWT认证
func (m *AuthMiddleware) JWTAuthForPath(paths ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查当前路径是否需要认证
		path := c.Request.URL.Path
		requiresAuth := false

		for _, p := range paths {
			if strings.HasPrefix(path, p) {
				requiresAuth = true
				break
			}
		}

		if !requiresAuth {
			c.Next()
			return
		}

		// 执行JWT认证
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "未提供认证令牌",
			})
			return
		}

		// 提取令牌
		tokenString, err := auth.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": err.Error(),
			})
			return
		}

		// 验证令牌
		claims, err := m.jwtManager.VerifyToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "无效的认证令牌: " + err.Error(),
			})
			return
		}

		// 检查令牌类型
		if claims.TokenType != "access" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code":    config.ErrCodeUnauthorized,
				"message": "无效的令牌类型，需要访问令牌",
			})
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

func (m *AuthMiddleware) CasbinAuth() gin.HandlerFunc {

	return func(c *gin.Context) {
		// Get current user role from JWT token
		role, exists := c.Get("role")
		if !exists {
			log.Printf("Role not found in context")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
			})
			c.Abort()
			return
		}

		roleStr, ok := role.(string)
		if !ok {
			log.Printf("Role is not a string: %v", role)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid role format",
			})
			c.Abort()
			return
		}

		// Get request path and method
		obj := c.Request.URL.Path
		act := c.Request.Method

		// Debug: Print all policies for debugging
		enforcer := auth.GetEnforcer()
		// Debug: Print role inheritance
		allowed, err := enforcer.Enforce(roleStr, obj, act)

		if err != nil {
			log.Printf("Casbin enforce error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Authorization system error",
			})
			c.Abort()
			return
		}

		if !allowed {
			// log.Printf("Permission denied: role=%s, path=%s, method=%s", roleStr, obj, act)
			c.JSON(http.StatusForbidden, gin.H{
				"error":  "Permission denied",
				"role":   roleStr,
				"path":   obj,
				"method": act,
			})
			c.Abort()
			return
		}

		c.Next()
	}

}
