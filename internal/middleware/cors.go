package middleware

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// CORS 返回CORS中间件
func (m *MiddlewareManager) CORS() gin.HandlerFunc {
	corsConfig := cors.Config{
		AllowOrigins:     m.config.Middleware.CORS.AllowedOrigins,
		AllowMethods:     m.config.Middleware.CORS.AllowedMethods,
		AllowHeaders:     m.config.Middleware.CORS.AllowedHeaders,
		ExposeHeaders:    m.config.Middleware.CORS.ExposedHeaders,
		AllowCredentials: m.config.Middleware.CORS.AllowCredentials,
		MaxAge:           time.Duration(m.config.Middleware.CORS.MaxAge) * time.Second,
	}

	// 如果允许所有来源，使用AllowAllOrigins
	if len(corsConfig.AllowOrigins) == 1 && corsConfig.AllowOrigins[0] == "*" {
		corsConfig.AllowAllOrigins = true
		corsConfig.AllowOrigins = nil
	}

	return cors.New(corsConfig)
}

// DefaultCORS 返回默认CORS中间件
func DefaultCORS() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowAllOrigins:  true,
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           24 * time.Hour,
	})
}
