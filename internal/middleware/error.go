package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"beacon/cloud/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// APIError 自定义API错误
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	return e.Message
}

// NewAPIError 创建新的API错误
func NewAPIError(code int, message string, details ...string) *APIError {
	err := &APIError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool     `json:"success"`
	Error   APIError `json:"error"`
}

// ErrorHandler 返回错误处理中间件
func (m *MiddlewareManager) ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic日志
				logrus.WithFields(logrus.Fields{
					"error":  err,
					"stack":  string(debug.Stack()),
					"path":   c.Request.URL.Path,
					"method": c.Request.Method,
				}).Error("Panic recovered")

				// 返回内部服务器错误
				apiErr := NewAPIError(
					config.ErrCodeInternalError,
					config.ErrorMessages[config.ErrCodeInternalError],
				)

				if m.config.Middleware.ErrorHandler.ShowDetails {
					if errObj, ok := err.(error); ok {
						apiErr.Details = errObj.Error()
					} else {
						apiErr.Details = fmt.Sprintf("%v", err)
					}
				}

				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Success: false,
					Error:   *apiErr,
				})
				c.Abort()
			}
		}()

		c.Next()

		// 处理请求过程中的错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 检查是否是自定义API错误
			if apiErr, ok := err.Err.(*APIError); ok {
				statusCode := getHTTPStatusFromErrorCode(apiErr.Code)
				c.JSON(statusCode, ErrorResponse{
					Success: false,
					Error:   *apiErr,
				})
			} else {
				// 处理其他类型的错误
				apiErr := NewAPIError(
					config.ErrCodeInternalError,
					config.ErrorMessages[config.ErrCodeInternalError],
				)

				if m.config.Middleware.ErrorHandler.ShowDetails {
					apiErr.Details = err.Error()
				}

				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Success: false,
					Error:   *apiErr,
				})
			}
			c.Abort()
		}
	}
}

// DefaultErrorHandler 返回默认错误处理中间件
func DefaultErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				logrus.WithFields(logrus.Fields{
					"error":  err,
					"stack":  string(debug.Stack()),
					"path":   c.Request.URL.Path,
					"method": c.Request.Method,
				}).Error("Panic recovered")

				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Success: false,
					Error: APIError{
						Code:    config.ErrCodeInternalError,
						Message: config.ErrorMessages[config.ErrCodeInternalError],
					},
				})
				c.Abort()
			}
		}()

		c.Next()

		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			if apiErr, ok := err.Err.(*APIError); ok {
				statusCode := getHTTPStatusFromErrorCode(apiErr.Code)
				c.JSON(statusCode, ErrorResponse{
					Success: false,
					Error:   *apiErr,
				})
			} else {
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Success: false,
					Error: APIError{
						Code:    config.ErrCodeInternalError,
						Message: config.ErrorMessages[config.ErrCodeInternalError],
					},
				})
			}
			c.Abort()
		}
	}
}

// getHTTPStatusFromErrorCode 根据错误码获取HTTP状态码
func getHTTPStatusFromErrorCode(errorCode int) int {
	switch errorCode {
	case config.ErrCodeSuccess:
		return http.StatusOK
	case config.ErrCodeInvalidParams:
		return http.StatusBadRequest
	case config.ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case config.ErrCodeForbidden:
		return http.StatusForbidden
	case config.ErrCodeNotFound:
		return http.StatusNotFound
	case config.ErrCodeConflict:
		return http.StatusConflict
	case config.ErrCodeInternalError, config.ErrCodeDatabaseError, config.ErrCodeCacheError,
		config.ErrCodeMQTTError, config.ErrCodeEmailError, config.ErrCodeSMSError:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// AbortWithError 中止请求并返回错误
func AbortWithError(c *gin.Context, code int, message string, details ...string) {
	err := NewAPIError(code, message, details...)
	c.Error(err)
	c.Abort()
}

// AbortWithAPIError 中止请求并返回API错误
func AbortWithAPIError(c *gin.Context, err *APIError) {
	c.Error(err)
	c.Abort()
}
