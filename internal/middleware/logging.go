package middleware

import (
	"beacon/cloud/internal/model"
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// Logger 返回日志中间件 - 只存储到数据库，不打印控制台日志
func (m *MiddlewareManager) Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否跳过此路径
		for _, skipPath := range m.config.Middleware.Logging.SkipPaths {
			if c.Request.URL.Path == skipPath {
				c.Next()
				return
			}
		}

		start := time.Now()

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)
		statusCode := c.Writer.Status()

		// 异步存储详细日志到数据库
		if m.systemService != nil {
			go m.storeRequestLog(c, start, latency, statusCode)
		}
	}
}

// storeRequestLog 存储HTTP请求日志到数据库
func (m *MiddlewareManager) storeRequestLog(c *gin.Context, startTime time.Time, latency time.Duration, statusCode int) {
	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取用户ID（如果已认证）
	var userID string
	if uid, exists := c.Get("username"); exists {
		if uidStr, ok := uid.(string); ok {
			userID = uidStr
		}
	}

	// 确定日志级别
	var logLevel model.LogLevel
	switch {
	case statusCode >= 500:
		logLevel = model.LogLevelError
	case statusCode >= 400:
		logLevel = model.LogLevelWarn
	default:
		logLevel = model.LogLevelInfo
	}

	// 构建详细信息
	details := map[string]interface{}{
		"status_code": statusCode,
		"latency_ms":  latency.Milliseconds(),
		"method":      c.Request.Method,
		"path":        c.Request.URL.Path,
		"query":       c.Request.URL.RawQuery,
		"user_agent":  c.Request.UserAgent(),
		"referer":     c.Request.Referer(),
	}

	// 如果有错误，添加错误信息
	if len(c.Errors) > 0 {
		details["errors"] = c.Errors.String()
	}

	// 创建系统日志
	systemLog := &model.SystemLog{
		Level:     logLevel,
		Module:    model.LogModuleSystem,
		Action:    "http_request",
		Message:   fmt.Sprintf("%s %s - %d", c.Request.Method, c.Request.URL.Path, statusCode),
		UserID:    userID,
		IP:        c.ClientIP(),
		UserAgent: c.Request.UserAgent(),
		Details:   details,
		Timestamp: startTime,
	}

	// 异步存储到数据库
	if err := m.systemService.CreateLog(ctx, systemLog); err != nil {
		// 记录错误但不影响请求处理
		fmt.Printf("存储HTTP请求日志失败: %v\n", err)
	}
}
