package middleware

import (
	"beacon/cloud/internal/config"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// MiddlewareManager 中间件管理器
type MiddlewareManager struct {
	config        *config.Config
	redisClient   *redis.Client
	systemService service.SystemService
}

// NewMiddlewareManager 创建中间件管理器
func NewMiddlewareManager(cfg *config.Config, redisClient *redis.Client, systemService service.SystemService) *MiddlewareManager {
	return &MiddlewareManager{
		config:        cfg,
		redisClient:   redisClient,
		systemService: systemService,
	}
}

// SetupMiddlewares 设置所有中间件
func (m *MiddlewareManager) SetupMiddlewares(engine *gin.Engine) {
	// 错误处理中间件 - 最先注册，用于捕获所有错误
	if m.config.Middleware.ErrorHandler.Enabled {
		engine.Use(m.ErrorHandler())
	}

	// 日志中间件
	if m.config.Middleware.Logging.Enabled {
		engine.Use(m.Logger())
	}

	// CORS中间件
	if m.config.Middleware.CORS.Enabled {
		engine.Use(m.CORS())
	}

	// 限流中间件
	if m.config.Middleware.RateLimit.Enabled {
		engine.Use(m.RateLimit())
	}

	// 恢复中间件 - 最后注册，用于处理panic
	engine.Use(gin.Recovery())
}

// SetupMiddlewaresWithDefaults 使用默认配置设置中间件
func SetupMiddlewaresWithDefaults(engine *gin.Engine, cfg *config.Config, dbManager *database.DatabaseManager, systemService service.SystemService) {
	// 获取Redis客户端
	var redisClient *redis.Client
	if dbManager != nil && dbManager.GetRedis() != nil {
		redisClient = dbManager.GetRedis().GetClient()
	}

	manager := NewMiddlewareManager(cfg, redisClient, systemService)
	manager.SetupMiddlewares(engine)
}
