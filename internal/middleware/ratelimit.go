package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/ulule/limiter/v3"
	"github.com/ulule/limiter/v3/drivers/store/redis"
)

// RateLimit 返回限流中间件
func (m *MiddlewareManager) RateLimit() gin.HandlerFunc {
	// 创建Redis存储
	store, err := redis.NewStore(m.redisClient)
	if err != nil {
		logrus.WithError(err).Error("Failed to create Redis store for rate limiter")
		// 如果Redis不可用，返回一个空的中间件
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// 创建限流器
	rate := limiter.Rate{
		Period: time.Minute,
		Limit:  100, // 默认值
	}

	// 解析配置的限流率
	if m.config.Middleware.RateLimit.Rate != "" {
		if parsedRate, err := limiter.NewRateFromFormatted(m.config.Middleware.RateLimit.Rate); err == nil {
			rate = parsedRate
		} else {
			logrus.WithError(err).Warn("Failed to parse rate limit configuration, using default")
		}
	}

	// 创建限流器实例
	rateLimiter := limiter.New(store, rate, limiter.WithTrustForwardHeader(true))

	return func(c *gin.Context) {
		// 检查是否跳过此路径
		for _, skipPath := range m.config.Middleware.RateLimit.SkipPaths {
			if c.Request.URL.Path == skipPath {
				c.Next()
				return
			}
		}

		// 获取客户端IP作为限流键
		key := c.ClientIP()

		// 如果有用户认证信息，可以使用用户ID作为键
		if userID := c.GetString("user_id"); userID != "" {
			key = "user:" + userID
		}

		// 检查限流
		ctx := context.Background()
		limiterCtx, err := rateLimiter.Get(ctx, key)
		if err != nil {
			logrus.WithError(err).Error("Rate limiter error")
			c.Next()
			return
		}

		// 设置响应头
		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", limiterCtx.Limit))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", limiterCtx.Remaining))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", limiterCtx.Reset))

		// 检查是否超过限制
		if limiterCtx.Reached {
			logrus.WithFields(logrus.Fields{
				"client_ip": c.ClientIP(),
				"path":      c.Request.URL.Path,
				"method":    c.Request.Method,
				"key":       key,
			}).Warn("Rate limit exceeded")

			c.JSON(http.StatusTooManyRequests, ErrorResponse{
				Success: false,
				Error: APIError{
					Code:    http.StatusTooManyRequests,
					Message: "请求过于频繁，请稍后再试",
					Details: "Rate limit exceeded",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// DefaultRateLimit 返回默认限流中间件（基于内存）
func DefaultRateLimit() gin.HandlerFunc {
	// 使用内存存储的简单限流器
	clients := make(map[string]*clientInfo)

	return func(c *gin.Context) {
		key := c.ClientIP()
		now := time.Now()

		// 清理过期的客户端信息
		for k, v := range clients {
			if now.Sub(v.lastRequest) > time.Minute {
				delete(clients, k)
			}
		}

		// 获取或创建客户端信息
		client, exists := clients[key]
		if !exists {
			client = &clientInfo{
				requests:    0,
				lastRequest: now,
				resetTime:   now.Add(time.Minute),
			}
			clients[key] = client
		}

		// 检查是否需要重置计数器
		if now.After(client.resetTime) {
			client.requests = 0
			client.resetTime = now.Add(time.Minute)
		}

		// 检查限制
		if client.requests >= 100 { // 默认每分钟100次请求
			c.JSON(http.StatusTooManyRequests, ErrorResponse{
				Success: false,
				Error: APIError{
					Code:    http.StatusTooManyRequests,
					Message: "请求过于频繁，请稍后再试",
				},
			})
			c.Abort()
			return
		}

		// 增加请求计数
		client.requests++
		client.lastRequest = now

		// 设置响应头
		c.Header("X-RateLimit-Limit", "100")
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", 100-client.requests))
		c.Header("X-RateLimit-Reset", client.resetTime.Format(time.RFC3339))

		c.Next()
	}
}

// clientInfo 客户端信息
type clientInfo struct {
	requests    int
	lastRequest time.Time
	resetTime   time.Time
}

// RateLimitByUser 基于用户的限流中间件
func (m *MiddlewareManager) RateLimitByUser() gin.HandlerFunc {
	store, err := redis.NewStore(m.redisClient)
	if err != nil {
		logrus.WithError(err).Error("Failed to create Redis store for user rate limiter")
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// 为认证用户设置更高的限制
	rate := limiter.Rate{
		Period: time.Minute,
		Limit:  1000, // 认证用户每分钟1000次请求
	}

	rateLimiter := limiter.New(store, rate)

	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		if userID == "" {
			c.Next()
			return
		}

		key := "user_rate_limit:" + userID

		ctx := context.Background()
		limiterCtx, err := rateLimiter.Get(ctx, key)
		if err != nil {
			logrus.WithError(err).Error("User rate limiter error")
			c.Next()
			return
		}

		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", limiterCtx.Limit))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", limiterCtx.Remaining))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", limiterCtx.Reset))

		if limiterCtx.Reached {
			logrus.WithFields(logrus.Fields{
				"user_id": userID,
				"path":    c.Request.URL.Path,
				"method":  c.Request.Method,
			}).Warn("User rate limit exceeded")

			c.JSON(http.StatusTooManyRequests, ErrorResponse{
				Success: false,
				Error: APIError{
					Code:    http.StatusTooManyRequests,
					Message: "用户请求过于频繁，请稍后再试",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitByPath 基于路径的限流中间件
func (m *MiddlewareManager) RateLimitByPath(pathLimits map[string]string) gin.HandlerFunc {
	store, err := redis.NewStore(m.redisClient)
	if err != nil {
		logrus.WithError(err).Error("Failed to create Redis store for path rate limiter")
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// 为不同路径创建不同的限流器
	limiters := make(map[string]*limiter.Limiter)
	for path, rateStr := range pathLimits {
		if rate, err := limiter.NewRateFromFormatted(rateStr); err == nil {
			limiters[path] = limiter.New(store, rate)
		}
	}

	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// 查找匹配的路径限流器
		var rateLimiter *limiter.Limiter
		for pattern, limiterInstance := range limiters {
			if strings.HasPrefix(path, pattern) {
				rateLimiter = limiterInstance
				break
			}
		}

		if rateLimiter == nil {
			c.Next()
			return
		}

		key := "path_rate_limit:" + c.ClientIP() + ":" + path

		ctx := context.Background()
		limiterCtx, err := rateLimiter.Get(ctx, key)
		if err != nil {
			logrus.WithError(err).Error("Path rate limiter error")
			c.Next()
			return
		}

		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", limiterCtx.Limit))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", limiterCtx.Remaining))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", limiterCtx.Reset))

		if limiterCtx.Reached {
			c.JSON(http.StatusTooManyRequests, ErrorResponse{
				Success: false,
				Error: APIError{
					Code:    http.StatusTooManyRequests,
					Message: "路径访问过于频繁，请稍后再试",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
