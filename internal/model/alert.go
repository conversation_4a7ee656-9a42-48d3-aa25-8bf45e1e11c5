package model

import (
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AlertRuleType 预警规则类型
type AlertRuleType string

const (
	AlertRuleTypeThreshold AlertRuleType = "threshold" // 阈值预警
	AlertRuleTypeRate      AlertRuleType = "rate"      // 变化率预警
	AlertRuleTypeMissing   AlertRuleType = "missing"   // 缺失数据预警
	AlertRuleTypeAnomaly   AlertRuleType = "anomaly"   // 异常值预警
	AlertRuleTypeComposite AlertRuleType = "composite" // 复合条件预警
)

// AlertLevel 预警级别
type AlertLevel int

const (
	AlertLevelInfo     AlertLevel = 1 // 信息
	AlertLevelWarning  AlertLevel = 2 // 警告
	AlertLevelError    AlertLevel = 3 // 错误
	AlertLevelCritical AlertLevel = 4 // 严重
	AlertLevelFatal    AlertLevel = 5 // 灾难
)

// DataType 数据类型
type DataType string

const (
	DataTypeNumber DataType = "number" // 数值型
	DataTypeString DataType = "string" // 字符串型
	DataTypeBool   DataType = "bool"   // 布尔型
)

// Operator 操作符
type Operator string

const (
	// 数值型操作符
	OperatorGT  Operator = ">"  // 大于
	OperatorLT  Operator = "<"  // 小于
	OperatorGTE Operator = ">=" // 大于等于
	OperatorLTE Operator = "<=" // 小于等于
	OperatorEQ  Operator = "==" // 等于
	OperatorNE  Operator = "!=" // 不等于

	// 字符串型操作符
	OperatorEquals     Operator = "equals"     // 等于
	OperatorContains   Operator = "contains"   // 包含
	OperatorStartsWith Operator = "startsWith" // 开始于
	OperatorEndsWith   Operator = "endsWith"   // 结束于
)

// AlertCondition 预警条件
type AlertCondition struct {
	Field    string   `bson:"field" json:"field"`         // 监控字段
	Operator Operator `bson:"operator" json:"operator"`   // 操作符
	Value    string   `bson:"value" json:"value"`         // 阈值(统一用字符串存储)
	DataType DataType `bson:"data_type" json:"data_type"` // 数据类型
}

// FrequencyLimit 频率限制
type FrequencyLimit struct {
	MaxCount   int `bson:"max_count" json:"max_count"`     // 最大次数
	TimeWindow int `bson:"time_window" json:"time_window"` // 时间窗口(秒)
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Enabled    bool     `bson:"enabled" json:"enabled"`       // 是否启用通知
	Channels   []string `bson:"channels" json:"channels"`     // 通知渠道
	Recipients []string `bson:"recipients" json:"recipients"` // 接收人列表
	Template   string   `bson:"template" json:"template"`     // 通知模板
}

// AlertRule 预警规则模型
type AlertRule struct {
	ID           primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	GroupID      string             `bson:"group_id" json:"group_id"`         // 分组ID
	Topic        string             `bson:"topic" json:"topic"`               // Topic名称
	RuleName     string             `bson:"rule_name" json:"rule_name"`       // 规则名称
	RuleType     AlertRuleType      `bson:"rule_type" json:"rule_type"`       // 规则类型
	Description  string             `bson:"description" json:"description"`   // 规则描述
	Conditions   []AlertCondition   `bson:"conditions" json:"conditions"`     // 预警条件
	Level        AlertLevel         `bson:"level" json:"level"`               // 预警级别
	Notification NotificationConfig `bson:"notification" json:"notification"` // 通知配置
	Enabled      bool               `bson:"enabled" json:"enabled"`           // 是否启用
	CreatedBy    string             `bson:"created_by" json:"created_by"`     // 创建者
	CreatedAt    time.Time          `bson:"created_at" json:"created_at"`     // 创建时间
	UpdatedAt    time.Time          `bson:"updated_at" json:"updated_at"`     // 更新时间
}

// AlertRecord 预警记录模型(对应TDengine)
type AlertRecord struct {
	Timestamp    time.Time  `json:"timestamp"`     // 预警时间
	RuleID       string     `json:"rule_id"`       // 规则ID
	TopicName    string     `json:"topic_name"`    // Topic名称
	TriggerValue string     `json:"trigger_value"` // 触发值
	Message      string     `json:"message"`       // 预警消息
	Level        AlertLevel `json:"level"`         // 预警级别
	GroupID      string     `json:"group_id"`      // 分组ID(Tag)
	RuleType     string     `json:"rule_type"`     // 规则类型(Tag)
}

// BeforeCreate 创建前的钩子
func (ar *AlertRule) BeforeCreate() {
	if ar.ID.IsZero() {
		ar.ID = primitive.NewObjectID()
	}
	now := time.Now()
	ar.CreatedAt = now
	ar.UpdatedAt = now
}

// BeforeUpdate 更新前的钩子
func (ar *AlertRule) BeforeUpdate() {
	ar.UpdatedAt = time.Now()
}

// ToResponse 转换为响应格式
func (ar *AlertRule) ToResponse() AlertRuleResponse {
	return AlertRuleResponse{
		ID:           ar.ID.Hex(),
		GroupID:      ar.GroupID,
		Topic:        ar.Topic,
		RuleName:     ar.RuleName,
		RuleType:     ar.RuleType,
		Description:  ar.Description,
		Conditions:   ar.Conditions,
		Level:        ar.Level,
		Notification: ar.Notification,
		Enabled:      ar.Enabled,
		CreatedBy:    ar.CreatedBy,
		CreatedAt:    ar.CreatedAt,
		UpdatedAt:    ar.UpdatedAt,
	}
}

// AlertRuleResponse 预警规则响应
type AlertRuleResponse struct {
	ID           string             `json:"id"`
	GroupID      string             `json:"group_id"`
	Topic        string             `json:"topic"`
	RuleName     string             `json:"rule_name"`
	RuleType     AlertRuleType      `json:"rule_type"`
	Description  string             `json:"description"`
	Conditions   []AlertCondition   `json:"conditions"`
	Level        AlertLevel         `json:"level"`
	Notification NotificationConfig `json:"notification"`
	Enabled      bool               `json:"enabled"`
	CreatedBy    string             `json:"created_by"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
}

// CreateAlertRuleRequest 创建预警规则请求
type CreateAlertRuleRequest struct {
	GroupID      string             `json:"group_id" binding:"required"`
	Topic        string             `json:"topic" binding:"required"`
	RuleName     string             `json:"rule_name" binding:"required"`
	RuleType     AlertRuleType      `json:"rule_type" binding:"required"`
	Description  string             `json:"description"`
	Conditions   []AlertCondition   `json:"conditions" binding:"required"`
	Level        AlertLevel         `json:"level" binding:"required"`
	Notification NotificationConfig `json:"notification"`
	Enabled      bool               `json:"enabled"`
}

// UpdateAlertRuleRequest 更新预警规则请求
type UpdateAlertRuleRequest struct {
	RuleName     string             `json:"rule_name"`
	RuleType     AlertRuleType      `json:"rule_type"`
	Description  string             `json:"description"`
	Conditions   []AlertCondition   `json:"conditions"`
	Level        AlertLevel         `json:"level"`
	Notification NotificationConfig `json:"notification"`
	Enabled      *bool              `json:"enabled"` // 使用指针以区分false和未设置
}

// AlertRuleQuery 预警规则查询参数
type AlertRuleQuery struct {
	GroupID  string        `json:"group_id"`
	Topic    string        `json:"topic"`
	RuleType AlertRuleType `json:"rule_type"`
	Enabled  *bool         `json:"enabled"`
	Level    AlertLevel    `json:"level"`
}

// AlertRecordQuery 预警记录查询参数
type AlertRecordQuery struct {
	GroupID   string     `json:"group_id"`
	Topic     string     `json:"topic"`
	RuleID    string     `json:"rule_id"`
	Level     AlertLevel `json:"level"`
	TimeRange string     `json:"time_range"`
	Page      int        `json:"page"`
	PageSize  int        `json:"page_size"`
}

// TopicAlertCount Topic预警统计
type TopicAlertCount struct {
	Topic string `json:"topic"`
	Count int64  `json:"count"`
}

// 错误定义
var (
	ErrAlertRuleNotFound     = errors.New("预警规则不存在")
	ErrAlertRuleExists       = errors.New("预警规则已存在")
	ErrInvalidAlertCondition = errors.New("无效的预警条件")
	ErrInvalidAlertLevel     = errors.New("无效的预警级别")
)
