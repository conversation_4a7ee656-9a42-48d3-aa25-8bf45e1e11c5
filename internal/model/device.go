package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DeviceStatus 设备状态枚举
type DeviceStatus string

const (
	DeviceStatusActive      DeviceStatus = "active"      // 活跃
	DeviceStatusInactive    DeviceStatus = "inactive"    // 非活跃
	DeviceStatusMaintenance DeviceStatus = "maintenance" // 维护中
)

// DeviceAssignment 设备分配状态枚举
type DeviceAssignment string

const (
	DeviceAssignmentUnassigned DeviceAssignment = "unassigned" // 未分配
	DeviceAssignmentAssigned   DeviceAssignment = "assigned"   // 已分配
)

// Device 设备模型
type Device struct {
	ID           primitive.ObjectID  `bson:"_id,omitempty" json:"id"`
	DeviceType   string              `bson:"device_type" json:"device_type"`
	DeviceName   string              `bson:"device_name" json:"device_name"`
	SerialNumber string              `bson:"serial_number" json:"serial_number"`
	IMEICode     string              `bson:"imei_code" json:"imei_code"`
	UserID       *primitive.ObjectID `bson:"user_id,omitempty" json:"user_id,omitempty"` // 可选，设备分配给用户时才有值
	Status       DeviceStatus        `bson:"status" json:"status"`
	Assignment   DeviceAssignment    `bson:"assignment" json:"assignment"` // 设备分配状态
	CreatedAt    time.Time           `bson:"created_at" json:"created_at"`
	UpdatedAt    time.Time           `bson:"updated_at" json:"updated_at"`
}

// DeviceType 设备类型模型
type DeviceType struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Name        string             `bson:"name" json:"name"`
	Description string             `bson:"description" json:"description"`
	CreatedAt   time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at" json:"updated_at"`
}

// CreateDeviceRequest 创建设备请求（管理员用）
type CreateDeviceRequest struct {
	DeviceType   string `json:"device_type" binding:"required"`
	DeviceName   string `json:"device_name" binding:"required"`
	SerialNumber string `json:"serial_number" binding:"required"`
	IMEICode     string `json:"imei_code" binding:"required"`
}

// AssignDeviceRequest 用户添加设备请求
type AssignDeviceRequest struct {
	SerialNumber string `json:"serial_number" binding:"required"`
	IMEICode     string `json:"imei_code" binding:"required"`
}

// UpdateDeviceRequest 更新设备请求
type UpdateDeviceRequest struct {
	DeviceType   string       `json:"device_type,omitempty"`
	DeviceName   string       `json:"device_name,omitempty"`
	SerialNumber string       `json:"serial_number,omitempty"`
	IMEICode     string       `json:"imei_code,omitempty"`
	Status       DeviceStatus `json:"status,omitempty"`
}

// CreateDeviceTypeRequest 创建设备类型请求
type CreateDeviceTypeRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description,omitempty"`
}

// UpdateDeviceTypeRequest 更新设备类型请求
type UpdateDeviceTypeRequest struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
}

// DeviceResponse 设备响应
type DeviceResponse struct {
	ID           string           `json:"id"`
	DeviceType   string           `json:"device_type"`
	DeviceName   string           `json:"device_name"`
	SerialNumber string           `json:"serial_number"`
	IMEICode     string           `json:"imei_code"`
	UserID       *string          `json:"user_id,omitempty"`
	Status       DeviceStatus     `json:"status"`
	Assignment   DeviceAssignment `json:"assignment"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
}

// DeviceTypeResponse 设备类型响应
type DeviceTypeResponse struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (d *Device) ToResponse() DeviceResponse {
	var userID *string
	if d.UserID != nil {
		userIDStr := d.UserID.Hex()
		userID = &userIDStr
	}

	return DeviceResponse{
		ID:           d.ID.Hex(),
		DeviceType:   d.DeviceType,
		DeviceName:   d.DeviceName,
		SerialNumber: d.SerialNumber,
		IMEICode:     d.IMEICode,
		UserID:       userID,
		Status:       d.Status,
		Assignment:   d.Assignment,
		CreatedAt:    d.CreatedAt,
		UpdatedAt:    d.UpdatedAt,
	}
}

// ToResponse 转换为响应格式
func (dt *DeviceType) ToResponse() DeviceTypeResponse {
	return DeviceTypeResponse{
		ID:          dt.ID.Hex(),
		Name:        dt.Name,
		Description: dt.Description,
		CreatedAt:   dt.CreatedAt,
		UpdatedAt:   dt.UpdatedAt,
	}
}

// Validate 验证设备数据
func (d *Device) Validate() error {
	if d.DeviceType == "" {
		return ErrInvalidDeviceType
	}
	if d.DeviceName == "" {
		return ErrInvalidDeviceName
	}
	if d.SerialNumber == "" {
		return ErrInvalidSerialNumber
	}
	if d.IMEICode == "" {
		return ErrInvalidIMEICode
	}
	if d.Status != DeviceStatusActive && d.Status != DeviceStatusInactive && d.Status != DeviceStatusMaintenance {
		return ErrInvalidDeviceStatus
	}
	return nil
}

// Validate 验证设备类型数据
func (dt *DeviceType) Validate() error {
	if dt.Name == "" {
		return ErrInvalidDeviceTypeName
	}
	return nil
}

// BeforeCreate 创建前的钩子
func (d *Device) BeforeCreate() {
	now := time.Now()
	d.CreatedAt = now
	d.UpdatedAt = now
	if d.Status == "" {
		d.Status = DeviceStatusActive
	}
	if d.Assignment == "" {
		d.Assignment = DeviceAssignmentUnassigned
	}
}

// BeforeUpdate 更新前的钩子
func (d *Device) BeforeUpdate() {
	d.UpdatedAt = time.Now()
}

// BeforeCreate 创建前的钩子
func (dt *DeviceType) BeforeCreate() {
	now := time.Now()
	dt.CreatedAt = now
	dt.UpdatedAt = now
}

// BeforeUpdate 更新前的钩子
func (dt *DeviceType) BeforeUpdate() {
	dt.UpdatedAt = time.Now()
}

// IsActive 检查设备是否活跃
func (d *Device) IsActive() bool {
	return d.Status == DeviceStatusActive
}

// IsAssigned 检查设备是否已分配
func (d *Device) IsAssigned() bool {
	return d.Assignment == DeviceAssignmentAssigned
}

// AssignToUser 将设备分配给用户
func (d *Device) AssignToUser(userID primitive.ObjectID) {
	d.UserID = &userID
	d.Assignment = DeviceAssignmentAssigned
	d.BeforeUpdate()
}

// Unassign 取消设备分配
func (d *Device) Unassign() {
	d.UserID = nil
	d.Assignment = DeviceAssignmentUnassigned
	d.BeforeUpdate()
}

// 预定义的设备类型
var PredefinedDeviceTypes = []DeviceType{
	{
		Name:        "BC-3GM-R",
		Description: "BC-3GM-R 设备类型",
	},
	{
		Name:        "BC-4GM-C",
		Description: "BC-4GM-C 设备类型",
	},
	{
		Name:        "BC-ECR-C",
		Description: "BC-ECR-C 设备类型",
	},
	{
		Name:        "BGTR",
		Description: "BGTR 设备类型",
	},
}
