package model

import "errors"

// 用户相关错误
var (
	ErrUserNotFound            = errors.New("用户不存在")
	ErrUserAlreadyExists       = errors.New("用户已存在")
	ErrInvalidUsername         = errors.New("无效的用户名")
	ErrInvalidEmail            = errors.New("无效的邮箱地址")
	ErrInvalidPassword         = errors.New("无效的密码")
	ErrInvalidRole             = errors.New("无效的用户角色")
	ErrInvalidStatus           = errors.New("无效的用户状态")
	ErrPasswordMismatch        = errors.New("密码不匹配")
	ErrUserInactive            = errors.New("用户未激活")
	ErrUserBanned              = errors.New("用户已被禁用")
	ErrInsufficientPermissions = errors.New("权限不足")
)

// 分组相关错误
var (
	ErrGroupNotFound               = errors.New("分组不存在")
	ErrGroupAlreadyExists          = errors.New("分组已存在")
	ErrInvalidGroupName            = errors.New("无效的分组名称")
	ErrNotGroupCreator             = errors.New("不是分组创建者")
	ErrNotGroupMember              = errors.New("不是分组成员")
	ErrAlreadyGroupMember          = errors.New("已是分组成员")
	ErrGroupInactive               = errors.New("分组未激活")
	ErrGroupMemberLimitReached     = errors.New("分组成员数量已达上限")
	ErrCannotRemoveCreator         = errors.New("不能移除分组创建者")
	ErrCannotLeaveAsCreator        = errors.New("创建者不能退出分组")
	ErrGroupLimitReached           = errors.New("用户创建的分组数量已达上限")
	ErrJoinRequestNotFound         = errors.New("加入申请不存在")
	ErrJoinRequestAlreadyExists    = errors.New("加入申请已存在")
	ErrJoinRequestAlreadyProcessed = errors.New("加入申请已被处理")
)

// Topic相关错误
var (
	ErrTopicNotFound         = errors.New("Topic不存在")
	ErrTopicAlreadyExists    = errors.New("Topic已存在")
	ErrInvalidTopicName      = errors.New("无效的Topic名称")
	ErrTopicPermissionDenied = errors.New("Topic权限被拒绝")
)

// 设备相关错误
var (
	ErrDeviceNotFound          = errors.New("设备不存在")
	ErrDeviceAlreadyExists     = errors.New("设备已存在")
	ErrInvalidDeviceType       = errors.New("无效的设备类型")
	ErrInvalidDeviceName       = errors.New("无效的设备名称")
	ErrInvalidSerialNumber     = errors.New("无效的序列号")
	ErrInvalidIMEICode         = errors.New("无效的IMEI码")
	ErrInvalidDeviceStatus     = errors.New("无效的设备状态")
	ErrSerialNumberExists      = errors.New("序列号已存在")
	ErrIMEICodeExists          = errors.New("IMEI码已存在")
	ErrDeviceTypeNotFound      = errors.New("设备类型不存在")
	ErrDeviceTypeAlreadyExists = errors.New("设备类型已存在")
	ErrInvalidDeviceTypeName   = errors.New("无效的设备类型名称")
	ErrDeviceAlreadyAssigned   = errors.New("设备已被分配")
	ErrDeviceNotAssigned       = errors.New("设备未分配")
	ErrDeviceAssignedToOther   = errors.New("设备已分配给其他用户")
	ErrCannotAssignOwnDevice   = errors.New("不能分配已属于自己的设备")
)

// 通用错误
var (
	ErrInvalidID        = errors.New("无效的ID")
	ErrInvalidRequest   = errors.New("无效的请求")
	ErrDatabaseError    = errors.New("数据库错误")
	ErrInternalError    = errors.New("内部错误")
	ErrUnauthorized     = errors.New("未授权")
	ErrForbidden        = errors.New("禁止访问")
	ErrPermissionDenied = errors.New("权限被拒绝")
	ErrNotImplemented   = errors.New("功能未实现")
)
