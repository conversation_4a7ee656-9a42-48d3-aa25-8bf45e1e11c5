package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GroupRole 分组角色枚举
type GroupRole string

const (
	GroupRoleCreator GroupRole = "creator" // 分组创建者
	GroupRoleMember  GroupRole = "member"  // 分组成员
)

// GroupStatus 分组状态枚举
type GroupStatus string

const (
	GroupStatusActive   GroupStatus = "active"   // 活跃
	GroupStatusInactive GroupStatus = "inactive" // 非活跃
	GroupStatusArchived GroupStatus = "archived" // 已归档
)

// JoinRequestStatus 加入申请状态
type JoinRequestStatus string

const (
	JoinRequestPending  JoinRequestStatus = "pending"  // 待处理
	JoinRequestApproved JoinRequestStatus = "approved" // 已批准
	JoinRequestRejected JoinRequestStatus = "rejected" // 已拒绝
)

// Group 分组模型
type Group struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	GroupID     string             `bson:"group_id" json:"group_id"`                           // 分组唯一业务ID
	Name        string             `bson:"name" json:"name"`                                   // 分组名称
	Description string             `bson:"description,omitempty" json:"description,omitempty"` // 分组描述
	CreatorID   string             `bson:"creator_id" json:"creator_id"`                       // 创建者用户ID
	Status      GroupStatus        `bson:"status" json:"status"`                               // 分组状态
	MaxMembers  int                `bson:"max_members" json:"max_members"`                     // 最大成员数
	CreatedAt   time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at" json:"updated_at"`
}

// GroupMember 分组成员模型
type GroupMember struct {
	ID       primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	GroupID  string             `bson:"group_id" json:"group_id"`   // 分组ID
	UserID   string             `bson:"user_id" json:"user_id"`     // 用户ID
	Role     GroupRole          `bson:"role" json:"role"`           // 成员角色
	JoinedAt time.Time          `bson:"joined_at" json:"joined_at"` // 加入时间
}

// GroupJoinRequest 分组加入申请模型
type GroupJoinRequest struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	GroupID    string             `bson:"group_id" json:"group_id"`                   // 分组ID
	UserID     string             `bson:"user_id" json:"user_id"`                     // 申请用户ID
	OwnerID    string             `bson:"owner_id" json:"owner_id"`                   // 分组创建者ID（优化查询性能）
	Status     JoinRequestStatus  `bson:"status" json:"status"`                       // 申请状态
	Message    string             `bson:"message,omitempty" json:"message,omitempty"` // 申请消息
	CreatedAt  time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt  time.Time          `bson:"updated_at" json:"updated_at"`
	ReviewedBy string             `bson:"reviewed_by,omitempty" json:"reviewed_by,omitempty"` // 审核人ID
	ReviewedAt *time.Time         `bson:"reviewed_at,omitempty" json:"reviewed_at,omitempty"` // 审核时间
}

// Topic Topic模型
type Topic struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	GroupID   string             `bson:"group_id" json:"group_id"`     // 所属分组
	TopicName string             `bson:"topic_name" json:"topic_name"` // 原始名称，如 temp
	FullName  string             `bson:"full_name" json:"full_name"`   // 组合名称，如 group1/temp（唯一）
	CreatorID string             `bson:"creator_id" json:"creator_id"` // 创建者ID
	CreatedAt time.Time          `bson:"created_at" json:"created_at"`
}

// TopicPermission Topic权限模型
type TopicPermission struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	UserID    string             `bson:"user_id" json:"user_id"`       // 用户ID
	GroupID   string             `bson:"group_id" json:"group_id"`     // 分组ID
	FullTopic string             `bson:"full_topic" json:"full_topic"` // 完整Topic名称，如 group1/temp
	CanPub    bool               `bson:"can_pub" json:"can_pub"`       // 发布权限
	CanSub    bool               `bson:"can_sub" json:"can_sub"`       // 订阅权限
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"`
}

// CreateGroupRequest 创建分组请求
type CreateGroupRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=50"`
	Description string `json:"description,omitempty" binding:"max=200"`
	MaxMembers  int    `json:"max_members,omitempty" binding:"min=1,max=1000"`
}

// UpdateGroupRequest 更新分组请求
type UpdateGroupRequest struct {
	Name        string      `json:"name,omitempty" binding:"omitempty,min=1,max=50"`
	Description string      `json:"description,omitempty" binding:"max=200"`
	Status      GroupStatus `json:"status,omitempty" binding:"omitempty,oneof=active inactive archived"`
	MaxMembers  int         `json:"max_members,omitempty" binding:"omitempty,min=1,max=1000"`
}

// JoinGroupRequest 加入分组请求
type JoinGroupRequest struct {
	GroupID string `json:"group_id" binding:"required"`
	Message string `json:"message,omitempty" binding:"max=200"`
}

// ReviewJoinRequestRequest 审核加入申请请求
type ReviewJoinRequestRequest struct {
	Status JoinRequestStatus `json:"status" binding:"required,oneof=approved rejected"`
}

// CreateTopicRequest 创建Topic请求
type CreateTopicRequest struct {
	TopicName string `json:"topic_name" binding:"required,min=1,max=100"`
}

// SetTopicPermissionRequest 设置Topic权限请求
type SetTopicPermissionRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	FullTopic string `json:"full_topic" binding:"required"`
	CanPub    bool   `json:"can_pub"`
	CanSub    bool   `json:"can_sub"`
}

// GroupResponse 分组响应
type GroupResponse struct {
	ID          string      `json:"id"`
	GroupID     string      `json:"group_id"`
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	CreatorID   string      `json:"creator_id"`
	Status      GroupStatus `json:"status"`
	MaxMembers  int         `json:"max_members"`
	MemberCount int         `json:"member_count"`
	TopicCount  int         `json:"topic_count"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// GroupMemberResponse 分组成员响应
type GroupMemberResponse struct {
	ID       string    `json:"id"`
	GroupID  string    `json:"group_id"`
	UserID   string    `json:"user_id"`
	Role     GroupRole `json:"role"`
	JoinedAt time.Time `json:"joined_at"`
	// 用户信息
	Email       string `json:"email,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
}

// TopicResponse Topic响应
type TopicResponse struct {
	ID        string    `json:"id"`
	GroupID   string    `json:"group_id"`
	TopicName string    `json:"topic_name"`
	FullName  string    `json:"full_name"`
	CreatorID string    `json:"creator_id"`
	CreatedAt time.Time `json:"created_at"`
}

// TopicPermissionResponse Topic权限响应
type TopicPermissionResponse struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	GroupID   string    `json:"group_id"`
	FullTopic string    `json:"full_topic"`
	CanPub    bool      `json:"can_pub"`
	CanSub    bool      `json:"can_sub"`
	UpdatedAt time.Time `json:"updated_at"`
	// 用户信息
	Email       string `json:"email,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
}

// JoinRequestResponse 加入申请响应
type JoinRequestResponse struct {
	ID         string            `json:"id"`
	GroupID    string            `json:"group_id"`
	UserID     string            `json:"user_id"`
	Status     JoinRequestStatus `json:"status"`
	Message    string            `json:"message,omitempty"`
	CreatedAt  time.Time         `json:"created_at"`
	UpdatedAt  time.Time         `json:"updated_at"`
	ReviewedBy string            `json:"reviewed_by,omitempty"`
	ReviewedAt *time.Time        `json:"reviewed_at,omitempty"`
	// 用户信息
	Email       string `json:"email,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	// 分组信息
	GroupName string `json:"group_name,omitempty"`
}

// ToResponse 转换为响应格式
func (g *Group) ToResponse() GroupResponse {
	return GroupResponse{
		ID:          g.ID.Hex(),
		GroupID:     g.GroupID,
		Name:        g.Name,
		Description: g.Description,
		CreatorID:   g.CreatorID,
		Status:      g.Status,
		MaxMembers:  g.MaxMembers,
		CreatedAt:   g.CreatedAt,
		UpdatedAt:   g.UpdatedAt,
	}
}

// ToResponse 转换为响应格式
func (gm *GroupMember) ToResponse() GroupMemberResponse {
	return GroupMemberResponse{
		ID:       gm.ID.Hex(),
		GroupID:  gm.GroupID,
		UserID:   gm.UserID,
		Role:     gm.Role,
		JoinedAt: gm.JoinedAt,
	}
}

// ToResponse 转换为响应格式
func (t *Topic) ToResponse() TopicResponse {
	return TopicResponse{
		ID:        t.ID.Hex(),
		GroupID:   t.GroupID,
		TopicName: t.TopicName,
		FullName:  t.FullName,
		CreatorID: t.CreatorID,
		CreatedAt: t.CreatedAt,
	}
}

// ToResponse 转换为响应格式
func (tp *TopicPermission) ToResponse() TopicPermissionResponse {
	return TopicPermissionResponse{
		ID:        tp.ID.Hex(),
		UserID:    tp.UserID,
		GroupID:   tp.GroupID,
		FullTopic: tp.FullTopic,
		CanPub:    tp.CanPub,
		CanSub:    tp.CanSub,
		UpdatedAt: tp.UpdatedAt,
	}
}

// ToResponse 转换为响应格式
func (jr *GroupJoinRequest) ToResponse() JoinRequestResponse {
	return JoinRequestResponse{
		ID:         jr.ID.Hex(),
		GroupID:    jr.GroupID,
		UserID:     jr.UserID,
		Status:     jr.Status,
		Message:    jr.Message,
		CreatedAt:  jr.CreatedAt,
		UpdatedAt:  jr.UpdatedAt,
		ReviewedBy: jr.ReviewedBy,
		ReviewedAt: jr.ReviewedAt,
	}
}

// IsCreator 检查是否为分组创建者
func (g *Group) IsCreator(userID string) bool {
	return g.CreatorID == userID
}

// IsActive 检查分组是否活跃
func (g *Group) IsActive() bool {
	return g.Status == GroupStatusActive
}

// BeforeCreate 创建前的钩子
func (g *Group) BeforeCreate() {
	now := time.Now()
	g.CreatedAt = now
	g.UpdatedAt = now
	if g.Status == "" {
		g.Status = GroupStatusActive
	}
	if g.MaxMembers == 0 {
		g.MaxMembers = 100 // 默认最大成员数
	}
}

// BeforeUpdate 更新前的钩子
func (g *Group) BeforeUpdate() {
	g.UpdatedAt = time.Now()
}

// BeforeCreate 创建前的钩子
func (gm *GroupMember) BeforeCreate() {
	gm.JoinedAt = time.Now()
}

// BeforeCreate 创建前的钩子
func (jr *GroupJoinRequest) BeforeCreate() {
	now := time.Now()
	jr.CreatedAt = now
	jr.UpdatedAt = now
	if jr.Status == "" {
		jr.Status = JoinRequestPending
	}
}

// BeforeUpdate 更新前的钩子
func (jr *GroupJoinRequest) BeforeUpdate() {
	jr.UpdatedAt = time.Now()
}

// BeforeCreate 创建前的钩子
func (t *Topic) BeforeCreate() {
	t.CreatedAt = time.Now()
}

// BeforeUpdate 更新前的钩子
func (tp *TopicPermission) BeforeUpdate() {
	tp.UpdatedAt = time.Now()
}

// Validate 验证分组数据
func (g *Group) Validate() error {
	if g.Name == "" {
		return ErrInvalidGroupName
	}
	if g.CreatorID == "" {
		return ErrInvalidID
	}
	if g.MaxMembers <= 0 {
		return ErrInvalidRequest
	}
	return nil
}

// Validate 验证Topic数据
func (t *Topic) Validate() error {
	if t.TopicName == "" {
		return ErrInvalidTopicName
	}
	if t.GroupID == "" {
		return ErrInvalidID
	}
	if t.CreatorID == "" {
		return ErrInvalidID
	}
	return nil
}
