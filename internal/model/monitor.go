package model

import (
	"time"

	"github.com/mochi-mqtt/server/v2/system"
)

// SystemMetrics 系统指标模型
type SystemMetrics struct {
	ID              string    `json:"id" bson:"_id,omitempty"`
	Timestamp       time.Time `json:"timestamp" bson:"timestamp"`
	CPUUsage        float64   `json:"cpu_usage" bson:"cpu_usage"`
	MemoryUsage     int64     `json:"memory_usage" bson:"memory_usage"`
	MemoryTotal     int64     `json:"memory_total" bson:"memory_total"`
	DiskUsage       int64     `json:"disk_usage" bson:"disk_usage"`
	DiskTotal       int64     `json:"disk_total" bson:"disk_total"`
	NetworkInBytes  int64     `json:"network_in_bytes" bson:"network_in_bytes"`
	NetworkOutBytes int64     `json:"network_out_bytes" bson:"network_out_bytes"`
	LoadAverage     float64   `json:"load_average" bson:"load_average"`
	GoroutineCount  int64     `json:"goroutine_count" bson:"goroutine_count"`
	HeapSize        int64     `json:"heap_size" bson:"heap_size"`
	HeapUsed        int64     `json:"heap_used" bson:"heap_used"`
}

type PerformanceMetrics struct {
	ID               string    `json:"id" bson:"_id,omitempty"`
	Timestamp        time.Time `json:"timestamp" bson:"timestamp"`
	ClientsCount     int64     `json:"clients_count" bson:"clients_count"`
	Subscriptions    int64     `json:"subscriptions" bson:"subscriptions"`
	MessagesSent     int64     `json:"messages_sent" bson:"messages_sent"`
	MessagesReceived int64     `json:"messages_received" bson:"messages_received"`
	BytesSent        int64     `json:"bytes_sent" bson:"bytes_sent"`
	BytesReceived    int64     `json:"bytes_received" bson:"bytes_received"`
	Uptime           float64   `json:"uptime" bson:"uptime"`
	MemoryUsage      int64     `json:"memory_usage" bson:"memory_usage"`
	NetworkInBytes   int64     `json:"network_in_bytes" bson:"network_in_bytes"`
	NetworkOutBytes  int64     `json:"network_out_bytes" bson:"network_out_bytes"`
}

// MonitoringAlert 监控告警模型
type MonitoringAlert struct {
	ID          string     `json:"id" bson:"_id,omitempty"`
	AlertType   string     `json:"alert_type" bson:"alert_type"` // connection, performance, error
	Severity    string     `json:"severity" bson:"severity"`     // low, medium, high, critical
	Title       string     `json:"title" bson:"title"`
	Description string     `json:"description" bson:"description"`
	Timestamp   time.Time  `json:"timestamp" bson:"timestamp"`
	Resolved    bool       `json:"resolved" bson:"resolved"`
	ResolvedAt  *time.Time `json:"resolved_at,omitempty" bson:"resolved_at,omitempty"`
	GroupID     string     `json:"group_id,omitempty" bson:"group_id,omitempty"`
	ClientID    string     `json:"client_id,omitempty" bson:"client_id,omitempty"`
	Topic       string     `json:"topic,omitempty" bson:"topic,omitempty"`
	Threshold   float64    `json:"threshold,omitempty" bson:"threshold,omitempty"`
	ActualValue float64    `json:"actual_value,omitempty" bson:"actual_value,omitempty"`
}

// DashboardData 监控面板数据模型
type DashboardData struct {
	Timestamp     time.Time            `json:"timestamp"`
	ServerInfo    *system.Info         `json:"server_info"`
	SystemMetrics *SystemMetricsDetail `json:"system_metrics"`
}

// MonitorQuery 监控查询参数
type MonitorQuery struct {
	GroupID   string    `json:"group_id,omitempty"`
	ClientID  string    `json:"client_id,omitempty"`
	Topic     string    `json:"topic,omitempty"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Interval  string    `json:"interval,omitempty"` // 1m, 5m, 15m, 30m, 1h, 6h, 12h, 1d
	Limit     int       `json:"limit,omitempty"`
	Offset    int       `json:"offset,omitempty"`
}

// TimeSeriesData 时序数据
type TimeSeriesData struct {
	Timestamp time.Time         `json:"timestamp"`
	Value     interface{}       `json:"value"`
	Labels    map[string]string `json:"labels,omitempty"`
}

// MetricType 指标类型
type MetricType string

const (
	MetricTypeConnection  MetricType = "connection"
	MetricTypeMessage     MetricType = "message"
	MetricTypePerformance MetricType = "performance"
	MetricTypeSystem      MetricType = "system"
	MetricTypeTopic       MetricType = "topic"
	MetricTypeClient      MetricType = "client"
)

// EventType 事件类型
type EventType string

const (
	EventTypeConnect     EventType = "connect"
	EventTypeDisconnect  EventType = "disconnect"
	EventTypePublish     EventType = "publish"
	EventTypeSubscribe   EventType = "subscribe"
	EventTypeUnsubscribe EventType = "unsubscribe"
)

// AlertSeverity 告警严重程度
type AlertSeverity string

const (
	AlertSeverityLow      AlertSeverity = "low"
	AlertSeverityMedium   AlertSeverity = "medium"
	AlertSeverityHigh     AlertSeverity = "high"
	AlertSeverityCritical AlertSeverity = "critical"
)
