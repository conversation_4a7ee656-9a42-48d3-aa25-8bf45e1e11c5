package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SystemLog 系统日志模型
type SystemLog struct {
	ID        primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	Level     LogLevel               `bson:"level" json:"level"`                               // 日志级别
	Module    string                 `bson:"module" json:"module"`                             // 模块名称
	Action    string                 `bson:"action" json:"action"`                             // 操作动作
	Message   string                 `bson:"message" json:"message"`                           // 日志消息
	UserID    string                 `bson:"user_id,omitempty" json:"user_id,omitempty"`       // 用户ID
	IP        string                 `bson:"ip,omitempty" json:"ip,omitempty"`                 // IP地址
	UserAgent string                 `bson:"user_agent,omitempty" json:"user_agent,omitempty"` // 用户代理
	Details   map[string]interface{} `bson:"details,omitempty" json:"details,omitempty"`       // 详细信息
	Timestamp time.Time              `bson:"timestamp" json:"timestamp"`                       // 时间戳
}

// LogLevel 日志级别枚举
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug" // 调试级别
	LogLevelInfo  LogLevel = "info"  // 信息级别
	LogLevelWarn  LogLevel = "warn"  // 警告级别
	LogLevelError LogLevel = "error" // 错误级别
	LogLevelFatal LogLevel = "fatal" // 致命错误级别
)

// SystemLogFilter 系统日志查询过滤器
type SystemLogFilter struct {
	Level     LogLevel  `json:"level"`      // 日志级别过滤
	Module    string    `json:"module"`     // 模块过滤
	Action    string    `json:"action"`     // 操作过滤
	UserID    string    `json:"user_id"`    // 用户ID过滤
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
	Search    string    `json:"search"`     // 搜索关键词
	Page      int       `json:"page"`       // 页码
	Limit     int       `json:"limit"`      // 每页数量
}

// SystemMetrics 系统指标（重用现有的或扩展）
type SystemMetricsDetail struct {
	CPU     *CPUMetrics     `json:"cpu"`     // CPU指标
	Memory  *MemoryMetrics  `json:"memory"`  // 内存指标
	Disk    *DiskMetrics    `json:"disk"`    // 磁盘指标
	Network *NetworkMetrics `json:"network"` // 网络指标
}

// CPUMetrics CPU指标
type CPUMetrics struct {
	Usage     float64 `json:"usage"`       // CPU使用率 (%)
	LoadAvg1  float64 `json:"load_avg_1"`  // 1分钟平均负载
	LoadAvg5  float64 `json:"load_avg_5"`  // 5分钟平均负载
	LoadAvg15 float64 `json:"load_avg_15"` // 15分钟平均负载
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	Total     uint64  `json:"total"`     // 总内存 (bytes)
	Used      uint64  `json:"used"`      // 已用内存 (bytes)
	Available uint64  `json:"available"` // 可用内存 (bytes)
	Usage     float64 `json:"usage"`     // 内存使用率 (%)
}

// DiskMetrics 磁盘指标
type DiskMetrics struct {
	Total     uint64  `json:"total"`     // 总空间 (bytes)
	Used      uint64  `json:"used"`      // 已用空间 (bytes)
	Available uint64  `json:"available"` // 可用空间 (bytes)
	Usage     float64 `json:"usage"`     // 磁盘使用率 (%)
}

// NetworkMetrics 网络指标
type NetworkMetrics struct {
	BytesIn    uint64 `json:"bytes_in"`    // 入站字节数
	BytesOut   uint64 `json:"bytes_out"`   // 出站字节数
	PacketsIn  uint64 `json:"packets_in"`  // 入站包数
	PacketsOut uint64 `json:"packets_out"` // 出站包数
}

// SystemLogResponse 系统日志响应
type SystemLogResponse struct {
	*SystemLog
	UserName string `json:"user_name,omitempty"` // 用户名称
}

// 配置分类常量
const (
	ConfigCategorySystem   = "system"   // 系统配置
	ConfigCategoryMQTT     = "mqtt"     // MQTT配置
	ConfigCategoryDatabase = "database" // 数据库配置
	ConfigCategoryAuth     = "auth"     // 认证配置
	ConfigCategoryEmail    = "email"    // 邮件配置
	ConfigCategorySMS      = "sms"      // 短信配置
	ConfigCategoryMonitor  = "monitor"  // 监控配置
	ConfigCategoryCache    = "cache"    // 缓存配置
)

// 系统日志模块常量
const (
	LogModuleAuth    = "auth"    // 认证模块
	LogModuleUser    = "user"    // 用户模块
	LogModuleGroup   = "group"   // 分组模块
	LogModuleMQTT    = "mqtt"    // MQTT模块
	LogModuleSystem  = "system"  // 系统模块
	LogModuleMonitor = "monitor" // 监控模块
	LogModuleConfig  = "config"  // 配置模块
)

// 系统日志操作常量
const (
	LogActionLogin        = "login"         // 登录
	LogActionLogout       = "logout"        // 登出
	LogActionCreate       = "create"        // 创建
	LogActionUpdate       = "update"        // 更新
	LogActionDelete       = "delete"        // 删除
	LogActionView         = "view"          // 查看
	LogActionExport       = "export"        // 导出
	LogActionImport       = "import"        // 导入
	LogActionConfigChange = "config_change" // 配置变更
	LogActionSystemStart  = "system_start"  // 系统启动
	LogActionSystemStop   = "system_stop"   // 系统停止
)
