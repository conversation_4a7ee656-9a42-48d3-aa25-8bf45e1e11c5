package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserRole 用户角色枚举
type UserRole string

const (
	RoleAdmin UserRole = "admin" // 系统管理员
	RoleUser  UserRole = "user"  // 普通用户
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	StatusActive   UserStatus = "active"   // 活跃
	StatusInactive UserStatus = "inactive" // 非活跃
	StatusBanned   UserStatus = "banned"   // 被禁用
	StatusPending  UserStatus = "pending"  // 待激活
)

// User 用户模型
type User struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Email       string             `bson:"email" json:"email"`
	Password    string             `bson:"password" json:"-"` // 不在JSON中返回密码
	Role        UserRole           `bson:"role" json:"role"`
	Status      UserStatus         `bson:"status" json:"status"`
	Profile     UserProfile        `bson:"profile" json:"profile"`
	CreatedAt   time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at" json:"updated_at"`
	LastLoginAt *time.Time         `bson:"last_login_at,omitempty" json:"last_login_at,omitempty"`
}

// UserProfile 用户资料
type UserProfile struct {
	FirstName   string `bson:"first_name,omitempty" json:"first_name,omitempty"`
	LastName    string `bson:"last_name,omitempty" json:"last_name,omitempty"`
	DisplayName string `bson:"display_name,omitempty" json:"display_name,omitempty"`
	Avatar      string `bson:"avatar,omitempty" json:"avatar,omitempty"`
	Company     string `bson:"company,omitempty" json:"company,omitempty"`
	Department  string `bson:"department,omitempty" json:"department,omitempty"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Email    string      `json:"email" binding:"required,email"`
	Password string      `json:"password" binding:"required,min=8"`
	Role     UserRole    `json:"role,omitempty"`
	Profile  UserProfile `json:"profile,omitempty"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Profile UserProfile `json:"profile,omitempty"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// RegisterSendCodeRequest 发送注册验证码请求
type RegisterSendCodeRequest struct {
	Email    string      `json:"email" binding:"required,email"`
	Password string      `json:"password" binding:"required,min=8"`
	Role     UserRole    `json:"role,omitempty"`
	Profile  UserProfile `json:"profile,omitempty"`
}

// RegisterVerifyRequest 验证注册验证码请求
type RegisterVerifyRequest struct {
	Email string `json:"email" binding:"required,email"`
	Code  string `json:"code" binding:"required"`
}

// CachedUserInfo 缓存的用户注册信息
type CachedUserInfo struct {
	Email          string      `json:"email"`
	HashedPassword string      `json:"hashed_password"`
	Role           UserRole    `json:"role"`
	Profile        UserProfile `json:"profile"`
	CreatedAt      time.Time   `json:"created_at"`
}

// ForgotPasswordRequest 忘记密码请求
type ForgotPasswordRequest struct {
	Email    string `json:"email" binding:"required,email"`
	SendCode bool   `json:"send_code"`                                 // true: 发送验证码, false: 验证验证码并重置密码
	Code     string `json:"code" binding:"required_if=SendCode false"` // 当SendCode为false时必填
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status UserStatus `json:"status" binding:"required,oneof=active inactive banned pending"`
}

// AdminUpdateUserRequest 管理员更新用户请求
type AdminUpdateUserRequest struct {
	Email   string      `json:"email,omitempty" binding:"omitempty,email"`
	Role    UserRole    `json:"role,omitempty" binding:"omitempty,oneof=admin user"`
	Status  UserStatus  `json:"status,omitempty" binding:"omitempty,oneof=active inactive banned pending"`
	Profile UserProfile `json:"profile,omitempty"`
}

// UserResponse 用户响应（不包含敏感信息）
type UserResponse struct {
	ID          string      `json:"id"`
	Email       string      `json:"email"`
	Role        UserRole    `json:"role"`
	Status      UserStatus  `json:"status"`
	Profile     UserProfile `json:"profile"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
	LastLoginAt *time.Time  `json:"last_login_at,omitempty"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         UserResponse `json:"user"`
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int64        `json:"expires_in"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:          u.ID.Hex(),
		Email:       u.Email,
		Role:        u.Role,
		Status:      u.Status,
		Profile:     u.Profile,
		CreatedAt:   u.CreatedAt,
		UpdatedAt:   u.UpdatedAt,
		LastLoginAt: u.LastLoginAt,
	}
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == StatusActive
}

// GetDisplayName 获取显示名称
func (u *User) GetDisplayName() string {
	if u.Profile.DisplayName != "" {
		return u.Profile.DisplayName
	}
	if u.Profile.FirstName != "" || u.Profile.LastName != "" {
		return u.Profile.FirstName + " " + u.Profile.LastName
	}
	return u.Email
}

// Validate 验证用户数据
func (u *User) Validate() error {
	if u.Email == "" {
		return ErrInvalidEmail
	}
	if u.Password == "" {
		return ErrInvalidPassword
	}
	if u.Role != RoleAdmin && u.Role != RoleUser {
		return ErrInvalidRole
	}
	if u.Status != StatusActive && u.Status != StatusInactive &&
		u.Status != StatusBanned && u.Status != StatusPending {
		return ErrInvalidStatus
	}
	return nil
}

// BeforeCreate 创建前的钩子
func (u *User) BeforeCreate() {
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now
	if u.Role == "" {
		u.Role = RoleUser
	}
	if u.Status == "" {
		u.Status = StatusActive
	}
}

// BeforeUpdate 更新前的钩子
func (u *User) BeforeUpdate() {
	u.UpdatedAt = time.Now()
}

// UpdateLastLogin 更新最后登录时间
func (u *User) UpdateLastLogin() {
	now := time.Now()
	u.LastLoginAt = &now
	u.UpdatedAt = now
}
