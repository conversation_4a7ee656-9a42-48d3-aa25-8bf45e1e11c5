package mqtt

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/pkg/database"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// AuthenticateUser 认证用户
func (hm *HookManager) AuthenticateUser(ctx context.Context, clientID string) bool {
	// 更新统计信息
	hm.authStats.Lock()
	hm.authStats.TotalAuthRequests++
	hm.authStats.Unlock()

	if clientID == "" {
		return false
	}

	// 验证用户ID格式（MongoDB ObjectID）
	if !primitive.IsValidObjectID(clientID) {
		return false
	}

	// 尝试从缓存获取用户信息
	cachedUser, err := hm.getUserFromCache(ctx, clientID)
	if err == nil && cachedUser != nil {
		// 缓存命中统计
		hm.authStats.Lock()
		hm.authStats.CacheHits++
		hm.authStats.Unlock()

		// 检查缓存是否过期（软过期检查）
		if hm.authConfig != nil && time.Since(cachedUser.CachedAt) > hm.authConfig.CacheRefreshThreshold {
			// 缓存即将过期，异步刷新缓存
			go hm.refreshUserCache(context.Background(), clientID)
		}

		// 验证用户状态 - 管理员用户跳过状态检查
		if cachedUser.Role != model.RoleAdmin && cachedUser.Status != model.StatusActive {
			return false
		}

		return true
	}

	// 缓存未命中统计
	hm.authStats.Lock()
	hm.authStats.CacheMisses++
	hm.authStats.Unlock()

	// 缓存未命中，从数据库查询用户
	user, err := hm.getUserFromDatabase(ctx, clientID)
	if err != nil {
		return false
	}

	// 验证用户状态 - 管理员用户跳过状态检查
	if user.Role != model.RoleAdmin && user.Status != model.StatusActive {
		return false
	}

	// 缓存用户信息
	if err := hm.cacheUserInfo(ctx, user); err != nil {
		// 缓存失败不影响认证，继续执行
	}

	// 异步预热用户的Topic权限缓存
	go hm.preloadUserTopicPermissions(context.Background(), clientID)

	return true
}

// CheckACL 检查访问控制列表
func (hm *HookManager) CheckACL(clientID, topic string, write bool) bool {
	ctx := context.Background()

	// 更新权限检查统计
	hm.authStats.Lock()
	hm.authStats.PermissionChecks++
	hm.authStats.Unlock()

	// 验证用户ID格式（MongoDB ObjectID）
	if !primitive.IsValidObjectID(clientID) {
		hm.authStats.Lock()
		hm.authStats.PermissionDenied++
		hm.authStats.Unlock()
		return false
	}

	// 检查Topic权限
	action := "subscribe"
	if write {
		action = "publish"
	}

	// 检查是否为管理员用户 - 管理员拥有所有权限
	if hm.isAdminUser(ctx, clientID) {
		hm.authStats.Lock()
		hm.authStats.PermissionGranted++
		hm.authStats.Unlock()
		return true
	}

	// 调用分组服务检查权限
	if hm.groupService != nil {
		hasPermission, err := hm.groupService.CheckTopicPermission(ctx, clientID, topic, action)
		if err != nil {
			hm.authStats.Lock()
			hm.authStats.PermissionDenied++
			hm.authStats.Unlock()
			return false
		}

		// 更新权限统计
		hm.authStats.Lock()
		if hasPermission {
			hm.authStats.PermissionGranted++
		} else {
			hm.authStats.PermissionDenied++
		}
		hm.authStats.Unlock()

		return hasPermission
	}

	// 如果分组服务未初始化，允许所有操作（向后兼容）
	hm.authStats.Lock()
	hm.authStats.PermissionGranted++
	hm.authStats.Unlock()
	return true
}

// StoreMessage 存储消息 - 阻塞式，确保不丢失任何消息
func (hm *HookManager) StoreMessage(msg MessageData) {
	if hm.tdengineManager == nil {
		return
	}

	// 阻塞式发送到队列，确保消息不丢失
	// 如果队列满了，会等待直到有空间
	select {
	case hm.messageQueue <- msg:
		// 消息成功加入队列
	case <-hm.ctx.Done():
		// 系统正在关闭
		return
	}

	// 监控队列使用率，当队列使用率超过90%时发出警告
	queueLen := len(hm.messageQueue)
	queueCap := cap(hm.messageQueue)
	if queueLen > int(float64(queueCap)*0.9) {
		log.Printf("MQTT队列使用率过高 %d/%d (%.1f%%), 可能需要扩容",
			queueLen, queueCap, float64(queueLen)/float64(queueCap)*100)
	}
}

// ProcessAlert 处理预警检测
func (hm *HookManager) ProcessAlert(msg MessageData) {
	// 检查预警服务是否可用
	if hm.alertService == nil {
		return
	}

	// 异步处理预警检测，避免阻塞消息处理
	go func() {
		ctx := context.Background()
		if err := hm.alertService.ProcessMQTTMessage(ctx, msg.Topic, msg.Payload, msg.GroupID, msg.ClientID); err != nil {
			log.Printf("预警检测失败: %v", err)
		}
	}()
}

// ExtractGroupID 从客户端ID或Topic提取GroupID
func (hm *HookManager) ExtractGroupID(clientID, topic string) string {
	// 从topic中提取groupID，topic格式为: groupID/topicName
	if topic != "" {
		parts := strings.Split(topic, "/")
		if len(parts) >= 2 {
			// 第一部分是groupID
			return parts[0]
		}
	}

	// 如果无法从topic提取，使用客户端ID作为GroupID（客户端ID就是用户ID）
	if clientID != "" {
		return clientID
	}
	// 默认返回"default"组
	return "default"
}

// getUserFromCache 从Redis缓存获取用户信息
func (hm *HookManager) getUserFromCache(ctx context.Context, userID string) (*CachedUserInfo, error) {
	if hm.redisManager == nil {
		return nil, fmt.Errorf("Redis管理器未初始化")
	}

	cacheKey := fmt.Sprintf("user_auth:%s", userID)

	result, err := hm.redisManager.Get(ctx, cacheKey)
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("缓存未命中")
		}
		return nil, fmt.Errorf("获取缓存失败: %w", err)
	}

	var cachedUser CachedUserInfo
	if err := json.Unmarshal([]byte(result), &cachedUser); err != nil {
		return nil, fmt.Errorf("解析缓存数据失败: %w", err)
	}

	return &cachedUser, nil
}

// getUserFromDatabase 从MongoDB数据库查询用户信息
func (hm *HookManager) getUserFromDatabase(ctx context.Context, userID string) (*model.User, error) {
	if hm.mongoManager == nil {
		return nil, fmt.Errorf("MongoDB管理器未初始化")
	}

	objectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return nil, fmt.Errorf("无效的用户ID: %w", err)
	}

	collection := hm.mongoManager.GetCollection("users")
	if collection == nil {
		return nil, fmt.Errorf("获取用户集合失败")
	}

	var user model.User
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &user, nil
}

// cacheUserInfo 缓存用户信息到Redis
func (hm *HookManager) cacheUserInfo(ctx context.Context, user *model.User) error {
	if hm.redisManager == nil {
		return fmt.Errorf("Redis管理器未初始化")
	}

	cachedUser := CachedUserInfo{
		UserID:   user.ID.Hex(),
		Email:    user.Email,
		Role:     user.Role,
		Status:   user.Status,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cachedUser)
	if err != nil {
		return fmt.Errorf("序列化用户信息失败: %w", err)
	}

	cacheKey := fmt.Sprintf("user_auth:%s", user.ID.Hex())
	// 使用配置化的缓存时间
	cacheTTL := 30 * time.Minute // 默认值
	if hm.authConfig != nil {
		cacheTTL = hm.authConfig.UserCacheTTL
	}
	if err := hm.redisManager.Set(ctx, cacheKey, string(data), cacheTTL); err != nil {
		return fmt.Errorf("设置缓存失败: %w", err)
	}

	return nil
}

// refreshUserCache 异步刷新用户缓存
func (hm *HookManager) refreshUserCache(ctx context.Context, userID string) {
	// 从数据库重新获取用户信息
	user, err := hm.getUserFromDatabase(ctx, userID)
	if err != nil {
		return
	}

	// 更新缓存
	if err := hm.cacheUserInfo(ctx, user); err != nil {
		return
	}
}

// preloadUserTopicPermissions 预加载用户的Topic权限缓存
func (hm *HookManager) preloadUserTopicPermissions(ctx context.Context, userID string) {
	if hm.groupService == nil {
		return
	}

	// 检查是否启用预加载
	if hm.authConfig != nil && !hm.authConfig.EnablePreload {
		return
	}

	// 简化版本：只记录预加载尝试，实际的权限检查在需要时进行
	// 这避免了对其他服务方法的依赖，保持接口简单
}

// isAdminUser 检查用户是否为管理员
func (hm *HookManager) isAdminUser(ctx context.Context, userID string) bool {
	// 首先尝试从缓存获取用户信息
	cachedUser, err := hm.getUserFromCache(ctx, userID)
	if err == nil && cachedUser != nil {
		return cachedUser.Role == model.RoleAdmin
	}

	// 缓存未命中，从数据库查询用户
	user, err := hm.getUserFromDatabase(ctx, userID)
	if err != nil {
		return false
	}

	// 缓存用户信息（如果还没有缓存的话）
	if cachedUser == nil {
		if err := hm.cacheUserInfo(ctx, user); err != nil {
			// 缓存失败不影响权限检查
		}
	}

	return user.Role == model.RoleAdmin
}

// messageProcessor 消息处理器 - 批量处理消息存储
func (hm *HookManager) messageProcessor(processorID int) {
	var batch []MessageData
	ticker := time.NewTicker(hm.flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-hm.ctx.Done():
			// 处理剩余的消息
			if len(batch) > 0 {
				hm.processBatch(batch)
			}
			return

		case msg, ok := <-hm.messageQueue:
			if !ok {
				// 通道关闭，处理剩余消息
				if len(batch) > 0 {
					hm.processBatch(batch)
				}
				return
			}

			batch = append(batch, msg)

			// 达到批量大小，立即处理
			if len(batch) >= hm.batchSize {
				hm.processBatch(batch)
				batch = batch[:0] // 清空批次
			}

		case <-ticker.C:
			// 定时刷新，处理未满批次的消息
			if len(batch) > 0 {
				hm.processBatch(batch)
				batch = batch[:0] // 清空批次
			}
		}
	}
}

// processBatch 批量处理消息存储 - 带重试机制
func (hm *HookManager) processBatch(batch []MessageData) {
	if len(batch) == 0 {
		return
	}

	// 尝试批量插入，失败时回退到逐条插入
	if hm.processBatchOptimized(batch) {
		return
	}

	// 批量插入失败，使用逐条插入作为备选方案
	hm.processBatchFallback(batch)
}

// processBatchOptimized 优化的批量处理 - 尝试真正的批量插入
func (hm *HookManager) processBatchOptimized(batch []MessageData) bool {
	// 目前TDengine管理器不支持批量插入，直接返回false使用fallback方法
	// 未来可以在这里添加批量插入支持
	return false // 使用fallback方法
}

// processBatchFallback 备选的逐条处理方案 - 带重试机制
func (hm *HookManager) processBatchFallback(batch []MessageData) {
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	successCount := 0
	var failedMessages []MessageData

	for _, msg := range batch {
		// 转换为TDengine数据格式
		mqttData := database.MqttMessageData{
			Timestamp: msg.Timestamp,
			Topic:     msg.Topic,
			Payload:   msg.Payload,
			QoS:       msg.QoS,
			ClientID:  msg.ClientID,
			GroupID:   msg.GroupID,
		}

		// 带重试的存储
		if hm.insertMessageWithRetry(ctx, mqttData, 3) {
			successCount++
		} else {
			failedMessages = append(failedMessages, msg)
		}
	}

	// 记录处理结果
	if len(failedMessages) > 0 {
		// 对于失败的消息，可以考虑写入错误日志或重新排队
		hm.handleFailedMessages(failedMessages)
	}
}

// dataStorer 数据存储器 - 定期存储历史数据
func (hm *HookManager) dataStorer() {
	defer hm.wg.Done()

	ticker := time.NewTicker(hm.storeInterval)
	defer ticker.Stop()

	for {
		select {
		case <-hm.ctx.Done():
			return
		case <-ticker.C:
			hm.collectAndStoreMetrics()
		}
	}
}

// collectAndStoreMetrics 收集并存储各种指标数据
func (hm *HookManager) collectAndStoreMetrics() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	now := time.Now()

	// 1. 收集并存储性能指标
	hm.storePerformanceMetrics(ctx, now)

	// 2. 收集并存储系统指标
	hm.storeSystemMetrics(ctx, now)
}

// storePerformanceMetrics 存储性能指标
func (hm *HookManager) storePerformanceMetrics(ctx context.Context, timestamp time.Time) {
	hm.mu.RLock()
	server := hm.server
	hm.mu.RUnlock()

	if server == nil {
		return
	}

	// 获取服务器统计信息
	serverInfo := server.Info
	if serverInfo == nil {
		return
	}

	// 构建性能指标数据
	perfData := database.PerformanceMetricsData{
		Timestamp:        timestamp,
		ClientsCount:     serverInfo.ClientsConnected,
		Subscriptions:    serverInfo.Subscriptions,
		MessagesSent:     serverInfo.MessagesSent,
		MessagesReceived: serverInfo.MessagesReceived,
		BytesSent:        serverInfo.BytesSent,
		BytesReceived:    serverInfo.BytesReceived,
		Uptime:           float64(serverInfo.Time - serverInfo.Started),
		MemoryUsage:      serverInfo.MemoryAlloc,
		NetworkInBytes:   serverInfo.BytesReceived,
		NetworkOutBytes:  serverInfo.BytesSent,
	}

	// 存储性能指标
	if err := hm.tdengineManager.InsertPerformanceMetrics(ctx, perfData); err != nil {
		// 静默处理存储失败
	}
}

// storeSystemMetrics 存储系统指标
func (hm *HookManager) storeSystemMetrics(ctx context.Context, timestamp time.Time) {
	if hm.systemCollector == nil {
		return
	}

	systemMetrics, err := hm.systemCollector.GetSystemMetrics()
	if err != nil {
		return
	}

	// 构建系统指标数据
	sysData := database.SystemMetricsData{
		Timestamp:       timestamp,
		CPUUsage:        systemMetrics.CPU.Usage,
		MemoryUsage:     int64(systemMetrics.Memory.Used),
		MemoryTotal:     int64(systemMetrics.Memory.Total),
		DiskUsage:       int64(systemMetrics.Disk.Used),
		DiskTotal:       int64(systemMetrics.Disk.Total),
		NetworkInBytes:  int64(systemMetrics.Network.BytesIn),
		NetworkOutBytes: int64(systemMetrics.Network.BytesOut),
		LoadAverage:     systemMetrics.CPU.LoadAvg1,
	}

	// 存储系统指标
	if err := hm.tdengineManager.InsertSystemMetrics(ctx, sysData); err != nil {
		// 静默处理存储失败
	}
}

// insertMessageWithRetry 带重试机制的消息插入
func (hm *HookManager) insertMessageWithRetry(ctx context.Context, mqttData database.MqttMessageData, maxRetries int) bool {
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if err := hm.tdengineManager.InsertMqttMessage(ctx, mqttData); err != nil {
			if attempt == maxRetries {
				return false
			}

			// 等待一段时间后重试
			waitTime := time.Duration(attempt) * 100 * time.Millisecond
			select {
			case <-time.After(waitTime):
				continue
			case <-ctx.Done():
				return false
			}
		} else {
			return true
		}
	}
	return false
}

// handleFailedMessages 处理失败的消息
func (hm *HookManager) handleFailedMessages(failedMessages []MessageData) {
	if len(failedMessages) == 0 {
		return
	}

	// 静默处理失败消息

	// TODO: 可以在这里添加更复杂的失败处理逻辑
	// 例如：写入文件、发送到备用存储、发送告警等
}
