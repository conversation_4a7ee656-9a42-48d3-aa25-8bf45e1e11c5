package mqtt

import (
	"bytes"
	"context"
	"fmt"
	"sync"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/internal/utils"
	"beacon/cloud/pkg/database"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/packets"
)

// HookManager Hook管理器 - 统一管理所有MQTT功能
type HookManager struct {
	server      *mqtt.Server
	customHook  *CustomHook
	monitorHook *MonitorHook
	mu          sync.RWMutex

	// 内嵌的功能服务
	mongoManager    *database.MongoDBManager
	redisManager    *database.RedisManager
	tdengineManager *database.TDengineManager
	groupService    GroupService // 分组服务接口
	alertService    AlertService // 预警服务接口

	// 消息存储相关
	messageQueue   chan MessageData
	batchSize      int
	flushInterval  time.Duration
	processorCount int // 并发处理器数量
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup

	// 监控相关
	systemCollector *utils.SystemMetricsCollector
	storeInterval   time.Duration

	// 权限检查统计
	authStats struct {
		sync.RWMutex
		TotalAuthRequests int64 `json:"total_auth_requests"`
		CacheHits         int64 `json:"cache_hits"`
		CacheMisses       int64 `json:"cache_misses"`
		PermissionChecks  int64 `json:"permission_checks"`
		PermissionDenied  int64 `json:"permission_denied"`
		PermissionGranted int64 `json:"permission_granted"`
	}

	// 认证配置
	authConfig *AuthConfig
}

// GroupService 分组服务接口（避免循环依赖）
type GroupService interface {
	CheckTopicPermission(ctx context.Context, userID, fullTopic, action string) (bool, error)
}

// AlertService 预警服务接口（避免循环依赖）
type AlertService interface {
	ProcessMQTTMessage(ctx context.Context, topic, payload, groupID, clientID string) error
}

// MessageData 消息数据结构
type MessageData struct {
	Timestamp time.Time
	Topic     string
	Payload   string
	QoS       int
	ClientID  string
	GroupID   string
}

// CachedUserInfo 缓存的用户认证信息
type CachedUserInfo struct {
	UserID   string           `json:"user_id"`
	Email    string           `json:"email"`
	Role     model.UserRole   `json:"role"`
	Status   model.UserStatus `json:"status"`
	CachedAt time.Time        `json:"cached_at"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	UserCacheTTL          time.Duration `json:"user_cache_ttl"`          // 用户缓存TTL
	PermissionCacheTTL    time.Duration `json:"permission_cache_ttl"`    // 权限缓存TTL
	NoPermissionCacheTTL  time.Duration `json:"no_permission_cache_ttl"` // 无权限缓存TTL
	CacheRefreshThreshold time.Duration `json:"cache_refresh_threshold"` // 缓存刷新阈值
	EnablePreload         bool          `json:"enable_preload"`          // 是否启用权限预加载
	EnableDetailedLogs    bool          `json:"enable_detailed_logs"`    // 是否启用详细日志
}

// NewHookManager 创建新的Hook管理器
func NewHookManager(mongoManager *database.MongoDBManager, redisManager *database.RedisManager, tdengineManager *database.TDengineManager) *HookManager {
	ctx, cancel := context.WithCancel(context.Background())

	// 默认认证配置
	defaultAuthConfig := &AuthConfig{
		UserCacheTTL:          30 * time.Minute,
		PermissionCacheTTL:    time.Hour,
		NoPermissionCacheTTL:  10 * time.Minute,
		CacheRefreshThreshold: 10 * time.Minute,
		EnablePreload:         true,
		EnableDetailedLogs:    true,
	}

	return &HookManager{
		mongoManager:    mongoManager,
		redisManager:    redisManager,
		tdengineManager: tdengineManager,
		messageQueue:    make(chan MessageData, 50000), // 增大缓冲队列容量
		batchSize:       500,                           // 增大批量处理大小
		flushInterval:   3 * time.Second,               // 减少刷新间隔
		processorCount:  3,                             // 3个并发处理器
		ctx:             ctx,
		cancel:          cancel,
		systemCollector: utils.NewSystemMetricsCollector(),
		storeInterval:   time.Minute, // 每分钟存储一次历史数据
		authConfig:      defaultAuthConfig,
	}
}

// SetMQTTServer 设置MQTT服务器引用
func (hm *HookManager) SetMQTTServer(server *mqtt.Server) {
	hm.mu.Lock()
	defer hm.mu.Unlock()

	hm.server = server
}

// SetGroupService 设置分组服务
func (hm *HookManager) SetGroupService(groupService GroupService) {
	hm.mu.Lock()
	defer hm.mu.Unlock()

	hm.groupService = groupService
}

// SetAlertService 设置预警服务
func (hm *HookManager) SetAlertService(alertService AlertService) {
	hm.mu.Lock()
	defer hm.mu.Unlock()

	hm.alertService = alertService
}

// SetAuthConfig 设置认证配置
func (hm *HookManager) SetAuthConfig(config *AuthConfig) {
	hm.mu.Lock()
	defer hm.mu.Unlock()

	if config != nil {
		hm.authConfig = config
	}
}

// GetAuthConfig 获取认证配置
func (hm *HookManager) GetAuthConfig() *AuthConfig {
	hm.mu.RLock()
	defer hm.mu.RUnlock()

	return hm.authConfig
}

// RegisterHooks 注册所有Hook
func (hm *HookManager) RegisterHooks() error {
	if hm.server == nil {
		return fmt.Errorf("MQTT服务器未设置")
	}

	// 创建并注册CustomHook（集成认证和存储功能）
	hm.customHook = NewCustomHook(hm)
	if err := hm.server.AddHook(hm.customHook, nil); err != nil {
		return fmt.Errorf("注册CustomHook失败: %v", err)
	}

	// 创建并注册MonitorHook（集成监控功能）
	hm.monitorHook = NewMonitorHook(hm)
	if err := hm.server.AddHook(hm.monitorHook, nil); err != nil {
		return fmt.Errorf("注册MonitorHook失败: %v", err)
	}

	return nil
}

// StartHooks 启动所有Hook服务
func (hm *HookManager) StartHooks() error {
	// 启动多个消息存储处理器
	if hm.tdengineManager != nil {
		for i := 0; i < hm.processorCount; i++ {
			hm.wg.Add(1)
			go func(processorID int) {
				defer hm.wg.Done()
				hm.messageProcessor(processorID)
			}(i)
		}
	}

	// 启动监控数据收集器
	if hm.tdengineManager != nil {
		hm.wg.Add(1)
		go hm.dataStorer()
	}
	return nil
}

// StopHooks 停止所有Hook服务
func (hm *HookManager) StopHooks() error {
	hm.cancel()
	if hm.messageQueue != nil {
		close(hm.messageQueue)
	}
	hm.wg.Wait()

	return nil
}

// GetHookStatus 获取Hook状态
func (hm *HookManager) GetHookStatus() map[string]interface{} {
	hm.mu.RLock()
	defer hm.mu.RUnlock()

	status := map[string]interface{}{
		"is_running": hm.ctx.Err() == nil,
	}

	if hm.customHook != nil {
		status["custom_hook"] = hm.customHook.GetStatus()
	}

	if hm.monitorHook != nil {
		status["monitor_hook"] = hm.monitorHook.GetStatus()
	}

	// 添加消息队列状态
	if hm.messageQueue != nil {
		status["message_queue"] = map[string]interface{}{
			"length":         len(hm.messageQueue),
			"capacity":       cap(hm.messageQueue),
			"batch_size":     hm.batchSize,
			"flush_interval": hm.flushInterval.String(),
		}
	}

	// 添加权限检查统计
	status["auth_stats"] = hm.GetAuthStats()

	return status
}

// GetCurrentMetrics 获取当前监控指标
func (hm *HookManager) GetCurrentMetrics() map[string]interface{} {
	hm.mu.RLock()
	server := hm.server
	hm.mu.RUnlock()

	metrics := make(map[string]interface{})

	// 获取MQTT服务器指标
	if server != nil && server.Info != nil {
		serverInfo := server.Info
		metrics["mqtt"] = map[string]interface{}{
			"clients_count":     serverInfo.ClientsConnected,
			"subscriptions":     serverInfo.Subscriptions,
			"messages_sent":     serverInfo.MessagesSent,
			"messages_received": serverInfo.MessagesReceived,
			"bytes_sent":        serverInfo.BytesSent,
			"bytes_received":    serverInfo.BytesReceived,
			"uptime":            float64(serverInfo.Time - serverInfo.Started),
			"memory_usage":      serverInfo.MemoryAlloc,
		}
	}

	// 获取系统指标
	if hm.systemCollector != nil {
		if systemMetrics, err := hm.systemCollector.GetSystemMetrics(); err == nil {
			metrics["system"] = map[string]interface{}{
				"cpu_usage":         systemMetrics.CPU.Usage,
				"memory_used":       systemMetrics.Memory.Used,
				"memory_total":      systemMetrics.Memory.Total,
				"disk_used":         systemMetrics.Disk.Used,
				"disk_total":        systemMetrics.Disk.Total,
				"network_bytes_in":  systemMetrics.Network.BytesIn,
				"network_bytes_out": systemMetrics.Network.BytesOut,
				"load_average":      systemMetrics.CPU.LoadAvg1,
			}
		}
	}

	return metrics
}

// GetAuthStats 获取认证统计信息
func (hm *HookManager) GetAuthStats() map[string]interface{} {
	hm.authStats.RLock()
	defer hm.authStats.RUnlock()

	stats := map[string]interface{}{
		"total_auth_requests": hm.authStats.TotalAuthRequests,
		"cache_hits":          hm.authStats.CacheHits,
		"cache_misses":        hm.authStats.CacheMisses,
		"permission_checks":   hm.authStats.PermissionChecks,
		"permission_denied":   hm.authStats.PermissionDenied,
		"permission_granted":  hm.authStats.PermissionGranted,
	}

	// 计算缓存命中率
	if hm.authStats.TotalAuthRequests > 0 {
		stats["cache_hit_rate"] = float64(hm.authStats.CacheHits) / float64(hm.authStats.TotalAuthRequests) * 100
	} else {
		stats["cache_hit_rate"] = 0.0
	}

	// 计算权限通过率
	if hm.authStats.PermissionChecks > 0 {
		stats["permission_grant_rate"] = float64(hm.authStats.PermissionGranted) / float64(hm.authStats.PermissionChecks) * 100
	} else {
		stats["permission_grant_rate"] = 0.0
	}

	return stats
}

// CustomHook 统一的MQTT Hook，处理认证、ACL检查和消息存储
type CustomHook struct {
	mqtt.HookBase
	manager *HookManager
}

// NewCustomHook 创建新的统一Hook
func NewCustomHook(manager *HookManager) *CustomHook {
	return &CustomHook{
		manager: manager,
	}
}

// ID 返回Hook的ID
func (h *CustomHook) ID() string {
	return "custom-hook"
}

// Provides 提供的Hook接口
func (h *CustomHook) Provides(b byte) bool {
	return bytes.Contains([]byte{
		mqtt.OnPublish,
		mqtt.OnSubscribed,
		mqtt.OnUnsubscribed,
		mqtt.OnSubscribe,
		mqtt.OnConnectAuthenticate,
		mqtt.OnACLCheck,
	}, []byte{b})
}

// Init 初始化Hook
func (h *CustomHook) Init(config any) error {
	return nil
}

// Stop 停止Hook
func (h *CustomHook) Stop() error {
	return nil
}

// OnConnectAuthenticate MQTT连接认证
func (h *CustomHook) OnConnectAuthenticate(cl *mqtt.Client, pk packets.Packet) bool {
	ctx := context.Background()
	return h.manager.AuthenticateUser(ctx, cl.ID)
}

// OnACLCheck ACL检查
func (h *CustomHook) OnACLCheck(cl *mqtt.Client, topic string, write bool) bool {
	return h.manager.CheckACL(cl.ID, topic, write)
}

// OnPublish MQTT消息发布事件处理
func (h *CustomHook) OnPublish(cl *mqtt.Client, pk packets.Packet) (packets.Packet, error) {
	// 构造消息数据
	messageData := MessageData{
		Timestamp: time.Now(),
		Topic:     pk.TopicName,
		Payload:   string(pk.Payload),
		QoS:       int(pk.FixedHeader.Qos),
		ClientID:  cl.ID,
		GroupID:   h.manager.ExtractGroupID(cl.ID, pk.TopicName),
	}

	// 存储消息
	h.manager.StoreMessage(messageData)

	// 触发预警检测
	h.manager.ProcessAlert(messageData)

	// 返回原始数据包，不修改
	return pk, nil
}

// GetStatus 获取Hook状态信息
func (h *CustomHook) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"id":         h.ID(),
		"is_running": true,
	}
}

// MonitorHook MQTT监控Hook - 负责后台监控数据收集
type MonitorHook struct {
	mqtt.HookBase
	manager *HookManager
}

// NewMonitorHook 创建新的监控Hook
func NewMonitorHook(manager *HookManager) *MonitorHook {
	return &MonitorHook{
		manager: manager,
	}
}

// ID 返回Hook的ID
func (h *MonitorHook) ID() string {
	return "monitor-hook"
}

// Provides 提供的Hook接口 - 此Hook不直接处理MQTT事件，只进行后台指标收集
func (h *MonitorHook) Provides(b byte) bool {
	return false
}

// Init 初始化Hook
func (h *MonitorHook) Init(config any) error {
	return nil
}

// Stop 停止Hook
func (h *MonitorHook) Stop() error {
	return nil
}

// GetStatus 获取Hook状态信息
func (h *MonitorHook) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"id":         h.ID(),
		"is_running": true,
	}
}
