package mqtt

import (
	"fmt"
	"log"

	"beacon/cloud/internal/config"
	"beacon/cloud/pkg/database"
)

var (
	// GlobalManager 全局MQTT管理器实例
	GlobalManager *Manager
)

// InitializeMQTT 初始化全局MQTT服务
func InitializeMQTT(cfg *config.Config, mongoManager *database.MongoDBManager, redisManager *database.RedisManager, tdengineManager *database.TDengineManager) error {

	// 创建MQTT管理器
	GlobalManager = NewManager(cfg, mongoManager, redisManager, tdengineManager)

	// 初始化MQTT服务
	if err := GlobalManager.Initialize(); err != nil {
		return err
	}

	return nil
}

// StartMQTT 启动全局MQTT服务
func StartMQTT() error {
	if GlobalManager == nil {
		return fmt.Errorf("MQTT服务未初始化，请先调用InitializeMQTT()")
	}

	return GlobalManager.Start()
}

// StopMQTT 停止全局MQTT服务
func StopMQTT() error {
	if GlobalManager == nil {
		return nil
	}

	log.Println("正在停止MQTT服务...")
	return GlobalManager.Stop()
}

// GetMQTTManager 获取全局MQTT管理器
func GetMQTTManager() *Manager {
	return GlobalManager
}

// IsMQTTRunning 检查MQTT服务是否正在运行
func IsMQTTRunning() bool {
	if GlobalManager == nil {
		return false
	}
	return GlobalManager.IsRunning()
}

// GetMQTTStats 获取MQTT服务统计信息
func GetMQTTStats() map[string]interface{} {
	if GlobalManager == nil {
		return map[string]interface{}{
			"initialized": false,
			"running":     false,
		}
	}
	return GlobalManager.GetStats()
}

// HealthCheckMQTT MQTT服务健康检查
func HealthCheckMQTT() error {
	if GlobalManager == nil {
		return fmt.Errorf("MQTT服务未初始化")
	}
	return GlobalManager.Health()
}
