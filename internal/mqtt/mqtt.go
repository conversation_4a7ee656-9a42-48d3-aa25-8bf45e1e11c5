package mqtt

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/pkg/database"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/listeners"
	"github.com/mochi-mqtt/server/v2/system"
)

// Manager MQTT服务管理器
type Manager struct {
	server          *Server
	config          *config.Config
	mongoManager    *database.MongoDBManager
	redisManager    *database.RedisManager
	tdengineManager *database.TDengineManager
	mu              sync.RWMutex
}

// NewManager 创建新的MQTT管理器
func NewManager(cfg *config.Config, mongoManager *database.MongoDBManager, redisManager *database.RedisManager, tdengineManager *database.TDengineManager) *Manager {
	return &Manager{
		config:          cfg,
		mongoManager:    mongoManager,
		redisManager:    redisManager,
		tdengineManager: tdengineManager,
	}
}

// GetHookManager 获取Hook管理器
func (m *Manager) GetHookManager() *HookManager {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.server != nil {
		return m.server.GetHookManager()
	}
	return nil
}

// Initialize 初始化MQTT服务
func (m *Manager) Initialize() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.server != nil {
		return fmt.Errorf("MQTT服务已经初始化")
	}

	mqttConfig := &m.config.MQTT

	// 验证MQTT配置
	if err := m.validateMQTTConfig(mqttConfig); err != nil {
		return fmt.Errorf("MQTT配置验证失败: %v", err)
	}

	// 创建MQTT服务器
	m.server = NewServer(mqttConfig, m.mongoManager, m.redisManager, m.tdengineManager)

	return nil
}

// Start 启动MQTT服务
func (m *Manager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.server == nil {
		return fmt.Errorf("MQTT服务未初始化，请先调用Initialize()")
	}

	// 启动服务器（Hook管理已在Server内部处理）
	return m.server.Start()
}

// Stop 停止MQTT服务
func (m *Manager) Stop() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.server == nil {
		return nil
	}

	return m.server.Stop()
}

// IsRunning 检查MQTT服务是否正在运行
func (m *Manager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.server == nil {
		return false
	}

	return m.server.IsRunning()
}

// GetStats 获取MQTT服务统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.server == nil {
		return map[string]interface{}{
			"initialized": false,
			"running":     false,
		}
	}

	stats := m.server.GetStats()
	stats["initialized"] = true
	return stats
}

// Health 健康检查
func (m *Manager) Health() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.server == nil {
		return fmt.Errorf("MQTT服务未初始化")
	}

	return m.server.Health()
}

// HealthCheck 健康检查（实现MQTTStatsProvider接口）
func (m *Manager) HealthCheck() error {
	return m.Health()
}

// GetServer 获取MQTT服务器实例（用于高级操作）
func (m *Manager) GetServer() *Server {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.server
}

// GetServerInfo 获取服务器信息 - 实现 MQTTManagerInterface
func (m *Manager) GetServerInfo() *system.Info {
	m.mu.RLock()
	defer m.mu.RUnlock()
	if m.server != nil {
		return m.server.GetInfo()
	}
	return nil
}

// Restart 重启MQTT服务
func (m *Manager) Restart() error {
	log.Println("正在重启MQTT服务...")

	// 停止服务
	if err := m.Stop(); err != nil {
		log.Printf("停止MQTT服务时出错: %v", err)
	}

	// 重新启动服务
	if err := m.Start(); err != nil {
		return fmt.Errorf("重启MQTT服务失败: %v", err)
	}

	log.Println("MQTT服务重启成功")
	return nil
}

// UpdateConfig 更新MQTT配置（需要重启服务生效）
func (m *Manager) UpdateConfig(newConfig *config.MQTTConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 验证新配置
	if err := m.validateMQTTConfig(newConfig); err != nil {
		return fmt.Errorf("新配置验证失败: %v", err)
	}

	// 更新配置
	m.config.MQTT = *newConfig

	log.Println("MQTT配置已更新，需要重启服务生效")
	return nil
}

// validateMQTTConfig 验证MQTT配置参数
func (m *Manager) validateMQTTConfig(cfg *config.MQTTConfig) error {
	if cfg.TCPPort == "" {
		return fmt.Errorf("TCP端口不能为空")
	}

	if cfg.WebSocketPort == "" {
		return fmt.Errorf("WebSocket端口不能为空")
	}

	if cfg.TCPPort == cfg.WebSocketPort {
		return fmt.Errorf("TCP端口和WebSocket端口不能相同")
	}

	if cfg.MaxClients <= 0 {
		return fmt.Errorf("最大客户端数必须大于0")
	}

	if cfg.BufferSize <= 0 {
		return fmt.Errorf("缓冲区大小必须大于0")
	}

	// 验证端口配置
	if err := validatePortConfig(cfg); err != nil {
		return err
	}

	return nil
}

// GetConfig 获取当前MQTT配置
func (m *Manager) GetConfig() *config.MQTTConfig {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return &m.config.MQTT
}

// validatePortConfig 验证端口配置
func validatePortConfig(cfg *config.MQTTConfig) error {
	if cfg.TCPPort == "" {
		return fmt.Errorf("TCP端口不能为空")
	}

	if cfg.WebSocketPort == "" {
		return fmt.Errorf("WebSocket端口不能为空")
	}

	if cfg.TCPPort == cfg.WebSocketPort {
		return fmt.Errorf("TCP端口和WebSocket端口不能相同")
	}

	// 检查端口范围（简单验证）
	if !isValidPort(cfg.TCPPort) {
		return fmt.Errorf("TCP端口 %s 无效", cfg.TCPPort)
	}

	if !isValidPort(cfg.WebSocketPort) {
		return fmt.Errorf("WebSocket端口 %s 无效", cfg.WebSocketPort)
	}

	return nil
}

// isValidPort 检查端口号是否有效
func isValidPort(port string) bool {
	// 简单的端口验证，实际应用中可以更严格
	if port == "" {
		return false
	}

	// 这里可以添加更详细的端口验证逻辑
	// 比如检查端口范围 1-65535
	return true
}

// Server MQTT服务器结构体
type Server struct {
	server          *mqtt.Server
	config          *config.MQTTConfig
	hookManager     *HookManager
	mongoManager    *database.MongoDBManager
	redisManager    *database.RedisManager
	tdengineManager *database.TDengineManager
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
	running         bool
	mu              sync.RWMutex
}

// NewServer 创建新的MQTT服务器实例
func NewServer(cfg *config.MQTTConfig, mongoManager *database.MongoDBManager, redisManager *database.RedisManager, tdengineManager *database.TDengineManager) *Server {
	ctx, cancel := context.WithCancel(context.Background())

	return &Server{
		config:          cfg,
		hookManager:     NewHookManager(mongoManager, redisManager, tdengineManager),
		mongoManager:    mongoManager,
		redisManager:    redisManager,
		tdengineManager: tdengineManager,
		ctx:             ctx,
		cancel:          cancel,
	}
}

// SetMonitorHook 设置监控Hook（保持兼容性）
func (s *Server) SetMonitorHook(monitorHook interface{}) {
	// 这个方法保持兼容性，但实际功能由HookManager处理
	log.Println("Server: SetMonitorHook调用已委托给HookManager处理")
}

// GetHookManager 获取Hook管理器
func (s *Server) GetHookManager() *HookManager {
	return s.hookManager
}

// Start 启动MQTT服务器
func (s *Server) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("MQTT服务器已经在运行")
	}

	// 创建MQTT服务器实例
	s.server = mqtt.New(&mqtt.Options{
		InlineClient: true, // 启用内联客户端支持
	})

	// 设置MQTT服务器引用到HookManager
	s.hookManager.SetMQTTServer(s.server)

	// 注册所有Hook
	if err := s.hookManager.RegisterHooks(); err != nil {
		return fmt.Errorf("注册Hook失败: %v", err)
	}

	// 启动所有Hook服务
	if err := s.hookManager.StartHooks(); err != nil {
		return fmt.Errorf("启动Hook服务失败: %v", err)
	}

	// 启动监听器
	if err := s.startListeners(); err != nil {
		return fmt.Errorf("启动监听器失败: %v", err)
	}

	// 在后台启动MQTT服务器
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.server.Serve(); err != nil {
			log.Printf("MQTT服务器运行错误: %v", err)
		}
	}()

	s.running = true

	return nil
}

// Stop 停止MQTT服务器
func (s *Server) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	log.Println("正在停止MQTT服务器...")

	// 停止所有Hook服务
	if err := s.hookManager.StopHooks(); err != nil {
		log.Printf("停止Hook服务时出错: %v", err)
	}

	// 监听器会随着MQTT服务器关闭而自动停止
	log.Println("监听器将随MQTT服务器关闭")

	// 取消上下文
	s.cancel()

	// 关闭MQTT服务器
	if s.server != nil {
		if err := s.server.Close(); err != nil {
			log.Printf("关闭MQTT服务器时出错: %v", err)
		}
	}

	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	// 设置超时等待
	select {
	case <-done:
		log.Println("MQTT服务器已成功停止")
	case <-time.After(10 * time.Second):
		log.Println("MQTT服务器停止超时，强制退出")
	}

	s.running = false
	return nil
}

// IsRunning 检查服务器是否正在运行
func (s *Server) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// GetStats 获取服务器统计信息
func (s *Server) GetStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.running || s.server == nil {
		return map[string]interface{}{
			"running":       false,
			"clients_count": 0,
			"subscriptions": 0,
			"messages_sent": 0,
			"messages_recv": 0,
		}
	}

	info := s.server.Info
	stats := map[string]interface{}{
		"running":       true,
		"clients_count": s.server.Clients.Len(),
		"subscriptions": info.Subscriptions,
		"messages_sent": info.MessagesSent,
		"messages_recv": info.MessagesReceived,
		"bytes_sent":    info.BytesSent,
		"bytes_recv":    info.BytesReceived,
		"uptime":        time.Since(time.Unix(info.Started, 0)).Seconds(),
	}

	// 添加Hook状态信息
	if s.hookManager != nil {
		stats["hooks"] = s.hookManager.GetHookStatus()
	}

	return stats
}

// GetInfo 获取服务器信息对象
func (s *Server) GetInfo() *system.Info {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.running || s.server == nil {
		return nil
	}

	return s.server.Info
}

// GetServer 获取底层MQTT服务器实例（用于高级配置）
func (s *Server) GetServer() *mqtt.Server {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.server
}

// Health 健康检查
func (s *Server) Health() error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.running {
		return fmt.Errorf("MQTT服务器未运行")
	}

	// 检查监听器健康状态
	return s.checkListenersHealth()
}

// startListeners 启动所有监听器
func (s *Server) startListeners() error {
	// 启动TCP监听器
	if err := s.startTCPListener(); err != nil {
		return fmt.Errorf("启动TCP监听器失败: %v", err)
	}

	// 启动WebSocket监听器
	if err := s.startWebSocketListener(); err != nil {
		return fmt.Errorf("启动WebSocket监听器失败: %v", err)
	}

	log.Printf("所有监听器启动成功 - TCP:%s, WebSocket:%s",
		s.config.TCPPort, s.config.WebSocketPort)
	return nil
}

// startTCPListener 启动TCP监听器
func (s *Server) startTCPListener() error {
	tcp := listeners.NewTCP(listeners.Config{
		ID:      "tcp",
		Address: fmt.Sprintf(":%s", s.config.TCPPort),
	})

	err := s.server.AddListener(tcp)
	if err != nil {
		return fmt.Errorf("添加TCP监听器失败: %v", err)
	}

	return nil
}

// startWebSocketListener 启动WebSocket监听器
func (s *Server) startWebSocketListener() error {
	ws := listeners.NewWebsocket(listeners.Config{
		ID:      "ws",
		Address: fmt.Sprintf(":%s", s.config.WebSocketPort),
	})

	err := s.server.AddListener(ws)
	if err != nil {
		return fmt.Errorf("添加WebSocket监听器失败: %v", err)
	}

	return nil
}

// checkListenersHealth 检查监听器健康状态
func (s *Server) checkListenersHealth() error {
	// 检查TCP端口是否可用
	if err := s.checkTCPPort(); err != nil {
		return err
	}

	// 检查WebSocket端口是否可用
	if err := s.checkWebSocketPort(); err != nil {
		return err
	}

	return nil
}

// checkTCPPort 检查TCP端口是否可访问
func (s *Server) checkTCPPort() error {
	tcpAddr := fmt.Sprintf(":%s", s.config.TCPPort)
	conn, err := net.DialTimeout("tcp", tcpAddr, 5*time.Second)
	if err != nil {
		return fmt.Errorf("TCP端口 %s 不可访问: %v", s.config.TCPPort, err)
	}
	conn.Close()
	return nil
}

// checkWebSocketPort 检查WebSocket端口是否可访问
func (s *Server) checkWebSocketPort() error {
	wsURL := fmt.Sprintf("http://localhost:%s", s.config.WebSocketPort)
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(wsURL)
	if err != nil {
		return fmt.Errorf("WebSocket端口 %s 不可访问: %v", s.config.WebSocketPort, err)
	}
	resp.Body.Close()
	return nil
}
