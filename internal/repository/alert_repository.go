package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// AlertRuleRepository 预警规则仓储接口
type AlertRuleRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, rule *model.AlertRule) error
	GetByID(ctx context.Context, id string) (*model.AlertRule, error)
	Update(ctx context.Context, rule *model.AlertRule) error
	Delete(ctx context.Context, id string) error

	// 查询操作
	List(ctx context.Context, filter AlertRuleFilter, pagination Pagination) ([]*model.AlertRule, int64, error)
	GetByGroupAndTopic(ctx context.Context, groupID, topic string) ([]*model.AlertRule, error)
	GetEnabledRules(ctx context.Context) ([]*model.AlertRule, error)
	GetByGroupID(ctx context.Context, groupID string) ([]*model.AlertRule, error)

	// 统计操作
	Count(ctx context.Context, filter AlertRuleFilter) (int64, error)
	CountByStatus(ctx context.Context, enabled bool) (int64, error)
}

// AlertRuleFilter 预警规则过滤器
type AlertRuleFilter struct {
	GroupID   string              `json:"group_id,omitempty"`
	Topic     string              `json:"topic,omitempty"`
	RuleType  model.AlertRuleType `json:"rule_type,omitempty"`
	Level     model.AlertLevel    `json:"level,omitempty"`
	Enabled   *bool               `json:"enabled,omitempty"`
	CreatedBy string              `json:"created_by,omitempty"`
	Search    string              `json:"search,omitempty"` // 搜索规则名称或描述
}

// alertRuleRepository 预警规则仓储实现
type alertRuleRepository struct {
	collection *mongo.Collection
}

// NewAlertRuleRepository 创建预警规则仓储
func NewAlertRuleRepository(db *mongo.Database) AlertRuleRepository {
	return &alertRuleRepository{
		collection: db.Collection("alert_rules"),
	}
}

// Create 创建预警规则
func (r *alertRuleRepository) Create(ctx context.Context, rule *model.AlertRule) error {
	// 设置创建时间
	rule.BeforeCreate()

	// 检查是否已存在相同的规则
	filter := bson.M{
		"group_id":  rule.GroupID,
		"topic":     rule.Topic,
		"rule_name": rule.RuleName,
	}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return fmt.Errorf("检查预警规则是否存在失败: %w", err)
	}
	if count > 0 {
		return model.ErrAlertRuleExists
	}

	result, err := r.collection.InsertOne(ctx, rule)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrAlertRuleExists
		}
		return fmt.Errorf("创建预警规则失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		rule.ID = oid
	}

	return nil
}

// GetByID 根据ID获取预警规则
func (r *alertRuleRepository) GetByID(ctx context.Context, id string) (*model.AlertRule, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("无效的预警规则ID: %w", err)
	}

	var rule model.AlertRule
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&rule)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrAlertRuleNotFound
		}
		return nil, fmt.Errorf("获取预警规则失败: %w", err)
	}

	return &rule, nil
}

// Update 更新预警规则
func (r *alertRuleRepository) Update(ctx context.Context, rule *model.AlertRule) error {
	rule.BeforeUpdate()

	filter := bson.M{"_id": rule.ID}
	update := bson.M{"$set": rule}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrAlertRuleExists
		}
		return fmt.Errorf("更新预警规则失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrAlertRuleNotFound
	}

	return nil
}

// Delete 删除预警规则
func (r *alertRuleRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的预警规则ID: %w", err)
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除预警规则失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrAlertRuleNotFound
	}

	return nil
}

// List 获取预警规则列表
func (r *alertRuleRepository) List(ctx context.Context, filter AlertRuleFilter, pagination Pagination) ([]*model.AlertRule, int64, error) {
	// 构建查询条件
	query := r.buildQuery(filter)

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计预警规则数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询预警规则列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var rules []*model.AlertRule
	for cursor.Next(ctx) {
		var rule model.AlertRule
		if err := cursor.Decode(&rule); err != nil {
			continue
		}
		rules = append(rules, &rule)
	}

	return rules, total, nil
}

// GetByGroupAndTopic 根据分组和Topic获取预警规则
func (r *alertRuleRepository) GetByGroupAndTopic(ctx context.Context, groupID, topic string) ([]*model.AlertRule, error) {
	filter := bson.M{
		"group_id": groupID,
		"topic":    topic,
		"enabled":  true,
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询预警规则失败: %w", err)
	}
	defer cursor.Close(ctx)

	var rules []*model.AlertRule
	for cursor.Next(ctx) {
		var rule model.AlertRule
		if err := cursor.Decode(&rule); err != nil {
			continue
		}
		rules = append(rules, &rule)
	}

	return rules, nil
}

// GetEnabledRules 获取所有启用的预警规则
func (r *alertRuleRepository) GetEnabledRules(ctx context.Context) ([]*model.AlertRule, error) {
	filter := bson.M{"enabled": true}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询启用的预警规则失败: %w", err)
	}
	defer cursor.Close(ctx)

	var rules []*model.AlertRule
	for cursor.Next(ctx) {
		var rule model.AlertRule
		if err := cursor.Decode(&rule); err != nil {
			continue
		}
		rules = append(rules, &rule)
	}

	return rules, nil
}

// GetByGroupID 根据分组ID获取预警规则
func (r *alertRuleRepository) GetByGroupID(ctx context.Context, groupID string) ([]*model.AlertRule, error) {
	filter := bson.M{"group_id": groupID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询分组预警规则失败: %w", err)
	}
	defer cursor.Close(ctx)

	var rules []*model.AlertRule
	for cursor.Next(ctx) {
		var rule model.AlertRule
		if err := cursor.Decode(&rule); err != nil {
			continue
		}
		rules = append(rules, &rule)
	}

	return rules, nil
}

// Count 统计预警规则数量
func (r *alertRuleRepository) Count(ctx context.Context, filter AlertRuleFilter) (int64, error) {
	query := r.buildQuery(filter)
	return r.collection.CountDocuments(ctx, query)
}

// CountByStatus 根据状态统计预警规则数量
func (r *alertRuleRepository) CountByStatus(ctx context.Context, enabled bool) (int64, error) {
	filter := bson.M{"enabled": enabled}
	return r.collection.CountDocuments(ctx, filter)
}

// buildQuery 构建查询条件
func (r *alertRuleRepository) buildQuery(filter AlertRuleFilter) bson.M {
	query := bson.M{}

	if filter.GroupID != "" {
		query["group_id"] = filter.GroupID
	}

	if filter.Topic != "" {
		query["topic"] = filter.Topic
	}

	if filter.RuleType != "" {
		query["rule_type"] = filter.RuleType
	}

	if filter.Level > 0 {
		query["level"] = filter.Level
	}

	if filter.Enabled != nil {
		query["enabled"] = *filter.Enabled
	}

	if filter.CreatedBy != "" {
		query["created_by"] = filter.CreatedBy
	}

	if filter.Search != "" {
		query["$or"] = []bson.M{
			{"rule_name": bson.M{"$regex": filter.Search, "$options": "i"}},
			{"description": bson.M{"$regex": filter.Search, "$options": "i"}},
		}
	}

	return query
}
