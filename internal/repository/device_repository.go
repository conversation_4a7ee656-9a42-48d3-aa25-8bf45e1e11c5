package repository

import (
	"beacon/cloud/internal/model"
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DeviceFilter 设备过滤器
type DeviceFilter struct {
	UserID       string                 `json:"user_id,omitempty"`
	DeviceType   string                 `json:"device_type,omitempty"`
	Status       model.DeviceStatus     `json:"status,omitempty"`
	Assignment   model.DeviceAssignment `json:"assignment,omitempty"`
	Search       string                 `json:"search,omitempty"` // 搜索设备名称、序列号、IMEI码
	SerialNumber string                 `json:"serial_number,omitempty"`
	IMEICode     string                 `json:"imei_code,omitempty"`
}

// DeviceRepository 设备仓储接口
type DeviceRepository interface {
	Create(ctx context.Context, device *model.Device) error
	GetByID(ctx context.Context, id string) (*model.Device, error)
	GetBySerialNumber(ctx context.Context, serialNumber string) (*model.Device, error)
	GetByIMEICode(ctx context.Context, imeiCode string) (*model.Device, error)
	GetBySerialNumberAndIMEI(ctx context.Context, serialNumber, imeiCode string) (*model.Device, error)
	Update(ctx context.Context, device *model.Device) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filter DeviceFilter, pagination Pagination) ([]*model.Device, int64, error)
	ExistsBySerialNumber(ctx context.Context, serialNumber string) (bool, error)
	ExistsByIMEICode(ctx context.Context, imeiCode string) (bool, error)
	AssignToUser(ctx context.Context, deviceID string, userID primitive.ObjectID) error
	UnassignFromUser(ctx context.Context, deviceID string) error
	GetUserDevices(ctx context.Context, userID string, pagination Pagination) ([]*model.Device, int64, error)
	CreateIndexes(ctx context.Context) error
}

// deviceRepository 设备仓储实现
type deviceRepository struct {
	collection *mongo.Collection
}

// NewDeviceRepository 创建设备仓储
func NewDeviceRepository(db *mongo.Database) DeviceRepository {
	return &deviceRepository{
		collection: db.Collection("devices"),
	}
}

// Create 创建设备
func (r *deviceRepository) Create(ctx context.Context, device *model.Device) error {
	// 设置创建时间
	device.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, device)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrDeviceAlreadyExists
		}
		return fmt.Errorf("创建设备失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		device.ID = oid
	}

	return nil
}

// GetByID 根据ID获取设备
func (r *deviceRepository) GetByID(ctx context.Context, id string) (*model.Device, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var device model.Device
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	return &device, nil
}

// GetBySerialNumber 根据序列号获取设备（全局查找）
func (r *deviceRepository) GetBySerialNumber(ctx context.Context, serialNumber string) (*model.Device, error) {
	var device model.Device
	filter := bson.M{
		"serial_number": serialNumber,
	}
	err := r.collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	return &device, nil
}

// GetByIMEICode 根据IMEI码获取设备（全局查找）
func (r *deviceRepository) GetByIMEICode(ctx context.Context, imeiCode string) (*model.Device, error) {
	var device model.Device
	filter := bson.M{
		"imei_code": imeiCode,
	}
	err := r.collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	return &device, nil
}

// GetBySerialNumberAndIMEI 根据序列号和IMEI码获取设备
func (r *deviceRepository) GetBySerialNumberAndIMEI(ctx context.Context, serialNumber, imeiCode string) (*model.Device, error) {
	var device model.Device
	filter := bson.M{
		"serial_number": serialNumber,
		"imei_code":     imeiCode,
	}
	err := r.collection.FindOne(ctx, filter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceNotFound
		}
		return nil, fmt.Errorf("查询设备失败: %w", err)
	}

	return &device, nil
}

// Update 更新设备
func (r *deviceRepository) Update(ctx context.Context, device *model.Device) error {
	// 设置更新时间
	device.BeforeUpdate()

	filter := bson.M{"_id": device.ID}
	update := bson.M{"$set": device}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrDeviceAlreadyExists
		}
		return fmt.Errorf("更新设备失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrDeviceNotFound
	}

	return nil
}

// Delete 删除设备
func (r *deviceRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除设备失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrDeviceNotFound
	}

	return nil
}

// List 获取设备列表
func (r *deviceRepository) List(ctx context.Context, filter DeviceFilter, pagination Pagination) ([]*model.Device, int64, error) {
	// 构建查询条件
	query := bson.M{}

	if filter.UserID != "" {
		userObjectID, err := primitive.ObjectIDFromHex(filter.UserID)
		if err != nil {
			return nil, 0, model.ErrInvalidID
		}
		query["user_id"] = userObjectID
	}

	if filter.DeviceType != "" {
		query["device_type"] = filter.DeviceType
	}

	if filter.Status != "" {
		query["status"] = filter.Status
	}

	if filter.SerialNumber != "" {
		query["serial_number"] = filter.SerialNumber
	}

	if filter.IMEICode != "" {
		query["imei_code"] = filter.IMEICode
	}

	// 搜索功能
	if filter.Search != "" {
		query["$or"] = []bson.M{
			{"device_name": bson.M{"$regex": filter.Search, "$options": "i"}},
			{"serial_number": bson.M{"$regex": filter.Search, "$options": "i"}},
			{"imei_code": bson.M{"$regex": filter.Search, "$options": "i"}},
		}
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计设备数量失败: %w", err)
	}

	// 构建查询选项
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.M{"created_at": -1}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询设备列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var devices []*model.Device
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, 0, fmt.Errorf("解析设备列表失败: %w", err)
	}

	return devices, total, nil
}

// ExistsBySerialNumber 检查序列号是否存在（全局检查）
func (r *deviceRepository) ExistsBySerialNumber(ctx context.Context, serialNumber string) (bool, error) {
	filter := bson.M{
		"serial_number": serialNumber,
	}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("检查序列号失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByIMEICode 检查IMEI码是否存在（全局检查）
func (r *deviceRepository) ExistsByIMEICode(ctx context.Context, imeiCode string) (bool, error) {
	filter := bson.M{
		"imei_code": imeiCode,
	}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("检查IMEI码失败: %w", err)
	}
	return count > 0, nil
}

// AssignToUser 将设备分配给用户
func (r *deviceRepository) AssignToUser(ctx context.Context, deviceID string, userID primitive.ObjectID) error {
	objectID, err := primitive.ObjectIDFromHex(deviceID)
	if err != nil {
		return model.ErrInvalidID
	}

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$set": bson.M{
			"user_id":    userID,
			"assignment": model.DeviceAssignmentAssigned,
			"updated_at": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("分配设备失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrDeviceNotFound
	}

	return nil
}

// UnassignFromUser 取消设备分配
func (r *deviceRepository) UnassignFromUser(ctx context.Context, deviceID string) error {
	objectID, err := primitive.ObjectIDFromHex(deviceID)
	if err != nil {
		return model.ErrInvalidID
	}

	filter := bson.M{"_id": objectID}
	update := bson.M{
		"$unset": bson.M{
			"user_id": "",
		},
		"$set": bson.M{
			"assignment": model.DeviceAssignmentUnassigned,
			"updated_at": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("取消设备分配失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrDeviceNotFound
	}

	return nil
}

// GetUserDevices 获取用户的设备列表
func (r *deviceRepository) GetUserDevices(ctx context.Context, userID string, pagination Pagination) ([]*model.Device, int64, error) {
	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return nil, 0, model.ErrInvalidID
	}

	filter := bson.M{
		"user_id": userObjectID,
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("统计用户设备数量失败: %w", err)
	}

	// 构建查询选项
	opts := options.Find()
	if pagination.Page > 0 && pagination.PageSize > 0 {
		skip := (pagination.Page - 1) * pagination.PageSize
		opts.SetSkip(int64(skip)).SetLimit(int64(pagination.PageSize))
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})

	// 查询设备
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户设备失败: %w", err)
	}
	defer cursor.Close(ctx)

	var devices []*model.Device
	for cursor.Next(ctx) {
		var device model.Device
		if err := cursor.Decode(&device); err != nil {
			return nil, 0, fmt.Errorf("解码设备数据失败: %w", err)
		}
		devices = append(devices, &device)
	}

	if err := cursor.Err(); err != nil {
		return nil, 0, fmt.Errorf("遍历设备数据失败: %w", err)
	}

	return devices, total, nil
}

// CreateIndexes 创建索引
func (r *deviceRepository) CreateIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "serial_number", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "imei_code", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "device_type", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "user_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "status", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "assignment", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "created_at", Value: -1}},
		},
	}

	_, err := r.collection.Indexes().CreateMany(ctx, indexes)
	if err != nil {
		return fmt.Errorf("创建设备索引失败: %w", err)
	}

	return nil
}
