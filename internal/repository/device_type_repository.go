package repository

import (
	"beacon/cloud/internal/model"
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DeviceTypeRepository 设备类型仓储接口
type DeviceTypeRepository interface {
	Create(ctx context.Context, deviceType *model.DeviceType) error
	GetByID(ctx context.Context, id string) (*model.DeviceType, error)
	GetByName(ctx context.Context, name string) (*model.DeviceType, error)
	Update(ctx context.Context, deviceType *model.DeviceType) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, pagination Pagination) ([]*model.DeviceType, int64, error)
	ExistsByName(ctx context.Context, name string) (bool, error)
	CreateIndexes(ctx context.Context) error
}

// deviceTypeRepository 设备类型仓储实现
type deviceTypeRepository struct {
	collection *mongo.Collection
}

// NewDeviceTypeRepository 创建设备类型仓储
func NewDeviceTypeRepository(db *mongo.Database) DeviceTypeRepository {
	return &deviceTypeRepository{
		collection: db.Collection("device_types"),
	}
}

// Create 创建设备类型
func (r *deviceTypeRepository) Create(ctx context.Context, deviceType *model.DeviceType) error {
	// 设置创建时间
	deviceType.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, deviceType)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrDeviceTypeAlreadyExists
		}
		return fmt.Errorf("创建设备类型失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		deviceType.ID = oid
	}

	return nil
}

// GetByID 根据ID获取设备类型
func (r *deviceTypeRepository) GetByID(ctx context.Context, id string) (*model.DeviceType, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var deviceType model.DeviceType
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&deviceType)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceTypeNotFound
		}
		return nil, fmt.Errorf("查询设备类型失败: %w", err)
	}

	return &deviceType, nil
}

// GetByName 根据名称获取设备类型
func (r *deviceTypeRepository) GetByName(ctx context.Context, name string) (*model.DeviceType, error) {
	var deviceType model.DeviceType
	err := r.collection.FindOne(ctx, bson.M{"name": name}).Decode(&deviceType)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrDeviceTypeNotFound
		}
		return nil, fmt.Errorf("查询设备类型失败: %w", err)
	}

	return &deviceType, nil
}

// Update 更新设备类型
func (r *deviceTypeRepository) Update(ctx context.Context, deviceType *model.DeviceType) error {
	// 设置更新时间
	deviceType.BeforeUpdate()

	filter := bson.M{"_id": deviceType.ID}
	update := bson.M{"$set": deviceType}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrDeviceTypeAlreadyExists
		}
		return fmt.Errorf("更新设备类型失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrDeviceTypeNotFound
	}

	return nil
}

// Delete 删除设备类型
func (r *deviceTypeRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除设备类型失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrDeviceTypeNotFound
	}

	return nil
}

// List 获取设备类型列表
func (r *deviceTypeRepository) List(ctx context.Context, pagination Pagination) ([]*model.DeviceType, int64, error) {
	// 计算总数
	total, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, 0, fmt.Errorf("统计设备类型数量失败: %w", err)
	}

	// 构建查询选项
	opts := options.Find()
	// 如果PageSize为0，表示获取所有数据，不设置分页
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.M{"created_at": -1}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询设备类型列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var deviceTypes []*model.DeviceType
	if err = cursor.All(ctx, &deviceTypes); err != nil {
		return nil, 0, fmt.Errorf("解析设备类型列表失败: %w", err)
	}

	return deviceTypes, total, nil
}

// ExistsByName 检查设备类型名称是否存在
func (r *deviceTypeRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"name": name})
	if err != nil {
		return false, fmt.Errorf("检查设备类型名称失败: %w", err)
	}
	return count > 0, nil
}

// CreateIndexes 创建索引
func (r *deviceTypeRepository) CreateIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "name", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	}

	_, err := r.collection.Indexes().CreateMany(ctx, indexes)
	if err != nil {
		return fmt.Errorf("创建设备类型索引失败: %w", err)
	}

	return nil
}
