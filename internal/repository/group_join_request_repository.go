package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GroupJoinRequestRepository 分组加入申请仓储接口
type GroupJoinRequestRepository interface {
	Create(ctx context.Context, request *model.GroupJoinRequest) error
	GetByID(ctx context.Context, id string) (*model.GroupJoinRequest, error)
	GetByGroupAndUser(ctx context.Context, groupID, userID string) (*model.GroupJoinRequest, error)
	Update(ctx context.Context, request *model.GroupJoinRequest) error
	Delete(ctx context.Context, id string) error
	ListByGroupID(ctx context.Context, groupID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error)
	ListByGroupIDs(ctx context.Context, groupIDs []string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error)
	ListByOwnerID(ctx context.Context, ownerID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error)
	ListByUserID(ctx context.Context, userID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error)
	ExistsByGroupAndUser(ctx context.Context, groupID, userID string) (bool, error)
	CountPendingByGroupID(ctx context.Context, groupID string) (int64, error)
}

// JoinRequestFilter 加入申请过滤器
type JoinRequestFilter struct {
	Status model.JoinRequestStatus `json:"status,omitempty"`
}

// groupJoinRequestRepository 分组加入申请仓储实现
type groupJoinRequestRepository struct {
	collection *mongo.Collection
}

// NewGroupJoinRequestRepository 创建分组加入申请仓储
func NewGroupJoinRequestRepository(db *mongo.Database) GroupJoinRequestRepository {
	return &groupJoinRequestRepository{
		collection: db.Collection("group_join_requests"),
	}
}

// Create 创建分组加入申请
func (r *groupJoinRequestRepository) Create(ctx context.Context, request *model.GroupJoinRequest) error {
	// 设置创建时间
	request.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, request)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrJoinRequestAlreadyExists
		}
		return fmt.Errorf("创建加入申请失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		request.ID = oid
	}

	return nil
}

// GetByID 根据ID获取加入申请
func (r *groupJoinRequestRepository) GetByID(ctx context.Context, id string) (*model.GroupJoinRequest, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var request model.GroupJoinRequest
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&request)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrJoinRequestNotFound
		}
		return nil, fmt.Errorf("查询加入申请失败: %w", err)
	}

	return &request, nil
}

// GetByGroupAndUser 根据分组ID和用户ID获取加入申请
func (r *groupJoinRequestRepository) GetByGroupAndUser(ctx context.Context, groupID, userID string) (*model.GroupJoinRequest, error) {
	var request model.GroupJoinRequest
	err := r.collection.FindOne(ctx, bson.M{
		"group_id": groupID,
		"user_id":  userID,
	}).Decode(&request)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrJoinRequestNotFound
		}
		return nil, fmt.Errorf("查询加入申请失败: %w", err)
	}

	return &request, nil
}

// Update 更新加入申请
func (r *groupJoinRequestRepository) Update(ctx context.Context, request *model.GroupJoinRequest) error {
	// 设置更新时间
	request.BeforeUpdate()

	filter := bson.M{"_id": request.ID}
	update := bson.M{"$set": request}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("更新加入申请失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrJoinRequestNotFound
	}

	return nil
}

// Delete 删除加入申请
func (r *groupJoinRequestRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除加入申请失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrJoinRequestNotFound
	}

	return nil
}

// ListByGroupID 根据分组ID获取加入申请列表
func (r *groupJoinRequestRepository) ListByGroupID(ctx context.Context, groupID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error) {
	query := bson.M{"group_id": groupID}

	if filter.Status != "" {
		query["status"] = filter.Status
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计加入申请数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询加入申请列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var requests []*model.GroupJoinRequest
	if err = cursor.All(ctx, &requests); err != nil {
		return nil, 0, fmt.Errorf("解析加入申请列表失败: %w", err)
	}

	return requests, total, nil
}

// ListByGroupIDs 根据多个分组ID获取加入申请列表
func (r *groupJoinRequestRepository) ListByGroupIDs(ctx context.Context, groupIDs []string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error) {
	query := bson.M{"group_id": bson.M{"$in": groupIDs}}

	if filter.Status != "" {
		query["status"] = filter.Status
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计加入申请数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询加入申请列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var requests []*model.GroupJoinRequest
	if err = cursor.All(ctx, &requests); err != nil {
		return nil, 0, fmt.Errorf("解析加入申请列表失败: %w", err)
	}

	return requests, total, nil
}

// ListByOwnerID 根据分组创建者ID获取加入申请列表（性能优化版本）
func (r *groupJoinRequestRepository) ListByOwnerID(ctx context.Context, ownerID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error) {
	query := bson.M{"owner_id": ownerID}

	if filter.Status != "" {
		query["status"] = filter.Status
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计加入申请数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询加入申请列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var requests []*model.GroupJoinRequest
	if err = cursor.All(ctx, &requests); err != nil {
		return nil, 0, fmt.Errorf("解析加入申请列表失败: %w", err)
	}

	return requests, total, nil
}

// ListByUserID 根据用户ID获取加入申请列表
func (r *groupJoinRequestRepository) ListByUserID(ctx context.Context, userID string, filter JoinRequestFilter, pagination Pagination) ([]*model.GroupJoinRequest, int64, error) {
	query := bson.M{"user_id": userID}

	if filter.Status != "" {
		query["status"] = filter.Status
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计用户加入申请数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户加入申请列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var requests []*model.GroupJoinRequest
	if err = cursor.All(ctx, &requests); err != nil {
		return nil, 0, fmt.Errorf("解析用户加入申请列表失败: %w", err)
	}

	return requests, total, nil
}

// ExistsByGroupAndUser 检查用户是否已有加入申请
func (r *groupJoinRequestRepository) ExistsByGroupAndUser(ctx context.Context, groupID, userID string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{
		"group_id": groupID,
		"user_id":  userID,
		"status":   model.JoinRequestPending, // 只检查待处理的申请
	})
	if err != nil {
		return false, fmt.Errorf("检查加入申请是否存在失败: %w", err)
	}
	return count > 0, nil
}

// CountPendingByGroupID 统计分组待处理的加入申请数量
func (r *groupJoinRequestRepository) CountPendingByGroupID(ctx context.Context, groupID string) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{
		"group_id": groupID,
		"status":   model.JoinRequestPending,
	})
	if err != nil {
		return 0, fmt.Errorf("统计待处理加入申请数量失败: %w", err)
	}
	return count, nil
}
