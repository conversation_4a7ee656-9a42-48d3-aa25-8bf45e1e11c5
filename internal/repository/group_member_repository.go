package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GroupMemberRepository 分组成员仓储接口
type GroupMemberRepository interface {
	Create(ctx context.Context, member *model.GroupMember) error
	GetByID(ctx context.Context, id string) (*model.GroupMember, error)
	GetByGroupAndUser(ctx context.Context, groupID, userID string) (*model.GroupMember, error)
	Delete(ctx context.Context, id string) error
	DeleteByGroupAndUser(ctx context.Context, groupID, userID string) error
	ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.GroupMember, int64, error)
	ListByUserID(ctx context.Context, userID string, pagination Pagination) ([]*model.GroupMember, int64, error)
	CountByGroupID(ctx context.Context, groupID string) (int64, error)
	ExistsByGroupAndUser(ctx context.Context, groupID, userID string) (bool, error)
}

// groupMemberRepository 分组成员仓储实现
type groupMemberRepository struct {
	collection *mongo.Collection
}

// NewGroupMemberRepository 创建分组成员仓储
func NewGroupMemberRepository(db *mongo.Database) GroupMemberRepository {
	return &groupMemberRepository{
		collection: db.Collection("group_members"),
	}
}

// Create 创建分组成员
func (r *groupMemberRepository) Create(ctx context.Context, member *model.GroupMember) error {
	// 设置创建时间
	member.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, member)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrAlreadyGroupMember
		}
		return fmt.Errorf("创建分组成员失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		member.ID = oid
	}

	return nil
}

// GetByID 根据ID获取分组成员
func (r *groupMemberRepository) GetByID(ctx context.Context, id string) (*model.GroupMember, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var member model.GroupMember
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&member)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrNotGroupMember
		}
		return nil, fmt.Errorf("查询分组成员失败: %w", err)
	}

	return &member, nil
}

// GetByGroupAndUser 根据分组ID和用户ID获取成员
func (r *groupMemberRepository) GetByGroupAndUser(ctx context.Context, groupID, userID string) (*model.GroupMember, error) {
	var member model.GroupMember
	err := r.collection.FindOne(ctx, bson.M{
		"group_id": groupID,
		"user_id":  userID,
	}).Decode(&member)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrNotGroupMember
		}
		return nil, fmt.Errorf("查询分组成员失败: %w", err)
	}

	return &member, nil
}

// Delete 删除分组成员
func (r *groupMemberRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除分组成员失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrNotGroupMember
	}

	return nil
}

// DeleteByGroupAndUser 根据分组ID和用户ID删除成员
func (r *groupMemberRepository) DeleteByGroupAndUser(ctx context.Context, groupID, userID string) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{
		"group_id": groupID,
		"user_id":  userID,
	})
	if err != nil {
		return fmt.Errorf("删除分组成员失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrNotGroupMember
	}

	return nil
}

// ListByGroupID 根据分组ID获取成员列表
func (r *groupMemberRepository) ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.GroupMember, int64, error) {
	query := bson.M{"group_id": groupID}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计分组成员数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "joined_at", Value: 1}}) // 按加入时间正序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询分组成员列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var members []*model.GroupMember
	if err = cursor.All(ctx, &members); err != nil {
		return nil, 0, fmt.Errorf("解析分组成员列表失败: %w", err)
	}

	return members, total, nil
}

// ListByUserID 根据用户ID获取加入的分组列表
func (r *groupMemberRepository) ListByUserID(ctx context.Context, userID string, pagination Pagination) ([]*model.GroupMember, int64, error) {
	query := bson.M{"user_id": userID}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计用户分组数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "joined_at", Value: -1}}) // 按加入时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户分组列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var members []*model.GroupMember
	if err = cursor.All(ctx, &members); err != nil {
		return nil, 0, fmt.Errorf("解析用户分组列表失败: %w", err)
	}

	return members, total, nil
}

// CountByGroupID 统计分组成员数量
func (r *groupMemberRepository) CountByGroupID(ctx context.Context, groupID string) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"group_id": groupID})
	if err != nil {
		return 0, fmt.Errorf("统计分组成员数量失败: %w", err)
	}
	return count, nil
}

// ExistsByGroupAndUser 检查用户是否为分组成员
func (r *groupMemberRepository) ExistsByGroupAndUser(ctx context.Context, groupID, userID string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{
		"group_id": groupID,
		"user_id":  userID,
	})
	if err != nil {
		return false, fmt.Errorf("检查分组成员是否存在失败: %w", err)
	}
	return count > 0, nil
}
