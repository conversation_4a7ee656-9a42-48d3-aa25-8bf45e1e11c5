package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GroupRepository 分组仓储接口
type GroupRepository interface {
	Create(ctx context.Context, group *model.Group) error
	GetByID(ctx context.Context, id string) (*model.Group, error)
	GetByGroupID(ctx context.Context, groupID string) (*model.Group, error)
	Update(ctx context.Context, group *model.Group) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filter GroupFilter, pagination Pagination) ([]*model.Group, int64, error)
	GetByCreatorID(ctx context.Context, creatorID string) ([]*model.Group, error)
	CountByCreatorID(ctx context.Context, creatorID string) (int64, error)
	ExistsByName(ctx context.Context, name string, creatorID string) (bool, error)
}

// GroupFilter 分组过滤器
type GroupFilter struct {
	CreatorID string             `json:"creator_id,omitempty"`
	Status    model.GroupStatus  `json:"status,omitempty"`
	Search    string             `json:"search,omitempty"` // 搜索分组名称
}

// groupRepository 分组仓储实现
type groupRepository struct {
	collection *mongo.Collection
}

// NewGroupRepository 创建分组仓储
func NewGroupRepository(db *mongo.Database) GroupRepository {
	return &groupRepository{
		collection: db.Collection("groups"),
	}
}

// Create 创建分组
func (r *groupRepository) Create(ctx context.Context, group *model.Group) error {
	// 设置创建时间
	group.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, group)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrGroupAlreadyExists
		}
		return fmt.Errorf("创建分组失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		group.ID = oid
	}

	return nil
}

// GetByID 根据ID获取分组
func (r *groupRepository) GetByID(ctx context.Context, id string) (*model.Group, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var group model.Group
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&group)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrGroupNotFound
		}
		return nil, fmt.Errorf("查询分组失败: %w", err)
	}

	return &group, nil
}

// GetByGroupID 根据分组ID获取分组
func (r *groupRepository) GetByGroupID(ctx context.Context, groupID string) (*model.Group, error) {
	var group model.Group
	err := r.collection.FindOne(ctx, bson.M{"group_id": groupID}).Decode(&group)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrGroupNotFound
		}
		return nil, fmt.Errorf("查询分组失败: %w", err)
	}

	return &group, nil
}

// Update 更新分组
func (r *groupRepository) Update(ctx context.Context, group *model.Group) error {
	// 设置更新时间
	group.BeforeUpdate()

	filter := bson.M{"_id": group.ID}
	update := bson.M{"$set": group}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrGroupAlreadyExists
		}
		return fmt.Errorf("更新分组失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrGroupNotFound
	}

	return nil
}

// Delete 删除分组
func (r *groupRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除分组失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrGroupNotFound
	}

	return nil
}

// List 获取分组列表
func (r *groupRepository) List(ctx context.Context, filter GroupFilter, pagination Pagination) ([]*model.Group, int64, error) {
	// 构建查询条件
	query := bson.M{}

	if filter.CreatorID != "" {
		query["creator_id"] = filter.CreatorID
	}
	if filter.Status != "" {
		query["status"] = filter.Status
	}
	if filter.Search != "" {
		query["name"] = bson.M{"$regex": filter.Search, "$options": "i"}
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计分组数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询分组列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var groups []*model.Group
	if err = cursor.All(ctx, &groups); err != nil {
		return nil, 0, fmt.Errorf("解析分组列表失败: %w", err)
	}

	return groups, total, nil
}

// GetByCreatorID 根据创建者ID获取分组列表
func (r *groupRepository) GetByCreatorID(ctx context.Context, creatorID string) ([]*model.Group, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"creator_id": creatorID})
	if err != nil {
		return nil, fmt.Errorf("查询用户分组失败: %w", err)
	}
	defer cursor.Close(ctx)

	var groups []*model.Group
	if err = cursor.All(ctx, &groups); err != nil {
		return nil, fmt.Errorf("解析用户分组失败: %w", err)
	}

	return groups, nil
}

// CountByCreatorID 统计用户创建的分组数量
func (r *groupRepository) CountByCreatorID(ctx context.Context, creatorID string) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"creator_id": creatorID})
	if err != nil {
		return 0, fmt.Errorf("统计用户分组数量失败: %w", err)
	}
	return count, nil
}

// ExistsByName 检查分组名称是否存在（同一创建者下）
func (r *groupRepository) ExistsByName(ctx context.Context, name string, creatorID string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{
		"name":       name,
		"creator_id": creatorID,
	})
	if err != nil {
		return false, fmt.Errorf("检查分组名称是否存在失败: %w", err)
	}
	return count > 0, nil
}
