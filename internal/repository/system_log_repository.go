package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/pkg/database"
)

// SystemLogRepository 系统日志仓库接口
type SystemLogRepository interface {
	Create(ctx context.Context, log *model.SystemLog) error
	BatchCreate(ctx context.Context, logs []*model.SystemLog) error
	List(ctx context.Context, filter *model.SystemLogFilter) ([]*model.SystemLog, int64, error)
	GetByID(ctx context.Context, id string) (*model.SystemLog, error)
	Delete(ctx context.Context, id string) error
	DeleteBefore(ctx context.Context, before time.Time) (int64, error)
	GetStatistics(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
}

// systemLogRepository 系统日志仓库实现
type systemLogRepository struct {
	tdengine *database.TDengineManager
}

// NewSystemLogRepository 创建系统日志仓库
func NewSystemLogRepository(tdengine *database.TDengineManager) SystemLogRepository {
	repo := &systemLogRepository{
		tdengine: tdengine,
	}

	// 初始化日志表
	repo.initTables()

	return repo
}

// initTables 初始化日志表
func (r *systemLogRepository) initTables() {
	ctx := context.Background()

	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	// 创建系统日志超级表
	createSuperTableSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s.system_logs (
			ts TIMESTAMP,
			level NCHAR(10),
			module NCHAR(20),
			action NCHAR(30),
			message NCHAR(500),
			user_id NCHAR(50),
			ip NCHAR(50),
			user_agent NCHAR(200),
			details NCHAR(1000)
		) TAGS (
			log_date NCHAR(10)
		)
	`, dbName)

	if err := r.tdengine.ExecuteSQL(ctx, createSuperTableSQL); err != nil {
		// 日志错误但不阻止启动
		fmt.Printf("创建系统日志超级表失败: %v\n", err)
	}
}

// Create 创建系统日志
func (r *systemLogRepository) Create(ctx context.Context, log *model.SystemLog) error {
	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	// 生成表名（按日期分表）
	tableName := fmt.Sprintf("system_logs_%s", log.Timestamp.Format("20060102"))
	dateTag := log.Timestamp.Format("2006-01-02")

	// 创建子表（如果不存在）
	createTableSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s.%s USING %s.system_logs TAGS ('%s')
	`, dbName, tableName, dbName, dateTag)

	if err := r.tdengine.ExecuteSQL(ctx, createTableSQL); err != nil {
		return fmt.Errorf("创建日志子表失败: %w", err)
	}

	// 处理详细信息
	var detailsStr string
	if log.Details != nil {
		// 简单的JSON序列化（实际项目中应使用json.Marshal）
		parts := make([]string, 0, len(log.Details))
		for k, v := range log.Details {
			parts = append(parts, fmt.Sprintf(`"%s":"%v"`, k, v))
		}
		detailsStr = "{" + strings.Join(parts, ",") + "}"
	}

	// 插入日志数据
	insertSQL := fmt.Sprintf(`
		INSERT INTO %s.%s (ts, level, module, action, message, user_id, ip, user_agent, details)
		VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
	`, dbName, tableName,
		log.Timestamp.Format("2006-01-02 15:04:05.000"),
		string(log.Level),
		log.Module,
		log.Action,
		log.Message,
		log.UserID,
		log.IP,
		log.UserAgent,
		detailsStr)

	if err := r.tdengine.ExecuteSQL(ctx, insertSQL); err != nil {
		return fmt.Errorf("插入系统日志失败: %w", err)
	}

	return nil
}

// BatchCreate 批量创建系统日志
func (r *systemLogRepository) BatchCreate(ctx context.Context, logs []*model.SystemLog) error {
	if len(logs) == 0 {
		return nil
	}

	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	// 按日期分组
	logsByDate := make(map[string][]*model.SystemLog)
	for _, log := range logs {
		dateKey := log.Timestamp.Format("20060102")
		logsByDate[dateKey] = append(logsByDate[dateKey], log)
	}

	// 分别处理每个日期的日志
	for dateKey, dateLogs := range logsByDate {
		tableName := fmt.Sprintf("system_logs_%s", dateKey)
		dateTag := dateLogs[0].Timestamp.Format("2006-01-02")

		// 创建子表
		createTableSQL := fmt.Sprintf(`
			CREATE TABLE IF NOT EXISTS %s.%s USING %s.system_logs TAGS ('%s')
		`, dbName, tableName, dbName, dateTag)

		if err := r.tdengine.ExecuteSQL(ctx, createTableSQL); err != nil {
			return fmt.Errorf("创建日志子表失败: %w", err)
		}

		// 构建批量插入SQL
		valuesParts := make([]string, 0, len(dateLogs))

		for _, log := range dateLogs {
			var detailsStr string
			if log.Details != nil {
				parts := make([]string, 0, len(log.Details))
				for k, v := range log.Details {
					parts = append(parts, fmt.Sprintf(`"%s":"%v"`, k, v))
				}
				detailsStr = "{" + strings.Join(parts, ",") + "}"
			}

			valuePart := fmt.Sprintf("('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')",
				log.Timestamp.Format("2006-01-02 15:04:05.000"),
				string(log.Level),
				log.Module,
				log.Action,
				log.Message,
				log.UserID,
				log.IP,
				log.UserAgent,
				detailsStr,
			)
			valuesParts = append(valuesParts, valuePart)
		}

		insertSQL := fmt.Sprintf(`
			INSERT INTO %s.%s (ts, level, module, action, message, user_id, ip, user_agent, details)
			VALUES %s
		`, dbName, tableName, strings.Join(valuesParts, ","))

		if err := r.tdengine.ExecuteSQL(ctx, insertSQL); err != nil {
			return fmt.Errorf("批量插入系统日志失败: %w", err)
		}
	}

	return nil
}

// List 获取系统日志列表
func (r *systemLogRepository) List(ctx context.Context, filter *model.SystemLogFilter) ([]*model.SystemLog, int64, error) {
	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	// 构建查询条件
	var conditions []string

	// 时间范围条件
	if !filter.StartTime.IsZero() {
		conditions = append(conditions, fmt.Sprintf("ts >= '%s'", filter.StartTime.Format("2006-01-02 15:04:05")))
	}
	if !filter.EndTime.IsZero() {
		conditions = append(conditions, fmt.Sprintf("ts <= '%s'", filter.EndTime.Format("2006-01-02 15:04:05")))
	}

	// 其他过滤条件
	if filter.Level != "" {
		conditions = append(conditions, fmt.Sprintf("level = '%s'", string(filter.Level)))
	}
	if filter.Module != "" {
		conditions = append(conditions, fmt.Sprintf("module = '%s'", filter.Module))
	}
	if filter.Action != "" {
		conditions = append(conditions, fmt.Sprintf("action = '%s'", filter.Action))
	}
	if filter.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = '%s'", filter.UserID))
	}
	if filter.Search != "" {
		searchPattern := "%" + filter.Search + "%"
		conditions = append(conditions, fmt.Sprintf("(message LIKE '%s' OR action LIKE '%s')", searchPattern, searchPattern))
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM %s.system_logs %s", dbName, whereClause)
	var total int64
	countRows, err := r.tdengine.QuerySQL(ctx, countSQL)
	if err != nil {
		return nil, 0, fmt.Errorf("统计日志数量失败: %w", err)
	}
	defer countRows.Close()

	if countRows.Next() {
		if err := countRows.Scan(&total); err != nil {
			return nil, 0, fmt.Errorf("扫描总数失败: %w", err)
		}
	}

	// 构建查询SQL
	querySQL := fmt.Sprintf(`
		SELECT ts, level, module, action, message, user_id, ip, user_agent, details
		FROM %s.system_logs
		%s
		ORDER BY ts DESC
	`, dbName, whereClause)

	// 添加分页
	if filter.Page > 0 && filter.Limit > 0 {
		offset := (filter.Page - 1) * filter.Limit
		querySQL += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.Limit, offset)
	}

	// 执行查询
	rows, err := r.tdengine.QuerySQL(ctx, querySQL)
	if err != nil {
		return nil, 0, fmt.Errorf("查询系统日志失败: %w", err)
	}
	defer rows.Close()

	var logs []*model.SystemLog
	for rows.Next() {
		var log model.SystemLog
		var levelStr, detailsStr string

		err := rows.Scan(
			&log.Timestamp,
			&levelStr,
			&log.Module,
			&log.Action,
			&log.Message,
			&log.UserID,
			&log.IP,
			&log.UserAgent,
			&detailsStr,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描日志数据失败: %w", err)
		}

		log.Level = model.LogLevel(levelStr)

		// 解析详细信息（简化版本）
		if detailsStr != "" && detailsStr != "{}" {
			log.Details = make(map[string]interface{})
			// 实际项目中应使用json.Unmarshal
			log.Details["raw"] = detailsStr
		}

		logs = append(logs, &log)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("遍历日志数据失败: %w", err)
	}

	return logs, total, nil
}

// GetByID 根据ID获取系统日志（TDengine中使用时间戳作为标识）
func (r *systemLogRepository) GetByID(ctx context.Context, id string) (*model.SystemLog, error) {
	// 在TDengine中，我们使用时间戳作为ID
	// 这里简化处理，实际项目中可能需要更复杂的ID映射
	return nil, fmt.Errorf("TDengine不支持按ID查询，请使用时间范围查询")
}

// Delete 删除系统日志（TDengine中通常不删除单条记录）
func (r *systemLogRepository) Delete(ctx context.Context, id string) error {
	return fmt.Errorf("TDengine不支持删除单条日志记录")
}

// DeleteBefore 删除指定时间之前的日志
func (r *systemLogRepository) DeleteBefore(ctx context.Context, before time.Time) (int64, error) {
	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	// 删除指定时间之前的数据
	deleteSQL := fmt.Sprintf("DELETE FROM %s.system_logs WHERE ts < '%s'", dbName, before.Format("2006-01-02 15:04:05"))

	if err := r.tdengine.ExecuteSQL(ctx, deleteSQL); err != nil {
		return 0, fmt.Errorf("删除历史日志失败: %w", err)
	}

	// TDengine删除操作不返回影响行数，这里返回0
	return 0, nil
}

// GetStatistics 获取日志统计信息
func (r *systemLogRepository) GetStatistics(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error) {
	// 获取数据库名称
	dbName := r.tdengine.GetDBName()

	stats := make(map[string]interface{})

	// 按级别统计
	levelStatsSQL := fmt.Sprintf(`
		SELECT level, COUNT(*) as count
		FROM %s.system_logs
		WHERE ts >= '%s' AND ts <= '%s'
		GROUP BY level
	`, dbName, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))

	rows, err := r.tdengine.QuerySQL(ctx, levelStatsSQL)
	if err != nil {
		return nil, fmt.Errorf("查询级别统计失败: %w", err)
	}
	defer rows.Close()

	levelStats := make(map[string]int64)
	for rows.Next() {
		var level string
		var count int64
		if err := rows.Scan(&level, &count); err != nil {
			return nil, fmt.Errorf("扫描级别统计失败: %w", err)
		}
		levelStats[level] = count
	}
	stats["by_level"] = levelStats

	// 按模块统计
	moduleStatsSQL := fmt.Sprintf(`
		SELECT module, COUNT(*) as count
		FROM %s.system_logs
		WHERE ts >= '%s' AND ts <= '%s'
		GROUP BY module
	`, dbName, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))

	rows, err = r.tdengine.QuerySQL(ctx, moduleStatsSQL)
	if err != nil {
		return nil, fmt.Errorf("查询模块统计失败: %w", err)
	}
	defer rows.Close()

	moduleStats := make(map[string]int64)
	for rows.Next() {
		var module string
		var count int64
		if err := rows.Scan(&module, &count); err != nil {
			return nil, fmt.Errorf("扫描模块统计失败: %w", err)
		}
		moduleStats[module] = count
	}
	stats["by_module"] = moduleStats

	return stats, nil
}
