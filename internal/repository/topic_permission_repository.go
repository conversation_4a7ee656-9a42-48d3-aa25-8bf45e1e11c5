package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TopicPermissionRepository Topic权限仓储接口
type TopicPermissionRepository interface {
	Create(ctx context.Context, permission *model.TopicPermission) error
	GetByID(ctx context.Context, id string) (*model.TopicPermission, error)
	GetByUserAndTopic(ctx context.Context, userID, fullTopic string) (*model.TopicPermission, error)
	Update(ctx context.Context, permission *model.TopicPermission) error
	Upsert(ctx context.Context, permission *model.TopicPermission) error
	Delete(ctx context.Context, id string) error
	DeleteByUserAndTopic(ctx context.Context, userID, fullTopic string) error
	DeleteByTopic(ctx context.Context, fullTopic string) error
	DeleteByGroupID(ctx context.Context, groupID string) error
	ListByTopic(ctx context.Context, fullTopic string, pagination Pagination) ([]*model.TopicPermission, int64, error)
	ListByUserID(ctx context.Context, userID string, pagination Pagination) ([]*model.TopicPermission, int64, error)
	ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.TopicPermission, int64, error)
	GetUserTopicPermissions(ctx context.Context, userID string, topics []string) (map[string]*model.TopicPermission, error)
}

// TopicPermissionFilter Topic权限过滤器
type TopicPermissionFilter struct {
	UserID    string `json:"user_id,omitempty"`
	GroupID   string `json:"group_id,omitempty"`
	FullTopic string `json:"full_topic,omitempty"`
	CanPub    *bool  `json:"can_pub,omitempty"`
	CanSub    *bool  `json:"can_sub,omitempty"`
}

// topicPermissionRepository Topic权限仓储实现
type topicPermissionRepository struct {
	collection *mongo.Collection
}

// NewTopicPermissionRepository 创建Topic权限仓储
func NewTopicPermissionRepository(db *mongo.Database) TopicPermissionRepository {
	return &topicPermissionRepository{
		collection: db.Collection("topic_permissions"),
	}
}

// Create 创建Topic权限
func (r *topicPermissionRepository) Create(ctx context.Context, permission *model.TopicPermission) error {
	// 设置更新时间
	permission.BeforeUpdate()

	result, err := r.collection.InsertOne(ctx, permission)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrTopicPermissionDenied
		}
		return fmt.Errorf("创建Topic权限失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		permission.ID = oid
	}

	return nil
}

// GetByID 根据ID获取Topic权限
func (r *topicPermissionRepository) GetByID(ctx context.Context, id string) (*model.TopicPermission, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var permission model.TopicPermission
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&permission)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrTopicPermissionDenied
		}
		return nil, fmt.Errorf("查询Topic权限失败: %w", err)
	}

	return &permission, nil
}

// GetByUserAndTopic 根据用户ID和Topic获取权限
func (r *topicPermissionRepository) GetByUserAndTopic(ctx context.Context, userID, fullTopic string) (*model.TopicPermission, error) {
	var permission model.TopicPermission
	err := r.collection.FindOne(ctx, bson.M{
		"user_id":    userID,
		"full_topic": fullTopic,
	}).Decode(&permission)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrTopicPermissionDenied
		}
		return nil, fmt.Errorf("查询Topic权限失败: %w", err)
	}

	return &permission, nil
}

// Update 更新Topic权限
func (r *topicPermissionRepository) Update(ctx context.Context, permission *model.TopicPermission) error {
	// 设置更新时间
	permission.BeforeUpdate()

	filter := bson.M{"_id": permission.ID}
	update := bson.M{"$set": permission}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("更新Topic权限失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrTopicPermissionDenied
	}

	return nil
}

// Upsert 创建或更新Topic权限
func (r *topicPermissionRepository) Upsert(ctx context.Context, permission *model.TopicPermission) error {
	// 设置更新时间
	permission.BeforeUpdate()

	filter := bson.M{
		"user_id":    permission.UserID,
		"full_topic": permission.FullTopic,
	}
	update := bson.M{"$set": permission}
	opts := options.Update().SetUpsert(true)

	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("创建或更新Topic权限失败: %w", err)
	}

	// 如果是插入操作，设置生成的ID
	if result.UpsertedID != nil {
		if oid, ok := result.UpsertedID.(primitive.ObjectID); ok {
			permission.ID = oid
		}
	}

	return nil
}

// Delete 删除Topic权限
func (r *topicPermissionRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除Topic权限失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrTopicPermissionDenied
	}

	return nil
}

// DeleteByUserAndTopic 根据用户ID和Topic删除权限
func (r *topicPermissionRepository) DeleteByUserAndTopic(ctx context.Context, userID, fullTopic string) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{
		"user_id":    userID,
		"full_topic": fullTopic,
	})
	if err != nil {
		return fmt.Errorf("删除Topic权限失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrTopicPermissionDenied
	}

	return nil
}

// DeleteByTopic 删除Topic的所有权限
func (r *topicPermissionRepository) DeleteByTopic(ctx context.Context, fullTopic string) error {
	_, err := r.collection.DeleteMany(ctx, bson.M{"full_topic": fullTopic})
	if err != nil {
		return fmt.Errorf("删除Topic权限失败: %w", err)
	}

	return nil
}

// DeleteByGroupID 删除分组的所有权限
func (r *topicPermissionRepository) DeleteByGroupID(ctx context.Context, groupID string) error {
	_, err := r.collection.DeleteMany(ctx, bson.M{"group_id": groupID})
	if err != nil {
		return fmt.Errorf("删除分组权限失败: %w", err)
	}

	return nil
}

// ListByTopic 根据Topic获取权限列表
func (r *topicPermissionRepository) ListByTopic(ctx context.Context, fullTopic string, pagination Pagination) ([]*model.TopicPermission, int64, error) {
	query := bson.M{"full_topic": fullTopic}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计Topic权限数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "updated_at", Value: -1}}) // 按更新时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Topic权限列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var permissions []*model.TopicPermission
	if err = cursor.All(ctx, &permissions); err != nil {
		return nil, 0, fmt.Errorf("解析Topic权限列表失败: %w", err)
	}

	return permissions, total, nil
}

// ListByUserID 根据用户ID获取权限列表
func (r *topicPermissionRepository) ListByUserID(ctx context.Context, userID string, pagination Pagination) ([]*model.TopicPermission, int64, error) {
	query := bson.M{"user_id": userID}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计用户权限数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "updated_at", Value: -1}}) // 按更新时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户权限列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var permissions []*model.TopicPermission
	if err = cursor.All(ctx, &permissions); err != nil {
		return nil, 0, fmt.Errorf("解析用户权限列表失败: %w", err)
	}

	return permissions, total, nil
}

// ListByGroupID 根据分组ID获取权限列表
func (r *topicPermissionRepository) ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.TopicPermission, int64, error) {
	query := bson.M{"group_id": groupID}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计分组权限数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "updated_at", Value: -1}}) // 按更新时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询分组权限列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var permissions []*model.TopicPermission
	if err = cursor.All(ctx, &permissions); err != nil {
		return nil, 0, fmt.Errorf("解析分组权限列表失败: %w", err)
	}

	return permissions, total, nil
}

// GetUserTopicPermissions 批量获取用户对多个Topic的权限
func (r *topicPermissionRepository) GetUserTopicPermissions(ctx context.Context, userID string, topics []string) (map[string]*model.TopicPermission, error) {
	query := bson.M{
		"user_id":    userID,
		"full_topic": bson.M{"$in": topics},
	}

	cursor, err := r.collection.Find(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询用户Topic权限失败: %w", err)
	}
	defer cursor.Close(ctx)

	var permissions []*model.TopicPermission
	if err = cursor.All(ctx, &permissions); err != nil {
		return nil, fmt.Errorf("解析用户Topic权限失败: %w", err)
	}

	// 转换为map
	result := make(map[string]*model.TopicPermission)
	for _, permission := range permissions {
		result[permission.FullTopic] = permission
	}

	return result, nil
}
