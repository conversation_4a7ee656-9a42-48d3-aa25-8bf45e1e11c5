package repository

import (
	"context"
	"fmt"

	"beacon/cloud/internal/model"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TopicRepository Topic仓储接口
type TopicRepository interface {
	Create(ctx context.Context, topic *model.Topic) error
	GetByID(ctx context.Context, id string) (*model.Topic, error)
	GetByFullName(ctx context.Context, fullName string) (*model.Topic, error)
	Update(ctx context.Context, topic *model.Topic) error
	Delete(ctx context.Context, id string) error
	DeleteByFullName(ctx context.Context, fullName string) error
	ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.Topic, int64, error)
	CountByGroupID(ctx context.Context, groupID string) (int64, error)
	ExistsByGroupAndName(ctx context.Context, groupID, topicName string) (bool, error)
	ExistsByFullName(ctx context.Context, fullName string) (bool, error)
}

// TopicFilter Topic过滤器
type TopicFilter struct {
	GroupID   string `json:"group_id,omitempty"`
	CreatorID string `json:"creator_id,omitempty"`
	Search    string `json:"search,omitempty"` // 搜索Topic名称
}

// topicRepository Topic仓储实现
type topicRepository struct {
	collection *mongo.Collection
}

// NewTopicRepository 创建Topic仓储
func NewTopicRepository(db *mongo.Database) TopicRepository {
	return &topicRepository{
		collection: db.Collection("topics"),
	}
}

// Create 创建Topic
func (r *topicRepository) Create(ctx context.Context, topic *model.Topic) error {
	// 设置创建时间
	topic.BeforeCreate()

	result, err := r.collection.InsertOne(ctx, topic)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrTopicAlreadyExists
		}
		return fmt.Errorf("创建Topic失败: %w", err)
	}

	// 设置生成的ID
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		topic.ID = oid
	}

	return nil
}

// GetByID 根据ID获取Topic
func (r *topicRepository) GetByID(ctx context.Context, id string) (*model.Topic, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	var topic model.Topic
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&topic)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrTopicNotFound
		}
		return nil, fmt.Errorf("查询Topic失败: %w", err)
	}

	return &topic, nil
}

// GetByFullName 根据完整名称获取Topic
func (r *topicRepository) GetByFullName(ctx context.Context, fullName string) (*model.Topic, error) {
	var topic model.Topic
	err := r.collection.FindOne(ctx, bson.M{"full_name": fullName}).Decode(&topic)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, model.ErrTopicNotFound
		}
		return nil, fmt.Errorf("查询Topic失败: %w", err)
	}

	return &topic, nil
}

// Update 更新Topic
func (r *topicRepository) Update(ctx context.Context, topic *model.Topic) error {
	filter := bson.M{"_id": topic.ID}
	update := bson.M{"$set": topic}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return model.ErrTopicAlreadyExists
		}
		return fmt.Errorf("更新Topic失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return model.ErrTopicNotFound
	}

	return nil
}

// Delete 删除Topic
func (r *topicRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return model.ErrInvalidID
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除Topic失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrTopicNotFound
	}

	return nil
}

// DeleteByFullName 根据完整名称删除Topic
func (r *topicRepository) DeleteByFullName(ctx context.Context, fullName string) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"full_name": fullName})
	if err != nil {
		return fmt.Errorf("删除Topic失败: %w", err)
	}

	if result.DeletedCount == 0 {
		return model.ErrTopicNotFound
	}

	return nil
}

// ListByGroupID 根据分组ID获取Topic列表
func (r *topicRepository) ListByGroupID(ctx context.Context, groupID string, pagination Pagination) ([]*model.Topic, int64, error) {
	query := bson.M{"group_id": groupID}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, query)
	if err != nil {
		return nil, 0, fmt.Errorf("统计Topic数量失败: %w", err)
	}

	// 设置分页
	opts := options.Find()
	if pagination.PageSize > 0 {
		opts.SetLimit(int64(pagination.PageSize))
		if pagination.Page > 0 {
			opts.SetSkip(int64((pagination.Page - 1) * pagination.PageSize))
		}
	}
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}}) // 按创建时间倒序

	// 查询数据
	cursor, err := r.collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Topic列表失败: %w", err)
	}
	defer cursor.Close(ctx)

	var topics []*model.Topic
	if err = cursor.All(ctx, &topics); err != nil {
		return nil, 0, fmt.Errorf("解析Topic列表失败: %w", err)
	}

	return topics, total, nil
}

// CountByGroupID 统计分组Topic数量
func (r *topicRepository) CountByGroupID(ctx context.Context, groupID string) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"group_id": groupID})
	if err != nil {
		return 0, fmt.Errorf("统计分组Topic数量失败: %w", err)
	}
	return count, nil
}

// ExistsByGroupAndName 检查Topic名称在分组内是否存在
func (r *topicRepository) ExistsByGroupAndName(ctx context.Context, groupID, topicName string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{
		"group_id":   groupID,
		"topic_name": topicName,
	})
	if err != nil {
		return false, fmt.Errorf("检查Topic名称是否存在失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByFullName 检查完整Topic名称是否存在
func (r *topicRepository) ExistsByFullName(ctx context.Context, fullName string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"full_name": fullName})
	if err != nil {
		return false, fmt.Errorf("检查Topic完整名称是否存在失败: %w", err)
	}
	return count > 0, nil
}
