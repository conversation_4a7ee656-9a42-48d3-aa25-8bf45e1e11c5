package routes

import (
	"beacon/cloud/internal/config"
	"beacon/cloud/internal/handler"
	"beacon/cloud/internal/middleware"
	"net/http"

	"github.com/gin-gonic/gin"
)

// setupRoutes 设置路由
func SetupRoutes(
	engine *gin.Engine,
	authMiddleware *middleware.AuthMiddleware,
	authHandler *handler.AuthHandler,
	userHandler *handler.UserHandler,
	monitorHandler *handler.MonitorHandler,
	systemHandler *handler.SystemHandler,
	groupHandler *handler.GroupHandler,
	alertHandler *handler.AlertHandler,
	deviceHandler *handler.DeviceHandler,
	wireGuardHandler *handler.WireGuardHandler,
) {
	// 认证路由
	auth := engine.Group("/api/auth")
	{
		// 新的两步式注册流程
		auth.POST("/register/send-code", userHandler.RegisterSendCode)
		auth.POST("/register/verify", userHandler.RegisterVerify)

		auth.POST("/forgot-password", userHandler.ForgotPassword)
		auth.POST("/login", userHandler.Login)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/validate", authHandler.ValidateToken)
		auth.POST("/revoke", authHandler.RevokeToken)
		auth.GET("/status", authHandler.CheckTokenStatus)
	}

	// 受保护的用户路由
	userRoutes := engine.Group("/api/users")
	userRoutes.Use(authMiddleware.JWTAuth())
	userRoutes.Use(authMiddleware.CasbinAuth()) // Add Casbin authorization
	{
		userRoutes.GET("/profile", userHandler.GetProfile)
		userRoutes.PUT("/profile", userHandler.UpdateProfile)
		userRoutes.POST("/change-password", userHandler.ChangePassword)
		userRoutes.GET("/info", authHandler.GetUserInfo)
	}

	// 设置系统管理路由
	setupSystemRoutes(engine, systemHandler, authMiddleware)

	// 设置分组管理路由
	setupGroupRoutes(engine, groupHandler, authMiddleware)

	// 设置预警管理路由
	setupAlertRoutes(engine, alertHandler, authMiddleware)

	// 设置设备管理路由
	setupDeviceRoutes(engine, deviceHandler, authMiddleware)

	// 管理员路由
	admin := engine.Group("/api/admin")
	admin.Use(authMiddleware.JWTAuth())
	admin.Use(authMiddleware.CasbinAuth()) // Add Casbin authorization
	{
		// 用户管理
		admin.GET("/users", userHandler.ListUsers)
		admin.PUT("/users/:id", userHandler.AdminUpdateUser)
		admin.DELETE("/users/:id", userHandler.DeleteUser)
		admin.PUT("/users/:id/status", userHandler.UpdateUserStatus)
		// 设置监控路由
		// 监控面板路由
		{
			// 获取监控面板数据
			admin.GET("/dashboard", monitorHandler.GetDashboard)
		}
		// 性能监控路由
		performanceGroup := admin.Group("/performance")
		{
			// 获取性能历史数据
			performanceGroup.GET("/history", monitorHandler.GetPerformanceHistory)
		}
	}

	// 设置WireGuard路由
	setupWireGuardRoutes(engine, wireGuardHandler, authMiddleware)

	// 默认路由
	engine.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    config.ErrCodeNotFound,
			"message": "路由不存在",
			"path":    c.Request.URL.Path,
		})
	})
}

// setupSystemRoutes 设置系统管理路由
func setupSystemRoutes(
	engine *gin.Engine,
	systemHandler *handler.SystemHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// 系统管理路由组 - 需要认证和权限控制
	systemGroup := engine.Group("/api/system")
	systemGroup.Use(authMiddleware.JWTAuth())
	systemGroup.Use(authMiddleware.CasbinAuth()) // Add Casbin authorization

	// 系统日志管理
	systemGroup.GET("/logs", systemHandler.GetLogs)
	systemGroup.GET("/logs/statistics", systemHandler.GetLogStatistics)
	systemGroup.DELETE("/logs/cleanup", systemHandler.CleanupLogs)
}

// setupGroupRoutes 设置分组管理路由
func setupGroupRoutes(
	engine *gin.Engine,
	groupHandler *handler.GroupHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// 分组管理路由组 - 需要认证和权限控制
	groupGroup := engine.Group("/api/groups")
	groupGroup.Use(authMiddleware.JWTAuth())
	groupGroup.Use(authMiddleware.CasbinAuth()) // Add Casbin authorization

	// 分组基本管理
	groupGroup.POST("", groupHandler.CreateGroup)            // 创建分组
	groupGroup.GET("", groupHandler.ListGroups)              // 获取分组列表
	groupGroup.GET("/my", groupHandler.GetUserGroups)        // 获取用户分组列表
	groupGroup.GET("/:groupId", groupHandler.GetGroup)       // 获取分组信息
	groupGroup.PUT("/:groupId", groupHandler.UpdateGroup)    // 更新分组信息
	groupGroup.DELETE("/:groupId", groupHandler.DeleteGroup) // 删除分组

	// 分组成员管理
	groupGroup.POST("/join", groupHandler.JoinGroup)                              // 申请加入分组
	groupGroup.PUT("/requests/:requestId/review", groupHandler.ReviewJoinRequest) // 审核加入申请
	groupGroup.DELETE("/:groupId/members/:userId", groupHandler.RemoveMember)     // 移除成员
	groupGroup.DELETE("/:groupId/leave", groupHandler.LeaveGroup)                 // 退出分组
	groupGroup.GET("/:groupId/members", groupHandler.ListGroupMembers)            // 获取分组成员列表
	groupGroup.GET("/requests", groupHandler.ListJoinRequests)                    // 获取用户所属分组的加入申请列表

	// Topic管理
	groupGroup.POST("/:groupId/topics", groupHandler.CreateTopic)            // 创建Topic
	groupGroup.DELETE("/:groupId/topics/:topicId", groupHandler.DeleteTopic) // 删除Topic
	groupGroup.GET("/:groupId/topics", groupHandler.ListGroupTopics)         // 获取分组Topic列表

	// 权限管理
	groupGroup.POST("/:groupId/permissions", groupHandler.SetTopicPermission)              // 设置Topic权限
	groupGroup.DELETE("/:groupId/permissions/:userId", groupHandler.RemoveTopicPermission) // 移除Topic权限 (fullTopic通过查询参数传递)
	groupGroup.GET("/:groupId/permissions", groupHandler.ListTopicPermissions)             // 获取Topic权限列表 (fullTopic通过查询参数传递)

	// Topic数据管理
	groupGroup.GET("/topic-data", groupHandler.QueryTopicData)     // 查询Topic数据
	groupGroup.DELETE("/topic-data", groupHandler.DeleteTopicData) // 删除Topic数据
}

// setupAlertRoutes 设置预警管理路由
func setupAlertRoutes(
	engine *gin.Engine,
	alertHandler *handler.AlertHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// 预警管理路由组 - 需要认证和权限控制
	alertGroup := engine.Group("/api/groups")
	alertGroup.Use(authMiddleware.JWTAuth())
	alertGroup.Use(authMiddleware.CasbinAuth()) // Add Casbin authorization

	// 预警规则管理
	alertGroup.POST("/alert-rules", alertHandler.CreateAlertRule)            // 创建预警规则
	alertGroup.GET("/alert-rules", alertHandler.ListAlertRules)              // 获取预警规则列表
	alertGroup.GET("/alert-rules/:ruleId", alertHandler.GetAlertRule)        // 获取预警规则详情
	alertGroup.PUT("/alert-rules/:ruleId", alertHandler.UpdateAlertRule)     // 更新预警规则
	alertGroup.DELETE("/alert-rules/:ruleId", alertHandler.DeleteAlertRule)  // 删除预警规则
	alertGroup.POST("/alert-rules/:ruleId/test", alertHandler.TestAlertRule) // 测试预警规则

	// 预警记录管理
	alertGroup.GET("/alert-records", alertHandler.GetAlertRecords) // 获取预警记录列表
}

// setupDeviceRoutes 设置设备管理路由
func setupDeviceRoutes(
	engine *gin.Engine,
	deviceHandler *handler.DeviceHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// 管理员设备管理路由组 - 需要认证和管理员权限
	adminDeviceGroup := engine.Group("/api/admin/devices")
	adminDeviceGroup.Use(authMiddleware.JWTAuth())
	adminDeviceGroup.Use(authMiddleware.CasbinAuth()) // 管理员权限控制
	{
		// 管理员设备管理
		adminDeviceGroup.POST("", deviceHandler.AdminCreateDevice)       // 创建设备
		adminDeviceGroup.GET("", deviceHandler.AdminListDevices)         // 获取所有设备列表
		adminDeviceGroup.GET("/:id", deviceHandler.AdminGetDevice)       // 获取设备详情
		adminDeviceGroup.PUT("/:id", deviceHandler.AdminUpdateDevice)    // 更新设备
		adminDeviceGroup.DELETE("/:id", deviceHandler.AdminDeleteDevice) // 删除设备
	}

	// 用户设备管理路由组 - 需要认证
	userDeviceGroup := engine.Group("/api/user/devices")
	userDeviceGroup.Use(authMiddleware.JWTAuth())
	{
		// 用户设备管理
		userDeviceGroup.POST("", deviceHandler.UserAssignDevice)         // 用户添加设备
		userDeviceGroup.GET("", deviceHandler.UserListDevices)           // 获取用户设备列表
		userDeviceGroup.GET("/:id", deviceHandler.UserGetDevice)         // 获取用户设备详情
		userDeviceGroup.DELETE("/:id", deviceHandler.UserUnassignDevice) // 用户移除设备
	}

	// 设备类型管理路由组 - 需要认证
	deviceTypeGroup := engine.Group("/api/device-types")
	deviceTypeGroup.Use(authMiddleware.JWTAuth())
	{
		// 设备类型管理
		deviceTypeGroup.POST("", deviceHandler.CreateDeviceType)       // 创建设备类型（管理员）
		deviceTypeGroup.GET("", deviceHandler.ListDeviceTypes)         // 获取设备类型列表
		deviceTypeGroup.PUT("/:id", deviceHandler.UpdateDeviceType)    // 更新设备类型（管理员）
		deviceTypeGroup.DELETE("/:id", deviceHandler.DeleteDeviceType) // 删除设备类型（管理员）
	}
}

// setupWireGuardRoutes 设置WireGuard路由
func setupWireGuardRoutes(
	engine *gin.Engine,
	wireGuardHandler *handler.WireGuardHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// WireGuard API路由组 - 需要认证
	wgGroup := engine.Group("/api/v1/wireguard")
	wgGroup.Use(authMiddleware.JWTAuth())
	wgGroup.Use(authMiddleware.CasbinAuth()) // 权限控制

	// 服务器管理路由
	serverGroup := wgGroup.Group("/servers")
	{
		serverGroup.POST("", wireGuardHandler.CreateServer)
		serverGroup.GET("", wireGuardHandler.ListServers)
		serverGroup.GET("/:id", wireGuardHandler.GetServer)
		serverGroup.PUT("/:id", wireGuardHandler.UpdateServer)
		serverGroup.DELETE("/:id", wireGuardHandler.DeleteServer)
	}

	// 客户端管理路由
	clientGroup := wgGroup.Group("/clients")
	{
		clientGroup.POST("", wireGuardHandler.CreateClient)
		clientGroup.GET("", wireGuardHandler.ListClients)
		clientGroup.GET("/:id", wireGuardHandler.GetClient)
		clientGroup.PUT("/:id", wireGuardHandler.UpdateClient)
		clientGroup.DELETE("/:id", wireGuardHandler.DeleteClient)
		clientGroup.POST("/:id/enable", wireGuardHandler.EnableClient)
		clientGroup.POST("/:id/disable", wireGuardHandler.DisableClient)
	}

	// 配置管理路由
	configGroup := wgGroup.Group("/configs")
	{
		configGroup.GET("/clients/:id", wireGuardHandler.GetClientConfig)
		configGroup.GET("/clients/:id/qr", wireGuardHandler.GetClientQRCode)
	}

	// 统计信息路由
	statsGroup := wgGroup.Group("/stats")
	{
		statsGroup.GET("/clients/:id", wireGuardHandler.GetClientStats)
	}
}
