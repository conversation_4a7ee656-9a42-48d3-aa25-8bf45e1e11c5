package routes

import (
	"beacon/cloud/internal/handler"
	"beacon/cloud/pkg/wireguard"

	"github.com/gin-gonic/gin"
)

// SetupWireGuardRoutes 设置 WireGuard 路由
func SetupWireGuardRoutes(r *gin.Engine, wgManager wireguard.WireGuardManager) {
	wgHandler := handler.NewWireGuardHandler(wgManager)

	wg := r.Group("/api/v1/wireguard")
	// 这里应该使用现有的认证中间件
	// wg.Use(AuthMiddleware())

	// 服务器管理
	servers := wg.Group("/servers")
	{
		servers.POST("", wgHandler.CreateServer)
		servers.GET("", wgHandler.ListServers)
		servers.GET("/:id", wgHandler.GetServer)
		servers.PUT("/:id", wgHandler.UpdateServer)
		servers.DELETE("/:id", wgHandler.DeleteServer)
		servers.POST("/:id/restart", wgHandler.RestartServer)
	}

	// 客户端管理
	clients := wg.Group("/clients")
	{
		clients.POST("", wgHandler.CreateClient)
		clients.GET("", wgHandler.ListClients) // 支持 ?server_id= 查询参数
		clients.GET("/:id", wgHandler.GetClient)
		clients.PUT("/:id", wgHandler.UpdateClient)
		clients.DELETE("/:id", wgHandler.DeleteClient)
		clients.POST("/:id/enable", wgHandler.EnableClient)
		clients.POST("/:id/disable", wgHandler.DisableClient)
	}

	// 配置和下载
	configs := wg.Group("/configs")
	{
		configs.GET("/clients/:id", wgHandler.GetClientConfig)
		configs.GET("/clients/:id/qrcode", wgHandler.GetClientQRCode)
	}

	// 统计信息
	stats := wg.Group("/stats")
	{
		stats.GET("/clients/:id", wgHandler.GetClientStats)
	}
}

// WireGuardMiddleware WireGuard 中间件
func WireGuardMiddleware(wgManager wireguard.WireGuardManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("wgManager", wgManager)
		c.Next()
	}
}
