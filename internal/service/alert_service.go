package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/pkg/database"

	"github.com/redis/go-redis/v9"
)

// AlertService 预警服务接口
type AlertService interface {
	// 预警规则管理
	CreateAlertRule(ctx context.Context, userID string, req *model.CreateAlertRuleRequest) (*model.AlertRule, error)
	GetAlertRule(ctx context.Context, id string) (*model.AlertRuleResponse, error)
	UpdateAlertRule(ctx context.Context, userID, id string, req *model.UpdateAlertRuleRequest) (*model.AlertRule, error)
	DeleteAlertRule(ctx context.Context, userID, id string) error
	ListAlertRules(ctx context.Context, filter repository.AlertRuleFilter, pagination repository.Pagination) ([]*model.AlertRuleResponse, int64, error)

	// 预警记录管理
	GetAlertRecords(ctx context.Context, query model.AlertRecordQuery) ([]model.AlertRecord, int64, error)

	// 预警引擎
	ProcessMQTTMessage(ctx context.Context, topic, payload, groupID, clientID string) error
	TestAlertRule(ctx context.Context, ruleID string, testData map[string]interface{}) (bool, string, error)

	// 缓存管理
	RefreshRuleCache(ctx context.Context, groupID, topic string) error
	ClearRuleCache(ctx context.Context, groupID, topic string) error
}

// alertService 预警服务实现
type alertService struct {
	alertRepo       repository.AlertRuleRepository
	tdengineManager *database.TDengineManager
	redisClient     *redis.Client
	groupService    GroupService
	emailService    EmailService
	config          *config.Config
}

// NewAlertService 创建预警服务
func NewAlertService(
	alertRepo repository.AlertRuleRepository,
	tdengineManager *database.TDengineManager,
	redisClient *redis.Client,
	groupService GroupService,
	emailService EmailService,
	config *config.Config,
) AlertService {
	return &alertService{
		alertRepo:       alertRepo,
		tdengineManager: tdengineManager,
		redisClient:     redisClient,
		groupService:    groupService,
		emailService:    emailService,
		config:          config,
	}
}

// CreateAlertRule 创建预警规则
func (s *alertService) CreateAlertRule(ctx context.Context, userID string, req *model.CreateAlertRuleRequest) (*model.AlertRule, error) {
	// 验证用户是否有权限操作该分组
	isMember, err := s.groupService.IsGroupMember(ctx, userID, req.GroupID)
	if err != nil {
		return nil, fmt.Errorf("检查分组权限失败: %v", err)
	}
	if !isMember {
		return nil, fmt.Errorf("用户不是该分组的成员")
	}

	// 验证预警条件
	if err := s.validateAlertConditions(req.Conditions); err != nil {
		return nil, err
	}

	// 创建预警规则
	rule := &model.AlertRule{
		GroupID:      req.GroupID,
		Topic:        req.Topic,
		RuleName:     req.RuleName,
		RuleType:     req.RuleType,
		Description:  req.Description,
		Conditions:   req.Conditions,
		Level:        req.Level,
		Notification: req.Notification,
		Enabled:      req.Enabled,
		CreatedBy:    userID,
	}

	// 频率限制现在由全局配置管理，不再需要在这里设置

	err = s.alertRepo.Create(ctx, rule)
	if err != nil {
		return nil, err
	}

	// 刷新缓存
	s.RefreshRuleCache(ctx, req.GroupID, req.Topic)

	return rule, nil
}

// GetAlertRule 获取预警规则
func (s *alertService) GetAlertRule(ctx context.Context, id string) (*model.AlertRuleResponse, error) {
	rule, err := s.alertRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := rule.ToResponse()
	return &response, nil
}

// UpdateAlertRule 更新预警规则
func (s *alertService) UpdateAlertRule(ctx context.Context, userID, id string, req *model.UpdateAlertRuleRequest) (*model.AlertRule, error) {
	// 获取现有规则
	rule, err := s.alertRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 验证用户权限
	isMember, err := s.groupService.IsGroupMember(ctx, userID, rule.GroupID)
	if err != nil {
		return nil, fmt.Errorf("检查分组权限失败: %v", err)
	}
	if !isMember {
		return nil, fmt.Errorf("用户不是该分组的成员")
	}

	// 更新字段
	if req.RuleName != "" {
		rule.RuleName = req.RuleName
	}
	if req.RuleType != "" {
		rule.RuleType = req.RuleType
	}
	if req.Description != "" {
		rule.Description = req.Description
	}
	if len(req.Conditions) > 0 {
		if err := s.validateAlertConditions(req.Conditions); err != nil {
			return nil, err
		}
		rule.Conditions = req.Conditions
	}
	if req.Level > 0 {
		rule.Level = req.Level
	}
	if len(req.Notification.Channels) > 0 {
		rule.Notification = req.Notification
	}
	if req.Enabled != nil {
		rule.Enabled = *req.Enabled
	}

	err = s.alertRepo.Update(ctx, rule)
	if err != nil {
		return nil, err
	}

	// 刷新缓存
	s.RefreshRuleCache(ctx, rule.GroupID, rule.Topic)

	return rule, nil
}

// DeleteAlertRule 删除预警规则
func (s *alertService) DeleteAlertRule(ctx context.Context, userID, id string) error {
	// 获取现有规则
	rule, err := s.alertRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 验证用户权限
	isMember, err := s.groupService.IsGroupMember(ctx, userID, rule.GroupID)
	if err != nil {
		return fmt.Errorf("检查分组权限失败: %v", err)
	}
	if !isMember {
		return fmt.Errorf("用户不是该分组的成员")
	}

	err = s.alertRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	// 清除缓存
	s.ClearRuleCache(ctx, rule.GroupID, rule.Topic)

	return nil
}

// ListAlertRules 获取预警规则列表
func (s *alertService) ListAlertRules(ctx context.Context, filter repository.AlertRuleFilter, pagination repository.Pagination) ([]*model.AlertRuleResponse, int64, error) {
	rules, total, err := s.alertRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]*model.AlertRuleResponse, 0, len(rules))
	for _, rule := range rules {
		response := rule.ToResponse()
		responses = append(responses, &response)
	}

	return responses, total, nil
}

// GetAlertRecords 获取预警记录
func (s *alertService) GetAlertRecords(ctx context.Context, query model.AlertRecordQuery) ([]model.AlertRecord, int64, error) {
	// 转换查询参数
	tdQuery := database.AlertRecordQuery{
		GroupID:   query.GroupID,
		TopicName: query.Topic,
		RuleID:    query.RuleID,
		Level:     int(query.Level),
		TimeRange: query.TimeRange,
		Page:      query.Page,
		PageSize:  query.PageSize,
	}

	records, total, err := s.tdengineManager.QueryAlertRecords(ctx, tdQuery)
	if err != nil {
		return nil, 0, err
	}

	// 转换为模型格式
	result := make([]model.AlertRecord, 0, len(records))
	for _, record := range records {
		result = append(result, model.AlertRecord{
			Timestamp:    record.Timestamp,
			RuleID:       record.RuleID,
			TopicName:    record.TopicName,
			TriggerValue: record.TriggerValue,
			Message:      record.Message,
			Level:        model.AlertLevel(record.Level),
			GroupID:      record.GroupID,
			RuleType:     record.RuleType,
		})
	}

	return result, total, nil
}

// validateAlertConditions 验证预警条件
func (s *alertService) validateAlertConditions(conditions []model.AlertCondition) error {
	if len(conditions) == 0 {
		return model.ErrInvalidAlertCondition
	}

	for _, condition := range conditions {
		if condition.Field == "" {
			return fmt.Errorf("预警条件字段不能为空")
		}
		if condition.Operator == "" {
			return fmt.Errorf("预警条件操作符不能为空")
		}
		if condition.Value == "" {
			return fmt.Errorf("预警条件值不能为空")
		}

		// 验证操作符和数据类型的匹配
		if err := s.validateOperatorDataType(condition.Operator, condition.DataType); err != nil {
			return err
		}
	}

	return nil
}

// validateOperatorDataType 验证操作符和数据类型的匹配
func (s *alertService) validateOperatorDataType(operator model.Operator, dataType model.DataType) error {
	switch dataType {
	case model.DataTypeNumber:
		validOps := []model.Operator{
			model.OperatorGT, model.OperatorLT, model.OperatorGTE,
			model.OperatorLTE, model.OperatorEQ, model.OperatorNE,
		}
		for _, op := range validOps {
			if operator == op {
				return nil
			}
		}
		return fmt.Errorf("数值类型不支持操作符: %s", operator)

	case model.DataTypeString:
		validOps := []model.Operator{
			model.OperatorEquals, model.OperatorContains,
			model.OperatorStartsWith, model.OperatorEndsWith,
		}
		for _, op := range validOps {
			if operator == op {
				return nil
			}
		}
		return fmt.Errorf("字符串类型不支持操作符: %s", operator)

	case model.DataTypeBool:
		if operator == model.OperatorEQ || operator == model.OperatorNE {
			return nil
		}
		return fmt.Errorf("布尔类型只支持等于和不等于操作符")

	default:
		return fmt.Errorf("不支持的数据类型: %s", dataType)
	}
}

// ProcessMQTTMessage 处理MQTT消息进行预警检测
func (s *alertService) ProcessMQTTMessage(ctx context.Context, topic, payload, groupID, clientID string) error {

	// 从缓存获取预警规则
	rules, err := s.getRulesFromCache(ctx, groupID, topic)
	if err != nil {
		// 如果缓存获取失败，从数据库获取
		rules, err = s.alertRepo.GetByGroupAndTopic(ctx, groupID, topic)
		if err != nil {
			return fmt.Errorf("获取预警规则失败: %v", err)
		}
		// 更新缓存
		s.cacheRules(ctx, groupID, topic, rules)
	}

	if len(rules) == 0 {
		return nil // 没有预警规则
	}

	// 解析消息数据
	var messageData map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &messageData); err != nil {
		// 如果不是JSON格式，将整个payload作为字符串处理
		messageData = map[string]interface{}{
			"payload": payload,
		}
	}

	// 逐一检查预警规则
	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		// 检查频率限制
		if s.isFrequencyLimited(ctx, rule.ID.Hex()) {
			continue
		}

		// 检查预警条件
		triggered, triggerValue := s.checkAlertConditions(rule.Conditions, messageData)
		if triggered {
			// 触发预警
			if err := s.triggerAlert(ctx, rule, triggerValue, topic, payload); err != nil {
				// 记录错误但不中断处理
				fmt.Printf("触发预警失败: %v\n", err)
			}
		}
	}

	return nil
}

// checkAlertConditions 检查预警条件
func (s *alertService) checkAlertConditions(conditions []model.AlertCondition, data map[string]interface{}) (bool, string) {
	for _, condition := range conditions {
		value, exists := data[condition.Field]
		if !exists {
			return false, ""
		}

		triggered := s.evaluateCondition(condition, value)
		if triggered {
			return true, fmt.Sprintf("%v", value)
		}
	}
	return false, ""
}

// evaluateCondition 评估单个条件
func (s *alertService) evaluateCondition(condition model.AlertCondition, value interface{}) bool {
	switch condition.DataType {
	case model.DataTypeNumber:
		return s.evaluateNumberCondition(condition, value)
	case model.DataTypeString:
		return s.evaluateStringCondition(condition, value)
	case model.DataTypeBool:
		return s.evaluateBoolCondition(condition, value)
	default:
		return false
	}
}

// evaluateNumberCondition 评估数值条件
func (s *alertService) evaluateNumberCondition(condition model.AlertCondition, value interface{}) bool {
	var numValue float64
	switch v := value.(type) {
	case float64:
		numValue = v
	case float32:
		numValue = float64(v)
	case int:
		numValue = float64(v)
	case int64:
		numValue = float64(v)
	case string:
		var err error
		numValue, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return false
		}
	default:
		return false
	}

	thresholdValue, err := strconv.ParseFloat(condition.Value, 64)
	if err != nil {
		return false
	}

	switch condition.Operator {
	case model.OperatorGT:
		return numValue > thresholdValue
	case model.OperatorLT:
		return numValue < thresholdValue
	case model.OperatorGTE:
		return numValue >= thresholdValue
	case model.OperatorLTE:
		return numValue <= thresholdValue
	case model.OperatorEQ:
		return numValue == thresholdValue
	case model.OperatorNE:
		return numValue != thresholdValue
	default:
		return false
	}
}

// evaluateStringCondition 评估字符串条件
func (s *alertService) evaluateStringCondition(condition model.AlertCondition, value interface{}) bool {
	strValue := fmt.Sprintf("%v", value)
	thresholdValue := condition.Value

	switch condition.Operator {
	case model.OperatorEquals:
		return strValue == thresholdValue
	case model.OperatorContains:
		return strings.Contains(strValue, thresholdValue)
	case model.OperatorStartsWith:
		return strings.HasPrefix(strValue, thresholdValue)
	case model.OperatorEndsWith:
		return strings.HasSuffix(strValue, thresholdValue)
	default:
		return false
	}
}

// evaluateBoolCondition 评估布尔条件
func (s *alertService) evaluateBoolCondition(condition model.AlertCondition, value interface{}) bool {
	var boolValue bool
	switch v := value.(type) {
	case bool:
		boolValue = v
	case string:
		var err error
		boolValue, err = strconv.ParseBool(v)
		if err != nil {
			return false
		}
	default:
		return false
	}

	thresholdValue, err := strconv.ParseBool(condition.Value)
	if err != nil {
		return false
	}

	switch condition.Operator {
	case model.OperatorEQ:
		return boolValue == thresholdValue
	case model.OperatorNE:
		return boolValue != thresholdValue
	default:
		return false
	}
}

// triggerAlert 触发预警
func (s *alertService) triggerAlert(ctx context.Context, rule *model.AlertRule, triggerValue, topic, payload string) error {
	// 更新频率计数
	s.incrementFrequencyCount(ctx, rule.ID.Hex())

	// 生成预警消息
	message := fmt.Sprintf("预警规则 [%s] 被触发，Topic: %s, 触发值: %s", rule.RuleName, topic, triggerValue)

	// 创建预警记录
	record := database.AlertRecordData{
		Timestamp:    time.Now(),
		RuleID:       rule.ID.Hex(),
		TopicName:    topic,
		TriggerValue: triggerValue,
		Message:      message,
		Level:        int(rule.Level),
		GroupID:      rule.GroupID,
		RuleType:     string(rule.RuleType),
	}

	// 存储预警记录到TDengine
	if err := s.tdengineManager.InsertAlertRecord(ctx, record); err != nil {
		return fmt.Errorf("存储预警记录失败: %v", err)
	}

	// 发送通知（如果启用）
	if rule.Notification.Enabled {
		if err := s.sendNotifications(ctx, rule, message, triggerValue, topic); err != nil {
			// 通知发送失败不影响预警记录的存储，只记录错误日志
			log.Printf("发送预警通知失败: %v", err)
		}
	}

	return nil
}

// isFrequencyLimited 检查频率限制
func (s *alertService) isFrequencyLimited(ctx context.Context, ruleID string) bool {
	// 使用全局配置的频率限制
	limit := s.config.Alert.FrequencyLimit
	key := fmt.Sprintf("alert:count:%s:%d", ruleID, limit.TimeWindow)

	count, err := s.redisClient.Get(ctx, key).Int()
	if err != nil {
		// 如果key不存在，说明还没有触发过
		return false
	}

	return count >= limit.MaxCount
}

// incrementFrequencyCount 增加频率计数
func (s *alertService) incrementFrequencyCount(ctx context.Context, ruleID string) {
	// 使用全局配置的频率限制
	limit := s.config.Alert.FrequencyLimit
	key := fmt.Sprintf("alert:count:%s:%d", ruleID, limit.TimeWindow)

	// 增加计数
	s.redisClient.Incr(ctx, key)

	// 设置过期时间
	s.redisClient.Expire(ctx, key, time.Duration(limit.TimeWindow)*time.Second)
}

// getRulesFromCache 从缓存获取预警规则
func (s *alertService) getRulesFromCache(ctx context.Context, groupID, topic string) ([]*model.AlertRule, error) {
	key := fmt.Sprintf("alert:rules:%s:%s", groupID, topic)

	data, err := s.redisClient.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var rules []*model.AlertRule
	if err := json.Unmarshal([]byte(data), &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// cacheRules 缓存预警规则
func (s *alertService) cacheRules(ctx context.Context, groupID, topic string, rules []*model.AlertRule) {
	key := fmt.Sprintf("alert:rules:%s:%s", groupID, topic)

	data, err := json.Marshal(rules)
	if err != nil {
		return
	}

	// 缓存1小时
	s.redisClient.Set(ctx, key, data, time.Hour)
}

// RefreshRuleCache 刷新规则缓存
func (s *alertService) RefreshRuleCache(ctx context.Context, groupID, topic string) error {
	// 从数据库获取最新规则
	rules, err := s.alertRepo.GetByGroupAndTopic(ctx, groupID, topic)
	if err != nil {
		return err
	}

	// 更新缓存
	s.cacheRules(ctx, groupID, topic, rules)
	return nil
}

// ClearRuleCache 清除规则缓存
func (s *alertService) ClearRuleCache(ctx context.Context, groupID, topic string) error {
	key := fmt.Sprintf("alert:rules:%s:%s", groupID, topic)
	return s.redisClient.Del(ctx, key).Err()
}

// TestAlertRule 测试预警规则
func (s *alertService) TestAlertRule(ctx context.Context, ruleID string, testData map[string]interface{}) (bool, string, error) {
	// 获取预警规则
	rule, err := s.alertRepo.GetByID(ctx, ruleID)
	if err != nil {
		return false, "", err
	}

	// 检查预警条件
	triggered, triggerValue := s.checkAlertConditions(rule.Conditions, testData)

	var message string
	if triggered {
		message = fmt.Sprintf("预警规则测试通过，触发值: %s", triggerValue)
	} else {
		message = "预警规则测试未触发"
	}

	return triggered, message, nil
}

// sendNotifications 发送预警通知
func (s *alertService) sendNotifications(ctx context.Context, rule *model.AlertRule, message, triggerValue, topic string) error {
	if len(rule.Notification.Channels) == 0 || len(rule.Notification.Recipients) == 0 {
		return nil // 没有配置通知渠道或接收人
	}

	// 构建通知内容
	notificationData := s.buildNotificationContent(rule, message, triggerValue, topic)

	var errors []string

	// 遍历所有通知渠道
	for _, channel := range rule.Notification.Channels {
		switch channel {
		case "email":
			if err := s.sendEmailNotification(ctx, rule.Notification.Recipients, notificationData); err != nil {
				errors = append(errors, fmt.Sprintf("邮件通知失败: %v", err))
			}
		case "sms":
			if err := s.sendSMSNotification(ctx, rule.Notification.Recipients, notificationData); err != nil {
				errors = append(errors, fmt.Sprintf("短信通知失败: %v", err))
			}
		case "wechat":
			if err := s.sendWeChatNotification(ctx, rule.Notification.Recipients, notificationData); err != nil {
				errors = append(errors, fmt.Sprintf("微信通知失败: %v", err))
			}
		default:
			errors = append(errors, fmt.Sprintf("不支持的通知渠道: %s", channel))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分通知发送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// NotificationData 通知数据结构
type NotificationData struct {
	RuleName     string `json:"rule_name"`
	Topic        string `json:"topic"`
	TriggerValue string `json:"trigger_value"`
	Message      string `json:"message"`
	Level        string `json:"level"`
	Timestamp    string `json:"timestamp"`
	GroupID      string `json:"group_id"`
}

// buildNotificationContent 构建通知内容
func (s *alertService) buildNotificationContent(rule *model.AlertRule, message, triggerValue, topic string) *NotificationData {
	levelMap := map[model.AlertLevel]string{
		model.AlertLevelInfo:     "信息",
		model.AlertLevelWarning:  "警告",
		model.AlertLevelError:    "错误",
		model.AlertLevelCritical: "严重",
		model.AlertLevelFatal:    "灾难",
	}

	level := levelMap[rule.Level]
	if level == "" {
		level = "未知"
	}

	return &NotificationData{
		RuleName:     rule.RuleName,
		Topic:        topic,
		TriggerValue: triggerValue,
		Message:      message,
		Level:        level,
		Timestamp:    time.Now().Format("2006-01-02 15:04:05"),
		GroupID:      rule.GroupID,
	}
}

// sendEmailNotification 发送邮件通知
func (s *alertService) sendEmailNotification(ctx context.Context, recipients []string, data *NotificationData) error {
	if s.emailService == nil {
		return fmt.Errorf("邮件服务未配置")
	}

	// 构建邮件主题
	subject := fmt.Sprintf("【%s】预警通知 - %s", data.Level, data.RuleName)

	// 构建邮件内容
	body := s.buildEmailBody(data)

	// 发送邮件给所有接收人
	var errors []string
	for _, recipient := range recipients {
		if err := s.emailService.SendEmail(ctx, recipient, subject, body); err != nil {
			errors = append(errors, fmt.Sprintf("发送给 %s 失败: %v", recipient, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("邮件发送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// buildEmailBody 构建邮件内容
func (s *alertService) buildEmailBody(data *NotificationData) string {
	// 根据预警级别设置颜色
	levelColor := "#007BFF" // 默认蓝色
	switch data.Level {
	case "信息":
		levelColor = "#17a2b8" // 青色
	case "警告":
		levelColor = "#ffc107" // 黄色
	case "错误":
		levelColor = "#fd7e14" // 橙色
	case "严重":
		levelColor = "#dc3545" // 红色
	case "灾难":
		levelColor = "#6f42c1" // 紫色
	}

	return fmt.Sprintf(`
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%%">
                <tr>
                  <td align="center">
                    <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png"
                         alt="BeaconGlobalTechnology"
                         style="max-height: 50px; vertical-align: middle;">
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td style="padding: 40px; text-align: center; word-wrap: break-word; word-break: break-word;">
              <h2 style="color: %s; margin-bottom: 20px;">🚨 预警通知</h2>

              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                <p><strong>规则名称:</strong> %s</p>
                <p><strong>预警级别:</strong> <span style="color: %s; font-weight: bold;">%s</span></p>
                <p><strong>Topic:</strong> %s</p>
                <p><strong>触发值:</strong> %s</p>
                <p><strong>触发时间:</strong> %s</p>
                <p><strong>分组ID:</strong> %s</p>
              </div>

              <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <p style="margin: 0; color: #856404;"><strong>预警消息:</strong></p>
                <p style="margin: 10px 0 0 0; color: #856404;">%s</p>
              </div>

              <p style="font-size: 14px; color: #666666; margin-top: 20px;">
                请及时处理此预警，确保系统正常运行。
              </p>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>此邮件由 Beacon Cloud 预警系统自动发送</p>
              <p>如有任何问题，请联系
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>`,
		levelColor, data.RuleName, levelColor, data.Level, data.Topic,
		data.TriggerValue, data.Timestamp, data.GroupID, data.Message)
}

// sendSMSNotification 发送短信通知（占位实现）
func (s *alertService) sendSMSNotification(ctx context.Context, recipients []string, data *NotificationData) error {
	// TODO: 实现短信通知功能
	// 这里可以集成阿里云短信服务、腾讯云短信服务等

	log.Printf("【短信通知】规则: %s, 级别: %s, Topic: %s, 触发值: %s, 接收人: %v",
		data.RuleName, data.Level, data.Topic, data.TriggerValue, recipients)

	// 模拟短信发送成功
	for _, recipient := range recipients {
		log.Printf("短信通知发送成功: %s -> %s", data.RuleName, recipient)
	}

	return nil
}

// sendWeChatNotification 发送微信通知（占位实现）
func (s *alertService) sendWeChatNotification(ctx context.Context, recipients []string, data *NotificationData) error {
	// TODO: 实现微信通知功能
	// 这里可以集成企业微信机器人、微信公众号模板消息等

	log.Printf("【微信通知】规则: %s, 级别: %s, Topic: %s, 触发值: %s, 接收人: %v",
		data.RuleName, data.Level, data.Topic, data.TriggerValue, recipients)

	// 模拟微信发送成功
	for _, recipient := range recipients {
		log.Printf("微信通知发送成功: %s -> %s", data.RuleName, recipient)
	}

	return nil
}
