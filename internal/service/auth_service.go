package service

import (
	"beacon/cloud/internal/config"
	"beacon/cloud/pkg/auth"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// AuthService 认证服务
type AuthService struct {
	jwtManager  *auth.JWTManager
	redisClient *redis.Client
	config      *config.Config
}

// NewAuthService 创建认证服务
func NewAuthService(cfg *config.Config, redisClient *redis.Client) *AuthService {
	jwtManager := auth.NewJWTManager(
		cfg.JWTSecret,
		config.DefaultJWTExpiration,
		config.DefaultRefreshExpiration,
	)

	return &AuthService{
		jwtManager:  jwtManager,
		redisClient: redisClient,
		config:      cfg,
	}
}

// GenerateTokens 生成令牌对
func (s *AuthService) GenerateTokens(userID, username, role string) (*auth.TokenPair, error) {
	return s.jwtManager.GenerateTokenPair(userID, username, role)
}

// VerifyAccessToken 验证访问令牌
func (s *AuthService) VerifyAccessToken(tokenString string) (*auth.Claims, error) {
	claims, err := s.jwtManager.VerifyToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != "access" {
		return nil, errors.New("无效的令牌类型")
	}

	return claims, nil
}

// RefreshTokens 刷新令牌
func (s *AuthService) RefreshTokens(refreshTokenString string) (*auth.TokenPair, error) {
	// 验证刷新令牌
	claims, err := s.jwtManager.VerifyToken(refreshTokenString)
	if err != nil {
		return nil, fmt.Errorf("刷新令牌验证失败: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, errors.New("提供的不是刷新令牌")
	}

	// 检查刷新令牌是否在黑名单中
	if s.redisClient != nil {
		isBlacklisted, err := s.IsTokenBlacklisted(refreshTokenString)
		if err != nil {
			return nil, fmt.Errorf("检查令牌黑名单失败: %w", err)
		}
		if isBlacklisted {
			return nil, errors.New("刷新令牌已被撤销")
		}
	}

	// 生成新的令牌对
	newTokenPair, err := s.jwtManager.GenerateTokenPair(claims.UserID, claims.Username, claims.Role)
	if err != nil {
		return nil, fmt.Errorf("生成新令牌失败: %w", err)
	}

	// 将旧的刷新令牌加入黑名单
	if s.redisClient != nil {
		err = s.BlacklistToken(refreshTokenString, claims.ExpiresAt.Time)
		if err != nil {
			// 记录错误但不阻止令牌刷新
			fmt.Printf("将旧刷新令牌加入黑名单失败: %v\n", err)
		}
	}

	return newTokenPair, nil
}

// BlacklistToken 将令牌加入黑名单
func (s *AuthService) BlacklistToken(tokenString string, expireTime time.Time) error {
	if s.redisClient == nil {
		return errors.New("Redis客户端未配置")
	}

	ctx := context.Background()
	key := fmt.Sprintf("blacklist:token:%s", tokenString)
	
	// 计算过期时间
	ttl := time.Until(expireTime)
	if ttl <= 0 {
		// 令牌已过期，无需加入黑名单
		return nil
	}

	return s.redisClient.Set(ctx, key, "1", ttl).Err()
}

// IsTokenBlacklisted 检查令牌是否在黑名单中
func (s *AuthService) IsTokenBlacklisted(tokenString string) (bool, error) {
	if s.redisClient == nil {
		return false, nil
	}

	ctx := context.Background()
	key := fmt.Sprintf("blacklist:token:%s", tokenString)
	
	result, err := s.redisClient.Get(ctx, key).Result()
	if err == redis.Nil {
		return false, nil
	}
	if err != nil {
		return false, err
	}

	return result == "1", nil
}

// RevokeToken 撤销令牌（加入黑名单）
func (s *AuthService) RevokeToken(tokenString string) error {
	// 获取令牌声明（不验证过期时间）
	claims, err := s.jwtManager.GetTokenClaims(tokenString)
	if err != nil {
		return fmt.Errorf("获取令牌声明失败: %w", err)
	}

	return s.BlacklistToken(tokenString, claims.ExpiresAt.Time)
}

// RevokeAllUserTokens 撤销用户的所有令牌
func (s *AuthService) RevokeAllUserTokens(userID string) error {
	if s.redisClient == nil {
		return errors.New("Redis客户端未配置")
	}

	ctx := context.Background()
	key := fmt.Sprintf("user:revoked:%s", userID)
	
	// 设置用户令牌撤销时间戳
	now := time.Now().Unix()
	return s.redisClient.Set(ctx, key, now, config.DefaultRefreshExpiration).Err()
}

// IsUserTokenRevoked 检查用户令牌是否被撤销
func (s *AuthService) IsUserTokenRevoked(userID string, tokenIssuedAt time.Time) (bool, error) {
	if s.redisClient == nil {
		return false, nil
	}

	ctx := context.Background()
	key := fmt.Sprintf("user:revoked:%s", userID)
	
	result, err := s.redisClient.Get(ctx, key).Result()
	if err == redis.Nil {
		return false, nil
	}
	if err != nil {
		return false, err
	}

	// 解析撤销时间戳
	var revokedAt int64
	if _, err := fmt.Sscanf(result, "%d", &revokedAt); err != nil {
		return false, err
	}

	// 如果令牌签发时间早于撤销时间，则认为已被撤销
	return tokenIssuedAt.Unix() < revokedAt, nil
}

// ValidateTokenWithBlacklist 验证令牌并检查黑名单
func (s *AuthService) ValidateTokenWithBlacklist(tokenString string) (*auth.Claims, error) {
	// 验证令牌
	claims, err := s.VerifyAccessToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否在黑名单中
	if s.redisClient != nil {
		isBlacklisted, err := s.IsTokenBlacklisted(tokenString)
		if err != nil {
			return nil, fmt.Errorf("检查令牌黑名单失败: %w", err)
		}
		if isBlacklisted {
			return nil, errors.New("令牌已被撤销")
		}

		// 检查用户令牌是否被全局撤销
		isRevoked, err := s.IsUserTokenRevoked(claims.UserID, claims.IssuedAt.Time)
		if err != nil {
			return nil, fmt.Errorf("检查用户令牌撤销状态失败: %w", err)
		}
		if isRevoked {
			return nil, errors.New("用户令牌已被撤销")
		}
	}

	return claims, nil
}

// GetJWTManager 获取JWT管理器
func (s *AuthService) GetJWTManager() *auth.JWTManager {
	return s.jwtManager
}

// CleanupExpiredBlacklistTokens 清理过期的黑名单令牌
func (s *AuthService) CleanupExpiredBlacklistTokens() error {
	if s.redisClient == nil {
		return errors.New("Redis客户端未配置")
	}

	ctx := context.Background()
	pattern := "blacklist:token:*"
	
	// 使用SCAN命令遍历所有黑名单令牌
	iter := s.redisClient.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		
		// 检查键是否存在（可能已过期）
		exists, err := s.redisClient.Exists(ctx, key).Result()
		if err != nil {
			continue
		}
		if exists == 0 {
			// 键已过期，无需处理
			continue
		}
	}

	return iter.Err()
}
