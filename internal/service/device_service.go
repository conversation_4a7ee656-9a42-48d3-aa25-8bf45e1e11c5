package service

import (
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DeviceService 设备服务接口
type DeviceService interface {
	// 管理员设备管理
	AdminCreateDevice(ctx context.Context, req *model.CreateDeviceRequest) (*model.Device, error)
	AdminGetDevice(ctx context.Context, deviceID string) (*model.Device, error)
	AdminUpdateDevice(ctx context.Context, deviceID string, req *model.UpdateDeviceRequest) (*model.Device, error)
	AdminDeleteDevice(ctx context.Context, deviceID string) error
	AdminListDevices(ctx context.Context, filter repository.DeviceFilter, pagination repository.Pagination) ([]*model.Device, int64, error)

	// 用户设备操作
	UserAssignDevice(ctx context.Context, userID string, req *model.AssignDeviceRequest) (*model.Device, error)
	UserUnassignDevice(ctx context.Context, userID, deviceID string) error
	UserListDevices(ctx context.Context, userID string, pagination repository.Pagination) ([]*model.Device, int64, error)
	UserGetDevice(ctx context.Context, userID, deviceID string) (*model.Device, error)

	// 设备类型管理
	CreateDeviceType(ctx context.Context, req *model.CreateDeviceTypeRequest) (*model.DeviceType, error)
	UpdateDeviceType(ctx context.Context, deviceTypeID string, req *model.UpdateDeviceTypeRequest) (*model.DeviceType, error)
	DeleteDeviceType(ctx context.Context, deviceTypeID string) error
	ListDeviceTypes(ctx context.Context, pagination repository.Pagination) ([]*model.DeviceType, int64, error)
	ListAllDeviceTypes(ctx context.Context) ([]*model.DeviceType, error)

	// 初始化预定义设备类型
	InitializePredefinedDeviceTypes(ctx context.Context) error
}

// deviceService 设备服务实现
type deviceService struct {
	deviceRepo     repository.DeviceRepository
	deviceTypeRepo repository.DeviceTypeRepository
}

// NewDeviceService 创建设备服务
func NewDeviceService(deviceRepo repository.DeviceRepository, deviceTypeRepo repository.DeviceTypeRepository) DeviceService {
	return &deviceService{
		deviceRepo:     deviceRepo,
		deviceTypeRepo: deviceTypeRepo,
	}
}

// AdminCreateDevice 管理员创建设备
func (s *deviceService) AdminCreateDevice(ctx context.Context, req *model.CreateDeviceRequest) (*model.Device, error) {
	// 验证设备类型是否存在
	_, err := s.deviceTypeRepo.GetByName(ctx, req.DeviceType)
	if err != nil {
		if err == model.ErrDeviceTypeNotFound {
			return nil, model.ErrInvalidDeviceType
		}
		return nil, err
	}

	// 检查序列号是否已存在（全局检查）
	exists, err := s.deviceRepo.ExistsBySerialNumber(ctx, req.SerialNumber)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrSerialNumberExists
	}

	// 检查IMEI码是否已存在（全局检查）
	exists, err = s.deviceRepo.ExistsByIMEICode(ctx, req.IMEICode)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrIMEICodeExists
	}

	// 创建设备（不分配给任何用户）
	device := &model.Device{
		DeviceType:   req.DeviceType,
		DeviceName:   req.DeviceName,
		SerialNumber: req.SerialNumber,
		IMEICode:     req.IMEICode,
		UserID:       nil, // 管理员创建时不分配用户
		Status:       model.DeviceStatusActive,
	}

	// 验证设备数据
	if err := device.Validate(); err != nil {
		return nil, err
	}

	// 保存设备
	if err := s.deviceRepo.Create(ctx, device); err != nil {
		return nil, err
	}

	return device, nil
}

// CreateDeviceType 创建设备类型
func (s *deviceService) CreateDeviceType(ctx context.Context, req *model.CreateDeviceTypeRequest) (*model.DeviceType, error) {
	// 检查设备类型名称是否已存在
	exists, err := s.deviceTypeRepo.ExistsByName(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrDeviceTypeAlreadyExists
	}

	// 创建设备类型
	deviceType := &model.DeviceType{
		Name:        req.Name,
		Description: req.Description,
	}

	// 验证设备类型数据
	if err := deviceType.Validate(); err != nil {
		return nil, err
	}

	// 保存设备类型
	if err := s.deviceTypeRepo.Create(ctx, deviceType); err != nil {
		return nil, err
	}

	return deviceType, nil
}

// UpdateDeviceType 更新设备类型
func (s *deviceService) UpdateDeviceType(ctx context.Context, deviceTypeID string, req *model.UpdateDeviceTypeRequest) (*model.DeviceType, error) {
	// 获取现有设备类型
	deviceType, err := s.deviceTypeRepo.GetByID(ctx, deviceTypeID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != "" && req.Name != deviceType.Name {
		// 检查新名称是否已存在
		exists, err := s.deviceTypeRepo.ExistsByName(ctx, req.Name)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, model.ErrDeviceTypeAlreadyExists
		}
		deviceType.Name = req.Name
	}

	if req.Description != "" {
		deviceType.Description = req.Description
	}

	// 验证更新后的设备类型数据
	if err := deviceType.Validate(); err != nil {
		return nil, err
	}

	// 保存更新
	if err := s.deviceTypeRepo.Update(ctx, deviceType); err != nil {
		return nil, err
	}

	return deviceType, nil
}

// DeleteDeviceType 删除设备类型
func (s *deviceService) DeleteDeviceType(ctx context.Context, deviceTypeID string) error {
	return s.deviceTypeRepo.Delete(ctx, deviceTypeID)
}

// ListDeviceTypes 获取设备类型列表（分页）
func (s *deviceService) ListDeviceTypes(ctx context.Context, pagination repository.Pagination) ([]*model.DeviceType, int64, error) {
	return s.deviceTypeRepo.List(ctx, pagination)
}

// ListAllDeviceTypes 获取所有设备类型列表（无分页）
func (s *deviceService) ListAllDeviceTypes(ctx context.Context) ([]*model.DeviceType, error) {
	deviceTypes, _, err := s.deviceTypeRepo.List(ctx, repository.Pagination{Page: 0, PageSize: 0})
	return deviceTypes, err
}

// InitializePredefinedDeviceTypes 初始化预定义设备类型
func (s *deviceService) InitializePredefinedDeviceTypes(ctx context.Context) error {
	for _, predefinedType := range model.PredefinedDeviceTypes {
		// 检查是否已存在
		exists, err := s.deviceTypeRepo.ExistsByName(ctx, predefinedType.Name)
		if err != nil {
			return fmt.Errorf("检查设备类型 %s 失败: %w", predefinedType.Name, err)
		}

		if !exists {
			// 创建设备类型
			deviceType := predefinedType
			if err := s.deviceTypeRepo.Create(ctx, &deviceType); err != nil {
				return fmt.Errorf("创建预定义设备类型 %s 失败: %w", predefinedType.Name, err)
			}
		}
	}

	return nil
}

// AdminGetDevice 管理员获取设备
func (s *deviceService) AdminGetDevice(ctx context.Context, deviceID string) (*model.Device, error) {
	return s.deviceRepo.GetByID(ctx, deviceID)
}

// AdminUpdateDevice 管理员更新设备
func (s *deviceService) AdminUpdateDevice(ctx context.Context, deviceID string, req *model.UpdateDeviceRequest) (*model.Device, error) {
	// 获取现有设备
	device, err := s.deviceRepo.GetByID(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 检查序列号唯一性
	if req.SerialNumber != "" && req.SerialNumber != device.SerialNumber {
		exists, err := s.deviceRepo.ExistsBySerialNumber(ctx, req.SerialNumber)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, model.ErrSerialNumberExists
		}
	}

	// 检查IMEI码唯一性
	if req.IMEICode != "" && req.IMEICode != device.IMEICode {
		exists, err := s.deviceRepo.ExistsByIMEICode(ctx, req.IMEICode)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, model.ErrIMEICodeExists
		}
	}

	// 更新设备字段
	if req.DeviceType != "" {
		device.DeviceType = req.DeviceType
	}
	if req.DeviceName != "" {
		device.DeviceName = req.DeviceName
	}
	if req.SerialNumber != "" {
		device.SerialNumber = req.SerialNumber
	}
	if req.IMEICode != "" {
		device.IMEICode = req.IMEICode
	}
	if req.Status != "" {
		device.Status = req.Status
	}

	// 验证设备数据
	if err := device.Validate(); err != nil {
		return nil, err
	}

	// 更新设备
	if err := s.deviceRepo.Update(ctx, device); err != nil {
		return nil, err
	}

	return device, nil
}

// AdminDeleteDevice 管理员删除设备
func (s *deviceService) AdminDeleteDevice(ctx context.Context, deviceID string) error {
	return s.deviceRepo.Delete(ctx, deviceID)
}

// AdminListDevices 管理员列出所有设备
func (s *deviceService) AdminListDevices(ctx context.Context, filter repository.DeviceFilter, pagination repository.Pagination) ([]*model.Device, int64, error) {
	return s.deviceRepo.List(ctx, filter, pagination)
}

// UserAssignDevice 用户添加设备
func (s *deviceService) UserAssignDevice(ctx context.Context, userID string, req *model.AssignDeviceRequest) (*model.Device, error) {
	// 验证用户ID
	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return nil, model.ErrInvalidID
	}

	// 根据序列号和IMEI码查找设备
	device, err := s.deviceRepo.GetBySerialNumberAndIMEI(ctx, req.SerialNumber, req.IMEICode)
	if err != nil {
		return nil, err
	}

	// 检查设备是否已分配
	if device.IsAssigned() {
		if device.UserID != nil && device.UserID.Hex() == userID {
			return nil, model.ErrCannotAssignOwnDevice
		}
		return nil, model.ErrDeviceAlreadyAssigned
	}

	// 分配设备给用户
	device.AssignToUser(userObjectID)

	// 更新数据库
	if err := s.deviceRepo.Update(ctx, device); err != nil {
		return nil, fmt.Errorf("分配设备失败: %w", err)
	}

	return device, nil
}

// UserUnassignDevice 用户移除设备
func (s *deviceService) UserUnassignDevice(ctx context.Context, userID, deviceID string) error {
	// 获取设备
	device, err := s.deviceRepo.GetByID(ctx, deviceID)
	if err != nil {
		return err
	}

	// 检查设备是否属于该用户
	if device.UserID == nil || device.UserID.Hex() != userID {
		return model.ErrDeviceNotFound
	}

	// 取消分配
	device.Unassign()

	// 更新数据库
	return s.deviceRepo.Update(ctx, device)
}

// UserListDevices 用户列出自己的设备
func (s *deviceService) UserListDevices(ctx context.Context, userID string, pagination repository.Pagination) ([]*model.Device, int64, error) {
	return s.deviceRepo.GetUserDevices(ctx, userID, pagination)
}

// UserGetDevice 用户获取自己的设备
func (s *deviceService) UserGetDevice(ctx context.Context, userID, deviceID string) (*model.Device, error) {
	device, err := s.deviceRepo.GetByID(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	// 检查设备是否属于该用户
	if device.UserID == nil || device.UserID.Hex() != userID {
		return nil, model.ErrDeviceNotFound
	}

	return device, nil
}
