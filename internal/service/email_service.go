package service

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/tls"
	"fmt"
	"log"
	"math/big"
	"strconv"
	"text/template"
	"time"

	"beacon/cloud/internal/config"
	"beacon/cloud/pkg/cache"

	"gopkg.in/gomail.v2"
)

// EmailService 邮件服务接口
type EmailService interface {
	SendVerificationCode(ctx context.Context, email string) (string, error)
	VerifyCode(ctx context.Context, email, code string) error
	SendResetPassword(ctx context.Context, email, newPassword string) error
	SendEmail(ctx context.Context, to, subject, body string) error
}

// emailService 邮件服务实现
type emailService struct {
	config       *config.MailConfig
	cacheManager *cache.Manager
	maxRetries   int
}

// NewEmailService 创建邮件服务
func NewEmailService(cfg *config.MailConfig, cacheManager *cache.Manager) EmailService {
	return &emailService{
		config:       cfg,
		cacheManager: cacheManager,
		maxRetries:   3, // 默认重试3次
	}
}

// SendVerificationCode 发送验证码（异步）
func (s *emailService) SendVerificationCode(ctx context.Context, email string) (string, error) {
	// 生成6位数字验证码
	code, err := s.generateVerificationCode()
	if err != nil {
		return "", fmt.Errorf("生成验证码失败: %w", err)
	}

	// 将验证码存储到Redis，10分钟过期
	cacheKey := fmt.Sprintf("email:verification:%s", email)
	if err := s.cacheManager.Set(ctx, cacheKey, code, 10*time.Minute); err != nil {
		return "", fmt.Errorf("存储验证码失败: %w", err)
	}

	// 异步发送邮件
	go func() {
		subject := "Beacon Cloud - 邮箱验证码"
		body, err := s.renderTemplate(VerifyEmailTemplate, map[string]string{
			"Content": code,
		})
		if err != nil {
			log.Printf("[EmailService] 渲染验证码邮件模板失败: %v", err)
			return
		}

		if err := s.sendEmailWithRetry([]string{email}, subject, body); err != nil {
			log.Printf("[EmailService] 发送验证码邮件失败: %v", err)
		} else {
			log.Printf("[EmailService] 验证码邮件发送成功: %s", email)
		}
	}()

	return code, nil
}

// VerifyCode 验证验证码
func (s *emailService) VerifyCode(ctx context.Context, email, code string) error {
	if code == "" {
		return fmt.Errorf("验证码不能为空")
	}

	// 从Redis获取验证码
	cacheKey := fmt.Sprintf("email:verification:%s", email)
	var storedCode string
	if err := s.cacheManager.Get(ctx, cacheKey, &storedCode); err != nil {
		return fmt.Errorf("验证码已过期或不存在")
	}

	// 验证码比较
	if storedCode != code {
		return fmt.Errorf("验证码错误")
	}

	// 验证成功后删除验证码
	s.cacheManager.Delete(ctx, cacheKey)

	return nil
}

// SendResetPassword 发送重置密码邮件（异步）
func (s *emailService) SendResetPassword(ctx context.Context, email, newPassword string) error {
	// 异步发送邮件
	go func() {
		subject := "Beacon Global Tech - 密码重置成功"
		body, err := s.renderTemplate(ResetPasswordTemplate, map[string]string{
			"Content": newPassword,
		})
		if err != nil {
			log.Printf("[EmailService] 渲染重置密码邮件模板失败: %v", err)
			return
		}

		if err := s.sendEmailWithRetry([]string{email}, subject, body); err != nil {
			log.Printf("[EmailService] 发送重置密码邮件失败: %v", err)
		} else {
			log.Printf("[EmailService] 重置密码邮件发送成功: %s", email)
		}
	}()

	return nil
}

// SendEmail 发送邮件（异步）
func (s *emailService) SendEmail(ctx context.Context, to, subject, body string) error {
	// 异步发送邮件
	go func() {
		if err := s.sendEmailWithRetry([]string{to}, subject, body); err != nil {
			log.Printf("[EmailService] 发送邮件失败: %v", err)
		} else {
			log.Printf("[EmailService] 邮件发送成功: %s", to)
		}
	}()

	return nil
}

// sendEmailWithRetry 带重试的邮件发送
func (s *emailService) sendEmailWithRetry(to []string, subject, body string) error {
	if s.config.Host == "" {
		return fmt.Errorf("邮件服务未配置")
	}

	// 发送邮件，支持重试
	for attempt := 0; attempt < s.maxRetries; attempt++ {
		err := s.sendEmailDirect(to, subject, body)
		if err == nil {
			return nil
		}

		log.Printf("[EmailService] 邮件发送失败 (尝试 %d/%d): %v", attempt+1, s.maxRetries, err)

		// 重试前等待（指数退避）
		if attempt < s.maxRetries-1 {
			time.Sleep(time.Duration(1<<attempt) * time.Second)
		}
	}

	return fmt.Errorf("邮件发送失败，已重试 %d 次", s.maxRetries)
}

// sendEmailDirect 直接发送邮件
func (s *emailService) sendEmailDirect(to []string, subject, body string) error {
	smtpPort, err := strconv.Atoi(s.config.Port)
	if err != nil {
		return fmt.Errorf("无效的SMTP端口: %v", err)
	}

	mail := gomail.NewMessage()
	mail.SetHeader("From", "BeaconGlobalTechnology"+"<"+s.config.Username+">")
	mail.SetHeader("To", to...)
	mail.SetHeader("Subject", subject)
	mail.SetBody("text/html", body)

	// 创建 SMTP 拨号器
	dialer := gomail.NewDialer(s.config.Host, smtpPort, s.config.Username, s.config.Password)
	dialer.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	// 发送邮件
	return dialer.DialAndSend(mail)
}

// generateVerificationCode 生成验证码
func (s *emailService) generateVerificationCode() (string, error) {
	max := big.NewInt(1000000)
	n, err := rand.Int(rand.Reader, max)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%06d", n.Int64()), nil
}

// renderTemplate 渲染模板
func (s *emailService) renderTemplate(templateStr string, data interface{}) (string, error) {
	// 解析模板
	tmpl, err := template.New("email").Parse(templateStr)
	if err != nil {
		return "", err
	}

	// 渲染模板到缓冲区
	var renderedHTML bytes.Buffer
	if err := tmpl.Execute(&renderedHTML, data); err != nil {
		return "", err
	}

	// 返回渲染后的 HTML 字符串
	return renderedHTML.String(), nil
}

// 邮件模板常量
const VerifyEmailTemplate = `
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td align="center">
                    <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png"
                         alt="BeaconGlobalTechnology"
                         style="max-height: 50px; vertical-align: middle;">
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td style="padding: 40px; text-align: center; word-wrap: break-word; word-break: break-word;">
              <p style="font-size: 16px; color: #666666; margin-bottom: 20px;">感谢您选择我们！您的验证码为：</p>

              <div style="font-size: 32px; font-weight: bold; color: #007BFF; background: #f0f8ff; padding: 15px; border-radius: 8px; display: inline-block;">
                {{.Content}}
              </div>
              <p style="font-size: 16px; color: #666666; margin-top: 20px;">
                该验证码将在 <strong>10分钟</strong> 后失效，请尽快使用。
              </p>
              <a href="https://www.beaconglobaltech.com"
                 style="display: inline-block; margin-top: 20px; padding: 12px 24px; font-size: 16px; font-weight: bold; color: #ffffff; background-color: #007BFF; border-radius: 8px; text-decoration: none;">
                访问我们的网站
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>如果您没有请求此验证码，请忽略此邮件。</p>
              <p>如有任何问题，请联系
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
`

const ResetPasswordTemplate = `
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png"
                   alt="BeaconGlobalTechnology"
                   style="max-height: 50px; vertical-align: middle;">
            </td>
          </tr>
          <tr>
            <td style="padding: 40px; text-align: center; word-wrap: break-word; word-break: break-word;">
              <p style="font-size: 16px; color: #666666; margin-bottom: 20px;">我们收到了您的重置密码请求。您的新密码为：</p>
              <div style="font-size: 32px; font-weight: bold; color: #007BFF; background: #f0f8ff; padding: 15px; border-radius: 8px; display: inline-block;">
                {{.Content}}
              </div>
              <p style="font-size: 16px; color: #666666; margin-top: 20px;">请立即登录并修改密码以确保账户安全。</p>
              <a href="https://www.beaconglobaltech.com"
                 style="display: inline-block; margin-top: 20px; padding: 12px 24px; font-size: 16px; font-weight: bold; color: #ffffff; background-color: #007BFF; border-radius: 8px; text-decoration: none;">
                访问我们的网站
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>如有任何问题，请联系
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>`
