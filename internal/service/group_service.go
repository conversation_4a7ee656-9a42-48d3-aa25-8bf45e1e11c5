package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/pkg/cache"

	"github.com/google/uuid"
)

// GroupService 分组服务接口
type GroupService interface {
	// 分组管理
	CreateGroup(ctx context.Context, userID string, req *model.CreateGroupRequest) (*model.Group, error)
	GetGroup(ctx context.Context, groupID string) (*model.GroupResponse, error)
	UpdateGroup(ctx context.Context, userID, groupID string, req *model.UpdateGroupRequest) (*model.Group, error)
	DeleteGroup(ctx context.Context, userID, groupID string) error
	ListGroups(ctx context.Context, filter repository.GroupFilter, pagination repository.Pagination) ([]*model.GroupResponse, int64, error)
	GetUserGroups(ctx context.Context, userID string) ([]*model.GroupResponse, error)

	// 成员管理
	JoinGroup(ctx context.Context, userID string, req *model.JoinGroupRequest) (*model.GroupJoinRequest, error)
	ReviewJoinRequest(ctx context.Context, creatorID, requestID string, req *model.ReviewJoinRequestRequest) error
	RemoveMember(ctx context.Context, creatorID, groupID, userID string) error
	LeaveGroup(ctx context.Context, userID, groupID string) error
	ListGroupMembers(ctx context.Context, groupID string, pagination repository.Pagination) ([]*model.GroupMemberResponse, int64, error)
	ListJoinRequests(ctx context.Context, groupID string, filter repository.JoinRequestFilter, pagination repository.Pagination) ([]*model.JoinRequestResponse, int64, error)
	ListUserGroupJoinRequests(ctx context.Context, userID string, filter repository.JoinRequestFilter, pagination repository.Pagination) ([]*model.JoinRequestResponse, int64, error)

	// Topic管理
	CreateTopic(ctx context.Context, userID, groupID string, req *model.CreateTopicRequest) (*model.Topic, error)
	DeleteTopic(ctx context.Context, userID, groupID, topicID string) error
	ListGroupTopics(ctx context.Context, groupID string, pagination repository.Pagination) ([]*model.TopicResponse, int64, error)

	// 权限管理
	SetTopicPermission(ctx context.Context, creatorID, groupID string, req *model.SetTopicPermissionRequest) error
	RemoveTopicPermission(ctx context.Context, creatorID, groupID, userID, fullTopic string) error
	ListTopicPermissions(ctx context.Context, groupID, fullTopic string, pagination repository.Pagination) ([]*model.TopicPermissionResponse, int64, error)
	CheckTopicPermission(ctx context.Context, userID, fullTopic, action string) (bool, error)

	// 辅助方法
	IsGroupCreator(ctx context.Context, userID, groupID string) (bool, error)
	IsGroupMember(ctx context.Context, userID, groupID string) (bool, error)

	// 批量权限检查
	CheckMultipleTopicPermissions(ctx context.Context, userID string, topics []string, action string) (map[string]bool, error)
}

// groupService 分组服务实现
type groupService struct {
	groupRepo       repository.GroupRepository
	memberRepo      repository.GroupMemberRepository
	topicRepo       repository.TopicRepository
	permissionRepo  repository.TopicPermissionRepository
	joinRequestRepo repository.GroupJoinRequestRepository
	userService     UserService
	cacheManager    *cache.Manager
}

// NewGroupService 创建分组服务
func NewGroupService(
	groupRepo repository.GroupRepository,
	memberRepo repository.GroupMemberRepository,
	topicRepo repository.TopicRepository,
	permissionRepo repository.TopicPermissionRepository,
	joinRequestRepo repository.GroupJoinRequestRepository,
	userService UserService,
	cacheManager *cache.Manager,
) GroupService {
	return &groupService{
		groupRepo:       groupRepo,
		memberRepo:      memberRepo,
		topicRepo:       topicRepo,
		permissionRepo:  permissionRepo,
		joinRequestRepo: joinRequestRepo,
		userService:     userService,
		cacheManager:    cacheManager,
	}
}

// CreateGroup 创建分组
func (s *groupService) CreateGroup(ctx context.Context, userID string, req *model.CreateGroupRequest) (*model.Group, error) {
	// 检查用户是否存在
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 检查用户创建的分组数量限制（非管理员最多20个）
	if !user.IsAdmin() {
		count, err := s.groupRepo.CountByCreatorID(ctx, userID)
		if err != nil {
			return nil, err
		}
		if count >= 20 {
			return nil, model.ErrGroupLimitReached
		}
	}

	// 检查分组名称是否已存在（同一创建者下）
	exists, err := s.groupRepo.ExistsByName(ctx, req.Name, userID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrGroupAlreadyExists
	}

	// 创建分组
	group := &model.Group{
		GroupID:     generateGroupID(),
		Name:        req.Name,
		Description: req.Description,
		CreatorID:   userID,
		MaxMembers:  req.MaxMembers,
	}

	if group.MaxMembers == 0 {
		group.MaxMembers = 100 // 默认最大成员数
	}

	if err := s.groupRepo.Create(ctx, group); err != nil {
		return nil, err
	}

	// 将创建者添加为分组成员
	member := &model.GroupMember{
		GroupID: group.GroupID,
		UserID:  userID,
		Role:    model.GroupRoleCreator,
	}

	if err := s.memberRepo.Create(ctx, member); err != nil {
		// 如果添加成员失败，删除已创建的分组
		s.groupRepo.Delete(ctx, group.ID.Hex())
		return nil, err
	}

	return group, nil
}

// GetGroup 获取分组信息
func (s *groupService) GetGroup(ctx context.Context, groupID string) (*model.GroupResponse, error) {
	group, err := s.groupRepo.GetByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	response := group.ToResponse()
	memberCount, err := s.memberRepo.CountByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	response.MemberCount = int(memberCount)
	topicCount, err := s.topicRepo.CountByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	response.TopicCount = int(topicCount)
	return &response, nil
}

// UpdateGroup 更新分组信息
func (s *groupService) UpdateGroup(ctx context.Context, userID, groupID string, req *model.UpdateGroupRequest) (*model.Group, error) {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, userID, groupID)
	if err != nil {
		return nil, err
	}
	if !isCreator {
		return nil, model.ErrNotGroupCreator
	}

	// 获取分组
	group, err := s.groupRepo.GetByGroupID(ctx, groupID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != "" {
		// 检查新名称是否已存在
		if req.Name != group.Name {
			exists, err := s.groupRepo.ExistsByName(ctx, req.Name, userID)
			if err != nil {
				return nil, err
			}
			if exists {
				return nil, model.ErrGroupAlreadyExists
			}
		}
		group.Name = req.Name
	}

	if req.Description != "" {
		group.Description = req.Description
	}

	if req.Status != "" {
		group.Status = req.Status
	}

	if req.MaxMembers > 0 {
		// 检查新的最大成员数是否小于当前成员数
		currentMemberCount, err := s.memberRepo.CountByGroupID(ctx, groupID)
		if err != nil {
			return nil, err
		}
		if int64(req.MaxMembers) < currentMemberCount {
			return nil, fmt.Errorf("最大成员数不能小于当前成员数(%d)", currentMemberCount)
		}
		group.MaxMembers = req.MaxMembers
	}

	if err := s.groupRepo.Update(ctx, group); err != nil {
		return nil, err
	}

	return group, nil
}

// DeleteGroup 删除分组
func (s *groupService) DeleteGroup(ctx context.Context, userID, groupID string) error {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, userID, groupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 获取分组
	group, err := s.groupRepo.GetByGroupID(ctx, groupID)
	if err != nil {
		return err
	}

	// 删除所有相关数据
	// 1. 删除所有Topic权限
	if err := s.permissionRepo.DeleteByGroupID(ctx, groupID); err != nil {
		return err
	}

	// 2. 删除所有Topic
	topics, _, err := s.topicRepo.ListByGroupID(ctx, groupID, repository.Pagination{})
	if err != nil {
		return err
	}
	for _, topic := range topics {
		if err := s.topicRepo.Delete(ctx, topic.ID.Hex()); err != nil {
			return err
		}
	}

	// 3. 删除所有成员
	members, _, err := s.memberRepo.ListByGroupID(ctx, groupID, repository.Pagination{})
	if err != nil {
		return err
	}
	for _, member := range members {
		if err := s.memberRepo.Delete(ctx, member.ID.Hex()); err != nil {
			return err
		}
	}

	// 4. 删除所有加入申请
	requests, _, err := s.joinRequestRepo.ListByGroupID(ctx, groupID, repository.JoinRequestFilter{}, repository.Pagination{})
	if err != nil {
		return err
	}
	for _, request := range requests {
		if err := s.joinRequestRepo.Delete(ctx, request.ID.Hex()); err != nil {
			return err
		}
	}

	// 5. 删除分组
	if err := s.groupRepo.Delete(ctx, group.ID.Hex()); err != nil {
		return err
	}

	return nil
}

// ListGroups 获取分组列表
func (s *groupService) ListGroups(ctx context.Context, filter repository.GroupFilter, pagination repository.Pagination) ([]*model.GroupResponse, int64, error) {
	groups, total, err := s.groupRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.GroupResponse
	for _, group := range groups {
		response := group.ToResponse()

		// 获取成员数量
		memberCount, err := s.memberRepo.CountByGroupID(ctx, group.GroupID)
		if err == nil {
			response.MemberCount = int(memberCount)
		}

		// 获取Topic数量
		topicCount, err := s.topicRepo.CountByGroupID(ctx, group.GroupID)
		if err == nil {
			response.TopicCount = int(topicCount)
		}

		responses = append(responses, &response)
	}

	return responses, total, nil
}

// GetUserGroups 获取用户的分组列表
func (s *groupService) GetUserGroups(ctx context.Context, userID string) ([]*model.GroupResponse, error) {
	// 获取用户创建的分组
	createdGroups, err := s.groupRepo.GetByCreatorID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取用户加入的分组
	members, _, err := s.memberRepo.ListByUserID(ctx, userID, repository.Pagination{})
	if err != nil {
		return nil, err
	}

	// 合并分组信息
	groupMap := make(map[string]*model.Group)

	// 添加创建的分组
	for _, group := range createdGroups {
		groupMap[group.GroupID] = group
	}

	// 添加加入的分组
	for _, member := range members {
		if _, exists := groupMap[member.GroupID]; !exists {
			group, err := s.groupRepo.GetByGroupID(ctx, member.GroupID)
			if err == nil {
				groupMap[member.GroupID] = group
			}
		}
	}

	// 转换为响应格式
	var responses []*model.GroupResponse
	for _, group := range groupMap {
		response := group.ToResponse()

		// 获取成员数量
		memberCount, err := s.memberRepo.CountByGroupID(ctx, group.GroupID)
		if err == nil {
			response.MemberCount = int(memberCount)
		}

		// 获取Topic数量
		topicCount, err := s.topicRepo.CountByGroupID(ctx, group.GroupID)
		if err == nil {
			response.TopicCount = int(topicCount)
		}

		responses = append(responses, &response)
	}

	return responses, nil
}

// JoinGroup 申请加入分组
func (s *groupService) JoinGroup(ctx context.Context, userID string, req *model.JoinGroupRequest) (*model.GroupJoinRequest, error) {
	// 检查分组是否存在
	group, err := s.groupRepo.GetByGroupID(ctx, req.GroupID)
	if err != nil {
		return nil, err
	}

	// 检查分组是否活跃
	if !group.IsActive() {
		return nil, model.ErrGroupInactive
	}

	// 检查用户是否已是分组成员
	isMember, err := s.IsGroupMember(ctx, userID, req.GroupID)
	if err != nil {
		return nil, err
	}
	if isMember {
		return nil, model.ErrAlreadyGroupMember
	}

	// 检查是否已有待处理的申请
	pendingExists, err := s.joinRequestRepo.ExistsByGroupAndUser(ctx, req.GroupID, userID)
	if err != nil {
		return nil, err
	}
	if pendingExists {
		return nil, model.ErrJoinRequestAlreadyExists
	}

	// 检查分组成员数量限制
	memberCount, err := s.memberRepo.CountByGroupID(ctx, req.GroupID)
	if err != nil {
		return nil, err
	}
	if int(memberCount) >= group.MaxMembers {
		return nil, model.ErrGroupMemberLimitReached
	}

	// 检查是否存在历史申请（任何状态）
	existingRequest, err := s.joinRequestRepo.GetByGroupAndUser(ctx, req.GroupID, userID)
	if err != nil && err != model.ErrJoinRequestNotFound {
		return nil, err
	}

	var joinRequest *model.GroupJoinRequest

	if existingRequest != nil {
		// 存在历史申请，更新为新的待处理申请
		existingRequest.Status = model.JoinRequestPending
		existingRequest.Message = req.Message
		existingRequest.OwnerID = group.CreatorID // 确保设置分组创建者ID
		existingRequest.ReviewedBy = ""
		existingRequest.ReviewedAt = nil
		existingRequest.BeforeUpdate()

		if err := s.joinRequestRepo.Update(ctx, existingRequest); err != nil {
			return nil, err
		}
		joinRequest = existingRequest
	} else {
		// 不存在历史申请，创建新申请
		joinRequest = &model.GroupJoinRequest{
			GroupID: req.GroupID,
			UserID:  userID,
			OwnerID: group.CreatorID, // 设置分组创建者ID，优化查询性能
			Message: req.Message,
		}

		if err := s.joinRequestRepo.Create(ctx, joinRequest); err != nil {
			return nil, err
		}
	}

	return joinRequest, nil
}

// ReviewJoinRequest 审核加入申请
func (s *groupService) ReviewJoinRequest(ctx context.Context, creatorID, requestID string, req *model.ReviewJoinRequestRequest) error {
	// 获取加入申请
	joinRequest, err := s.joinRequestRepo.GetByID(ctx, requestID)
	if err != nil {
		return err
	}

	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, creatorID, joinRequest.GroupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 检查申请状态
	if joinRequest.Status != model.JoinRequestPending {
		return model.ErrJoinRequestAlreadyProcessed
	}

	// 更新申请状态
	joinRequest.Status = req.Status
	joinRequest.ReviewedBy = creatorID
	now := time.Now()
	joinRequest.ReviewedAt = &now

	if err := s.joinRequestRepo.Update(ctx, joinRequest); err != nil {
		return err
	}

	// 如果批准，添加用户为分组成员
	if req.Status == model.JoinRequestApproved {
		// 再次检查分组成员数量限制
		group, err := s.groupRepo.GetByGroupID(ctx, joinRequest.GroupID)
		if err != nil {
			return err
		}

		memberCount, err := s.memberRepo.CountByGroupID(ctx, joinRequest.GroupID)
		if err != nil {
			return err
		}
		if int(memberCount) >= group.MaxMembers {
			return model.ErrGroupMemberLimitReached
		}

		// 添加成员
		member := &model.GroupMember{
			GroupID: joinRequest.GroupID,
			UserID:  joinRequest.UserID,
			Role:    model.GroupRoleMember,
		}

		if err := s.memberRepo.Create(ctx, member); err != nil {
			return err
		}
	}

	return nil
}

// RemoveMember 移除分组成员
func (s *groupService) RemoveMember(ctx context.Context, creatorID, groupID, userID string) error {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, creatorID, groupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 不能移除创建者自己
	if creatorID == userID {
		return model.ErrCannotRemoveCreator
	}

	// 检查用户是否为分组成员
	member, err := s.memberRepo.GetByGroupAndUser(ctx, groupID, userID)
	if err != nil {
		return err
	}

	// 删除成员
	if err := s.memberRepo.Delete(ctx, member.ID.Hex()); err != nil {
		return err
	}

	// 删除用户在该分组的所有Topic权限
	permissions, _, err := s.permissionRepo.ListByUserID(ctx, userID, repository.Pagination{})
	if err != nil {
		return err
	}

	for _, permission := range permissions {
		if permission.GroupID == groupID {
			s.permissionRepo.Delete(ctx, permission.ID.Hex())
		}
	}

	return nil
}

// LeaveGroup 退出分组
func (s *groupService) LeaveGroup(ctx context.Context, userID, groupID string) error {
	// 检查用户是否为分组成员
	member, err := s.memberRepo.GetByGroupAndUser(ctx, groupID, userID)
	if err != nil {
		return err
	}

	// 创建者不能退出分组
	if member.Role == model.GroupRoleCreator {
		return model.ErrCannotLeaveAsCreator
	}

	// 删除成员
	if err := s.memberRepo.Delete(ctx, member.ID.Hex()); err != nil {
		return err
	}

	// 删除用户在该分组的所有Topic权限
	permissions, _, err := s.permissionRepo.ListByUserID(ctx, userID, repository.Pagination{})
	if err != nil {
		return err
	}

	for _, permission := range permissions {
		if permission.GroupID == groupID {
			s.permissionRepo.Delete(ctx, permission.ID.Hex())
		}
	}

	return nil
}

// ListGroupMembers 获取分组成员列表
func (s *groupService) ListGroupMembers(ctx context.Context, groupID string, pagination repository.Pagination) ([]*model.GroupMemberResponse, int64, error) {
	members, total, err := s.memberRepo.ListByGroupID(ctx, groupID, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.GroupMemberResponse
	for _, member := range members {
		response := member.ToResponse()

		// 获取用户信息
		user, err := s.userService.GetUserByID(ctx, member.UserID)
		if err == nil {
			response.Email = user.Email
			response.DisplayName = user.GetDisplayName()
		}

		responses = append(responses, &response)
	}

	return responses, total, nil
}

// ListJoinRequests 获取分组加入申请列表
func (s *groupService) ListJoinRequests(ctx context.Context, groupID string, filter repository.JoinRequestFilter, pagination repository.Pagination) ([]*model.JoinRequestResponse, int64, error) {
	requests, total, err := s.joinRequestRepo.ListByGroupID(ctx, groupID, filter, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.JoinRequestResponse
	for _, request := range requests {
		response := request.ToResponse()

		// 获取用户信息
		user, err := s.userService.GetUserByID(ctx, request.UserID)
		if err == nil {
			response.Email = user.Email
			response.DisplayName = user.GetDisplayName()
		}

		// 获取分组信息
		group, err := s.groupRepo.GetByGroupID(ctx, request.GroupID)
		if err == nil {
			response.GroupName = group.Name
		}

		responses = append(responses, &response)
	}

	return responses, total, nil
}

// ListUserGroupJoinRequests 获取用户所属分组的加入申请列表（性能优化版本）
func (s *groupService) ListUserGroupJoinRequests(ctx context.Context, userID string, filter repository.JoinRequestFilter, pagination repository.Pagination) ([]*model.JoinRequestResponse, int64, error) {
	// 直接根据owner_id查询申请列表，无需先查询分组表
	requests, total, err := s.joinRequestRepo.ListByOwnerID(ctx, userID, filter, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.JoinRequestResponse
	for _, request := range requests {
		response := request.ToResponse()

		// 获取用户信息
		user, err := s.userService.GetUserByID(ctx, request.UserID)
		if err == nil {
			response.Email = user.Email
			response.DisplayName = user.GetDisplayName()
		}

		// 获取分组信息
		group, err := s.groupRepo.GetByGroupID(ctx, request.GroupID)
		if err == nil {
			response.GroupName = group.Name
		}

		responses = append(responses, &response)
	}

	return responses, total, nil
}

// CreateTopic 创建Topic
func (s *groupService) CreateTopic(ctx context.Context, userID, groupID string, req *model.CreateTopicRequest) (*model.Topic, error) {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, userID, groupID)
	if err != nil {
		return nil, err
	}
	if !isCreator {
		return nil, model.ErrNotGroupCreator
	}

	// 检查Topic名称在分组内是否已存在
	exists, err := s.topicRepo.ExistsByGroupAndName(ctx, groupID, req.TopicName)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrTopicAlreadyExists
	}

	// 生成完整Topic名称
	fullName := fmt.Sprintf("%s/%s", groupID, req.TopicName)

	// 检查完整Topic名称是否已存在
	exists, err = s.topicRepo.ExistsByFullName(ctx, fullName)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, model.ErrTopicAlreadyExists
	}

	// 创建Topic
	topic := &model.Topic{
		GroupID:   groupID,
		TopicName: req.TopicName,
		FullName:  fullName,
		CreatorID: userID,
	}

	if err := s.topicRepo.Create(ctx, topic); err != nil {
		return nil, err
	}

	return topic, nil
}

// DeleteTopic 删除Topic
func (s *groupService) DeleteTopic(ctx context.Context, userID, groupID, topicID string) error {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, userID, groupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 获取Topic
	topic, err := s.topicRepo.GetByID(ctx, topicID)
	if err != nil {
		return err
	}

	// 检查Topic是否属于该分组
	if topic.GroupID != groupID {
		return model.ErrTopicNotFound
	}

	// 删除Topic的所有权限
	if err := s.permissionRepo.DeleteByTopic(ctx, topic.FullName); err != nil {
		return err
	}

	// 删除Topic
	if err := s.topicRepo.Delete(ctx, topicID); err != nil {
		return err
	}

	// 清除Redis缓存
	s.clearTopicPermissionCache(topic.FullName)

	return nil
}

// ListGroupTopics 获取分组Topic列表
func (s *groupService) ListGroupTopics(ctx context.Context, groupID string, pagination repository.Pagination) ([]*model.TopicResponse, int64, error) {
	topics, total, err := s.topicRepo.ListByGroupID(ctx, groupID, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.TopicResponse
	for _, topic := range topics {
		response := topic.ToResponse()
		responses = append(responses, &response)
	}

	return responses, total, nil
}

// SetTopicPermission 设置Topic权限
func (s *groupService) SetTopicPermission(ctx context.Context, creatorID, groupID string, req *model.SetTopicPermissionRequest) error {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, creatorID, groupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 检查用户是否为分组成员
	isMember, err := s.IsGroupMember(ctx, req.UserID, groupID)
	if err != nil {
		return err
	}
	if !isMember {
		return model.ErrNotGroupMember
	}

	// 检查Topic是否存在且属于该分组
	topic, err := s.topicRepo.GetByFullName(ctx, req.FullTopic)
	if err != nil {
		return err
	}
	if topic.GroupID != groupID {
		return model.ErrTopicNotFound
	}

	// 创建或更新权限
	permission := &model.TopicPermission{
		UserID:    req.UserID,
		GroupID:   groupID,
		FullTopic: req.FullTopic,
		CanPub:    req.CanPub,
		CanSub:    req.CanSub,
	}

	if err := s.permissionRepo.Upsert(ctx, permission); err != nil {
		return err
	}

	// 更新Redis缓存
	s.updateTopicPermissionCache(req.UserID, req.FullTopic, permission)

	return nil
}

// RemoveTopicPermission 移除Topic权限
func (s *groupService) RemoveTopicPermission(ctx context.Context, creatorID, groupID, userID, fullTopic string) error {
	// 检查是否为分组创建者
	isCreator, err := s.IsGroupCreator(ctx, creatorID, groupID)
	if err != nil {
		return err
	}
	if !isCreator {
		return model.ErrNotGroupCreator
	}

	// 检查Topic是否属于该分组
	topic, err := s.topicRepo.GetByFullName(ctx, fullTopic)
	if err != nil {
		return err
	}
	if topic.GroupID != groupID {
		return model.ErrTopicNotFound
	}

	// 删除权限
	if err := s.permissionRepo.DeleteByUserAndTopic(ctx, userID, fullTopic); err != nil {
		return err
	}

	// 清除Redis缓存
	s.clearUserTopicPermissionCache(userID, fullTopic)

	return nil
}

// ListTopicPermissions 获取Topic权限列表
func (s *groupService) ListTopicPermissions(ctx context.Context, groupID, fullTopic string, pagination repository.Pagination) ([]*model.TopicPermissionResponse, int64, error) {
	// 检查Topic是否属于该分组
	topic, err := s.topicRepo.GetByFullName(ctx, fullTopic)
	if err != nil {
		return nil, 0, err
	}
	if topic.GroupID != groupID {
		return nil, 0, model.ErrTopicNotFound
	}

	permissions, total, err := s.permissionRepo.ListByTopic(ctx, fullTopic, pagination)
	if err != nil {
		return nil, 0, err
	}

	var responses []*model.TopicPermissionResponse
	for _, permission := range permissions {
		response := permission.ToResponse()

		// 获取用户信息
		user, err := s.userService.GetUserByID(ctx, permission.UserID)
		if err == nil {
			response.Email = user.Email
			response.DisplayName = user.GetDisplayName()
		}

		responses = append(responses, &response)
	}

	return responses, total, nil
}

// CheckTopicPermission 检查Topic权限
func (s *groupService) CheckTopicPermission(ctx context.Context, userID, fullTopic, action string) (bool, error) {
	// 参数验证
	if userID == "" || fullTopic == "" || action == "" {
		return false, fmt.Errorf("参数不能为空: userID=%s, fullTopic=%s, action=%s", userID, fullTopic, action)
	}

	// 验证action类型
	if action != "publish" && action != "subscribe" {
		return false, fmt.Errorf("无效的操作类型: %s", action)
	}

	// 首先检查Redis缓存
	cacheKey := fmt.Sprintf("topic_permission:%s:%s", userID, fullTopic)
	if s.cacheManager != nil {
		var permission model.TopicPermission
		if err := s.cacheManager.Get(ctx, cacheKey, &permission); err == nil {
			// 缓存命中
			switch action {
			case "publish":
				return permission.CanPub, nil
			case "subscribe":
				return permission.CanSub, nil
			}
		}
	}

	// 从数据库查询权限
	permission, err := s.permissionRepo.GetByUserAndTopic(ctx, userID, fullTopic)
	if err != nil {
		// 检查是否是权限不存在的错误
		if err == model.ErrTopicPermissionDenied {
			// 用户没有该Topic的权限，缓存这个结果避免重复查询
			noPermission := &model.TopicPermission{
				UserID:    userID,
				FullTopic: fullTopic,
				CanPub:    false,
				CanSub:    false,
			}
			if s.cacheManager != nil {
				s.cacheManager.Set(ctx, cacheKey, noPermission, 10*time.Minute) // 较短的缓存时间
			}
			return false, nil
		}
		// 其他数据库错误
		return false, fmt.Errorf("查询Topic权限失败: %w", err)
	}

	// 更新缓存
	if s.cacheManager != nil {
		s.cacheManager.Set(ctx, cacheKey, permission, time.Hour)
	}

	switch action {
	case "publish":
		return permission.CanPub, nil
	case "subscribe":
		return permission.CanSub, nil
	}

	return false, nil
}

// CheckMultipleTopicPermissions 批量检查Topic权限
func (s *groupService) CheckMultipleTopicPermissions(ctx context.Context, userID string, topics []string, action string) (map[string]bool, error) {
	if userID == "" || len(topics) == 0 || action == "" {
		return nil, fmt.Errorf("参数不能为空")
	}

	// 验证action类型
	if action != "publish" && action != "subscribe" {
		return nil, fmt.Errorf("无效的操作类型: %s", action)
	}

	result := make(map[string]bool)
	uncachedTopics := make([]string, 0)

	// 首先检查缓存
	if s.cacheManager != nil {
		for _, topic := range topics {
			cacheKey := fmt.Sprintf("topic_permission:%s:%s", userID, topic)
			var permission model.TopicPermission
			if err := s.cacheManager.Get(ctx, cacheKey, &permission); err == nil {
				// 缓存命中
				switch action {
				case "publish":
					result[topic] = permission.CanPub
				case "subscribe":
					result[topic] = permission.CanSub
				}
			} else {
				uncachedTopics = append(uncachedTopics, topic)
			}
		}
	} else {
		uncachedTopics = topics
	}

	// 逐个查询未缓存的权限（简化版本）
	for _, topic := range uncachedTopics {
		hasPermission, err := s.CheckTopicPermission(ctx, userID, topic, action)
		if err != nil {
			// 查询失败，默认无权限
			result[topic] = false
		} else {
			result[topic] = hasPermission
		}
	}

	return result, nil
}

// IsGroupCreator 检查是否为分组创建者
func (s *groupService) IsGroupCreator(ctx context.Context, userID, groupID string) (bool, error) {
	group, err := s.groupRepo.GetByGroupID(ctx, groupID)
	if err != nil {
		return false, err
	}
	return group.IsCreator(userID), nil
}

// IsGroupMember 检查是否为分组成员
func (s *groupService) IsGroupMember(ctx context.Context, userID, groupID string) (bool, error) {
	return s.memberRepo.ExistsByGroupAndUser(ctx, groupID, userID)
}

// updateTopicPermissionCache 更新Topic权限缓存
func (s *groupService) updateTopicPermissionCache(userID, fullTopic string, permission *model.TopicPermission) {
	if s.cacheManager == nil {
		return
	}

	ctx := context.Background()
	cacheKey := fmt.Sprintf("topic_permission:%s:%s", userID, fullTopic)
	s.cacheManager.Set(ctx, cacheKey, permission, time.Hour)
}

// clearTopicPermissionCache 清除Topic权限缓存
func (s *groupService) clearTopicPermissionCache(fullTopic string) {
	if s.cacheManager == nil {
		return
	}

	ctx := context.Background()
	// 这里需要清除所有用户对该Topic的权限缓存
	// 由于Redis不支持通配符删除，这里可以使用一个简单的实现
	// 在实际生产环境中，可以考虑使用Redis的SCAN命令或者维护一个权限用户列表
	pattern := fmt.Sprintf("topic_permission:*:%s", fullTopic)
	s.cacheManager.DeletePattern(ctx, pattern)
}

// clearUserTopicPermissionCache 清除用户Topic权限缓存
func (s *groupService) clearUserTopicPermissionCache(userID, fullTopic string) {
	if s.cacheManager == nil {
		return
	}

	ctx := context.Background()
	cacheKey := fmt.Sprintf("topic_permission:%s:%s", userID, fullTopic)
	s.cacheManager.Delete(ctx, cacheKey)
}

// generateGroupID 生成分组ID
func generateGroupID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")[:16]
}
