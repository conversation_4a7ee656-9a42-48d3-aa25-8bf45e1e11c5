package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/internal/utils"
	"beacon/cloud/pkg/database"

	"github.com/mochi-mqtt/server/v2/system"
)

// MonitorService 监控服务接口
type MonitorService interface {
	// 实时数据获取
	GetDashboardData(ctx context.Context, userID string) (*model.DashboardData, error)

	// 历史数据查询
	GetPerformanceHistory(ctx context.Context, userID string, query *model.MonitorQuery) ([]*model.PerformanceMetrics, error)

	// 配置管理
	SetMQTTManager(manager MQTTManagerInterface)
}

// MQTTManagerInterface MQTT管理器接口
type MQTTManagerInterface interface {
	GetServerInfo() *system.Info
	HealthCheck() error
}

// monitorService 监控服务实现
type monitorService struct {
	tdengineManager  *database.TDengineManager
	mqttManager      MQTTManagerInterface
	userService      UserService
	metricsCollector *utils.SystemMetricsCollector
}

// NewMonitorService 创建监控服务
func NewMonitorService(
	tdengineManager *database.TDengineManager,
	mqttManager MQTTManagerInterface,
	userService UserService,
) MonitorService {
	return &monitorService{
		tdengineManager:  tdengineManager,
		mqttManager:      mqttManager,
		userService:      userService,
		metricsCollector: utils.NewSystemMetricsCollector(),
	}
}

// SetMQTTManager 设置MQTT管理器
func (s *monitorService) SetMQTTManager(manager MQTTManagerInterface) {
	s.mqttManager = manager
}

// GetDashboardData 获取监控面板数据
func (s *monitorService) GetDashboardData(ctx context.Context, userID string) (*model.DashboardData, error) {
	// 检查用户权限 - 检查用户是否为管理员
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user.Role != model.RoleAdmin {
		return nil, fmt.Errorf("Permission denied")
	}

	// 获取系统指标
	systemMetrics, err := s.metricsCollector.GetSystemMetrics()
	if err != nil {
		log.Printf("获取系统指标失败: %v", err)
		// 使用默认值
		systemMetrics = &model.SystemMetricsDetail{
			CPU:     &model.CPUMetrics{Usage: 0},
			Memory:  &model.MemoryMetrics{Usage: 0},
			Disk:    &model.DiskMetrics{Usage: 0},
			Network: &model.NetworkMetrics{},
		}
	}

	return &model.DashboardData{
		Timestamp:     time.Now(),
		ServerInfo:    s.mqttManager.GetServerInfo(),
		SystemMetrics: systemMetrics,
	}, nil
}

// GetPerformanceHistory 获取性能历史数据
func (s *monitorService) GetPerformanceHistory(ctx context.Context, userID string, query *model.MonitorQuery) ([]*model.PerformanceMetrics, error) {
	// 权限检查 - 检查用户是否为管理员
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user.Role != model.RoleAdmin {
		return nil, fmt.Errorf("只有管理员可以查看系统性能历史")
	}

	// 构建数据库查询
	dbQuery := database.MonitorQuery{
		StartTime: query.StartTime,
		EndTime:   query.EndTime,
		Interval:  query.Interval,
		Limit:     query.Limit,
		Offset:    query.Offset,
	}

	// 查询性能指标
	metrics, err := s.tdengineManager.QueryPerformanceMetrics(ctx, dbQuery)
	if err != nil {
		return nil, fmt.Errorf("查询性能历史失败: %v", err)
	}

	// 转换为服务层模型
	result := make([]*model.PerformanceMetrics, 0, len(metrics))
	for _, metric := range metrics {
		result = append(result, &model.PerformanceMetrics{
			Timestamp:        metric.Timestamp,
			ClientsCount:     metric.ClientsCount,
			Subscriptions:    metric.Subscriptions,
			MessagesSent:     metric.MessagesSent,
			MessagesReceived: metric.MessagesReceived,
			BytesSent:        metric.BytesSent,
			BytesReceived:    metric.BytesReceived,
			Uptime:           metric.Uptime,
			MemoryUsage:      metric.MemoryUsage,
			NetworkInBytes:   metric.NetworkInBytes,
			NetworkOutBytes:  metric.NetworkOutBytes,
		})
	}

	return result, nil
}

// ===== 辅助方法 =====
