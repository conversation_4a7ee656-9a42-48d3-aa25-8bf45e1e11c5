package service

import (
	"context"
	"fmt"
	"time"

	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/pkg/cache"
	"beacon/cloud/pkg/database"
)

// SystemService 系统管理服务接口
type SystemService interface {
	// 系统日志管理
	CreateLog(ctx context.Context, log *model.SystemLog) error
	BatchCreateLogs(ctx context.Context, logs []*model.SystemLog) error
	GetLogs(ctx context.Context, userID string, filter *model.SystemLogFilter) ([]*model.SystemLogResponse, int64, error)
	GetLogStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error)
	CleanupLogs(ctx context.Context, userID string, before time.Time) (int64, error)
}

// systemService 系统管理服务实现
type systemService struct {
	logRepo       repository.SystemLogRepository
	userRepo      repository.UserRepository
	healthManager *database.HealthManager
	cacheManager  *cache.Manager
}

// NewSystemService 创建系统管理服务
func NewSystemService(
	logRepo repository.SystemLogRepository,
	userRepo repository.UserRepository,
	healthManager *database.HealthManager,
	cacheManager *cache.Manager,
) SystemService {
	return &systemService{
		logRepo:       logRepo,
		userRepo:      userRepo,
		healthManager: healthManager,
		cacheManager:  cacheManager,
	}
}

// CreateLog 创建系统日志
func (s *systemService) CreateLog(ctx context.Context, log *model.SystemLog) error {
	if log.Timestamp.IsZero() {
		log.Timestamp = time.Now()
	}

	return s.logRepo.Create(ctx, log)
}

// BatchCreateLogs 批量创建系统日志
func (s *systemService) BatchCreateLogs(ctx context.Context, logs []*model.SystemLog) error {
	for _, log := range logs {
		if log.Timestamp.IsZero() {
			log.Timestamp = time.Now()
		}
	}

	return s.logRepo.BatchCreate(ctx, logs)
}

// GetLogs 获取系统日志
func (s *systemService) GetLogs(ctx context.Context, userID string, filter *model.SystemLogFilter) ([]*model.SystemLogResponse, int64, error) {
	// 权限检查 - 检查用户是否为管理员
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 非管理员只能查看自己的日志
	if user.Role != model.RoleAdmin {
		filter.UserID = userID
	}

	// 查询日志
	logs, total, err := s.logRepo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("获取系统日志失败: %w", err)
	}

	// 构建响应（包含用户名）
	responses := make([]*model.SystemLogResponse, len(logs))
	for i, log := range logs {
		response := &model.SystemLogResponse{
			SystemLog: log,
		}

		// 获取用户邮箱
		if log.UserID != "" {
			if user, err := s.userRepo.GetByID(ctx, log.UserID); err == nil {
				response.UserName = user.Email
			}
		}

		responses[i] = response
	}

	return responses, total, nil
}

// GetLogStatistics 获取日志统计
func (s *systemService) GetLogStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	// 权限检查 - 检查用户是否为管理员
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user.Role != model.RoleAdmin {
		return nil, model.ErrPermissionDenied
	}

	return s.logRepo.GetStatistics(ctx, startTime, endTime)
}

// CleanupLogs 清理历史日志
func (s *systemService) CleanupLogs(ctx context.Context, userID string, before time.Time) (int64, error) {
	// 权限检查 - 检查用户是否为管理员
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return 0, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user.Role != model.RoleAdmin {
		return 0, model.ErrPermissionDenied
	}

	count, err := s.logRepo.DeleteBefore(ctx, before)
	if err != nil {
		return 0, fmt.Errorf("清理历史日志失败: %w", err)
	}

	// 记录日志
	s.logSystemAction(ctx, userID, model.LogActionDelete, fmt.Sprintf("清理 %s 之前的历史日志", before.Format("2006-01-02 15:04:05")), nil)

	return count, nil
}

// 辅助方法

// logSystemAction 记录系统操作日志
func (s *systemService) logSystemAction(ctx context.Context, userID, action, message string, details map[string]interface{}) {
	log := &model.SystemLog{
		Level:     model.LogLevelInfo,
		Module:    model.LogModuleSystem,
		Action:    action,
		Message:   message,
		UserID:    userID,
		Details:   details,
		Timestamp: time.Now(),
	}

	s.logRepo.Create(ctx, log)
}
