package service

import (
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/pkg/cache"
	"beacon/cloud/pkg/utils"
	"context"
	"fmt"
	"log"
	"strings"
	"time"
)

// UserService 用户服务接口
type UserService interface {
	RegisterUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error)
	LoginUser(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error)
	GetUserProfile(ctx context.Context, userID string) (*model.User, error)
	UpdateUserProfile(ctx context.Context, userID string, req *model.UpdateUserRequest) (*model.User, error)
	ChangePassword(ctx context.Context, userID string, req *model.ChangePasswordRequest) error
	ValidateUserCredentials(ctx context.Context, email, password string) (*model.User, error)
	GetUserByID(ctx context.Context, userID string) (*model.User, error)
	ListUsers(ctx context.Context, filter repository.UserFilter, pagination repository.Pagination) ([]*model.User, int64, error)
	DeleteUser(ctx context.Context, userID string) error
	UpdateUserStatus(ctx context.Context, userID string, status model.UserStatus) error
	AdminUpdateUser(ctx context.Context, userID string, req *model.AdminUpdateUserRequest) (*model.User, error)
	CheckEmailExists(ctx context.Context, email string) (bool, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	ResetPassword(ctx context.Context, userID string) (string, error)
	CacheUserRegistrationInfo(ctx context.Context, req *model.RegisterSendCodeRequest) error
	GetCachedUserRegistrationInfo(ctx context.Context, email string) (*model.CachedUserInfo, error)
	RegisterUserFromCache(ctx context.Context, email string) (*model.User, error)
}

// userService 用户服务实现
type userService struct {
	userRepo          repository.UserRepository
	authService       *AuthService
	passwordValidator *utils.PasswordValidator
	cacheManager      *cache.Manager
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repository.UserRepository,
	authService *AuthService,
	cacheManager *cache.Manager,
) UserService {
	return &userService{
		userRepo:          userRepo,
		authService:       authService,
		passwordValidator: utils.DefaultPasswordValidator(),
		cacheManager:      cacheManager,
	}
}

// RegisterUser 用户注册
func (s *userService) RegisterUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error) {
	// 验证输入
	if err := s.validateCreateUserRequest(req); err != nil {
		return nil, err
	}

	// 验证密码强度
	if err := s.passwordValidator.ValidatePassword(req.Password); err != nil {
		return nil, fmt.Errorf("密码验证失败: %w", err)
	}

	// 检查邮箱是否已存在
	exists, err := s.userRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if exists {
		return nil, model.ErrUserAlreadyExists
	}

	// 哈希密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建用户
	user := &model.User{
		Email:    req.Email,
		Password: hashedPassword,
		Role:     req.Role,
		Profile:  req.Profile,
	}

	// 设置默认角色
	if user.Role == "" {
		user.Role = model.RoleUser
	}

	// 创建用户
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

// LoginUser 用户登录
func (s *userService) LoginUser(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error) {
	// 验证用户凭据
	user, err := s.ValidateUserCredentials(ctx, req.Email, req.Password)
	if err != nil {
		return nil, err
	}

	// 检查用户状态
	if !user.IsActive() {
		switch user.Status {
		case model.StatusInactive:
			return nil, model.ErrUserInactive
		case model.StatusBanned:
			return nil, model.ErrUserBanned
		default:
			return nil, fmt.Errorf("用户状态异常: %s", user.Status)
		}
	}

	// 生成令牌
	tokenPair, err := s.authService.GenerateTokens(user.ID.Hex(), user.Email, string(user.Role))
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID.Hex()); err != nil {
		// 记录错误但不阻止登录
		fmt.Printf("更新最后登录时间失败: %v\n", err)
	}

	return &model.LoginResponse{
		User:         user.ToResponse(),
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
	}, nil
}

// GetUserProfile 获取用户资料
func (s *userService) GetUserProfile(ctx context.Context, userID string) (*model.User, error) {
	return s.userRepo.GetByID(ctx, userID)
}

// UpdateUserProfile 更新用户资料
func (s *userService) UpdateUserProfile(ctx context.Context, userID string, req *model.UpdateUserRequest) (*model.User, error) {
	// 获取现有用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 更新资料（不允许修改邮箱，因为邮箱是唯一登录标识）
	user.Profile = req.Profile

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("更新用户资料失败: %w", err)
	}

	return user, nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, userID string, req *model.ChangePasswordRequest) error {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := utils.VerifyPassword(user.Password, req.OldPassword); err != nil {
		return model.ErrPasswordMismatch
	}

	// 验证新密码强度
	if err := s.passwordValidator.ValidatePassword(req.NewPassword); err != nil {
		return fmt.Errorf("新密码验证失败: %w", err)
	}

	// 哈希新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码
	user.Password = hashedPassword
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	// 撤销用户的所有令牌
	if err := s.authService.RevokeAllUserTokens(user.ID.Hex()); err != nil {
		// 记录错误但不阻止密码修改
		fmt.Printf("撤销用户令牌失败: %v\n", err)
	}

	return nil
}

// ValidateUserCredentials 验证用户凭据
func (s *userService) ValidateUserCredentials(ctx context.Context, email, password string) (*model.User, error) {
	// 获取用户
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil, err
	}

	// 验证密码
	if err := utils.VerifyPassword(user.Password, password); err != nil {
		return nil, model.ErrPasswordMismatch
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(ctx context.Context, userID string) (*model.User, error) {
	return s.userRepo.GetByID(ctx, userID)
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(ctx context.Context, filter repository.UserFilter, pagination repository.Pagination) ([]*model.User, int64, error) {
	return s.userRepo.List(ctx, filter, pagination)
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(ctx context.Context, userID string) error {
	// 撤销用户的所有令牌
	if err := s.authService.RevokeAllUserTokens(userID); err != nil {
		fmt.Printf("撤销用户令牌失败: %v\n", err)
	}

	return s.userRepo.Delete(ctx, userID)
}

// UpdateUserStatus 更新用户状态
func (s *userService) UpdateUserStatus(ctx context.Context, userID string, status model.UserStatus) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	user.Status = status
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("更新用户状态失败: %w", err)
	}

	// 如果用户被禁用，撤销所有令牌
	if status == model.StatusBanned || status == model.StatusInactive {
		if err := s.authService.RevokeAllUserTokens(userID); err != nil {
			fmt.Printf("撤销用户令牌失败: %v\n", err)
		}
	}

	return nil
}

// AdminUpdateUser 管理员更新用户信息
func (s *userService) AdminUpdateUser(ctx context.Context, userID string, req *model.AdminUpdateUserRequest) (*model.User, error) {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 检查邮箱唯一性（如果要更新邮箱）
	if req.Email != "" && req.Email != user.Email {
		exists, err := s.userRepo.ExistsByEmail(ctx, req.Email)
		if err != nil {
			return nil, fmt.Errorf("检查邮箱失败: %w", err)
		}
		if exists {
			return nil, model.ErrUserAlreadyExists
		}
		user.Email = req.Email
	}

	// 更新角色
	if req.Role != "" {
		user.Role = req.Role
	}

	// 更新状态
	if req.Status != "" {
		user.Status = req.Status
	}

	// 更新资料
	if req.Profile.DisplayName != "" {
		user.Profile.DisplayName = req.Profile.DisplayName
	}
	if req.Profile.FirstName != "" {
		user.Profile.FirstName = req.Profile.FirstName
	}
	if req.Profile.LastName != "" {
		user.Profile.LastName = req.Profile.LastName
	}
	if req.Profile.Avatar != "" {
		user.Profile.Avatar = req.Profile.Avatar
	}

	// 更新用户
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	// 如果用户被禁用，撤销所有令牌
	if req.Status == model.StatusBanned || req.Status == model.StatusInactive {
		if err := s.authService.RevokeAllUserTokens(userID); err != nil {
			fmt.Printf("撤销用户令牌失败: %v\n", err)
		}
	}

	return user, nil
}

// validateCreateUserRequest 验证创建用户请求
func (s *userService) validateCreateUserRequest(req *model.CreateUserRequest) error {
	if req.Email == "" {
		return model.ErrInvalidEmail
	}

	req.Email = strings.TrimSpace(strings.ToLower(req.Email))

	if req.Password == "" {
		return model.ErrInvalidPassword
	}

	return nil
}

// CheckEmailExists 检查邮箱是否已存在
func (s *userService) CheckEmailExists(ctx context.Context, email string) (bool, error) {
	return s.userRepo.ExistsByEmail(ctx, email)
}

// GetUserByEmail 根据邮箱获取用户
func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

// ResetPassword 重置用户密码
func (s *userService) ResetPassword(ctx context.Context, userID string) (string, error) {
	// 生成新的随机密码
	newPassword, err := utils.GenerateRandomPassword(12)
	if err != nil {
		return "", fmt.Errorf("生成新密码失败: %w", err)
	}

	// 哈希新密码
	hashedPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		return "", fmt.Errorf("密码加密失败: %w", err)
	}

	// 获取用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return "", err
	}

	// 更新密码
	user.Password = hashedPassword
	user.BeforeUpdate()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return "", fmt.Errorf("更新密码失败: %w", err)
	}

	// 撤销用户的所有令牌
	if err := s.authService.RevokeAllUserTokens(userID); err != nil {
		// 记录错误但不阻止密码重置
		fmt.Printf("撤销用户令牌失败: %v\n", err)
	}

	return newPassword, nil
}

// CacheUserRegistrationInfo 缓存用户注册信息
func (s *userService) CacheUserRegistrationInfo(ctx context.Context, req *model.RegisterSendCodeRequest) error {
	// 验证输入
	if req.Email == "" {
		return model.ErrInvalidEmail
	}

	req.Email = strings.TrimSpace(strings.ToLower(req.Email))

	if req.Password == "" {
		return model.ErrInvalidPassword
	}

	// 验证密码强度
	if err := s.passwordValidator.ValidatePassword(req.Password); err != nil {
		return fmt.Errorf("密码验证失败: %w", err)
	}

	// 检查邮箱是否已存在
	exists, err := s.userRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return fmt.Errorf("检查邮箱失败: %w", err)
	}
	if exists {
		return model.ErrUserAlreadyExists
	}

	// 哈希密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建缓存信息
	cachedInfo := &model.CachedUserInfo{
		Email:          req.Email,
		HashedPassword: hashedPassword,
		Role:           req.Role,
		Profile:        req.Profile,
		CreatedAt:      time.Now(),
	}

	// 设置默认角色
	if cachedInfo.Role == "" {
		cachedInfo.Role = model.RoleUser
	}

	// 缓存用户信息，10分钟过期
	cacheKey := fmt.Sprintf("user:register:%s", req.Email)
	if err := s.cacheManager.Set(ctx, cacheKey, cachedInfo, 10*time.Minute); err != nil {
		return fmt.Errorf("缓存用户信息失败: %w", err)
	}

	return nil
}

// GetCachedUserRegistrationInfo 获取缓存的用户注册信息
func (s *userService) GetCachedUserRegistrationInfo(ctx context.Context, email string) (*model.CachedUserInfo, error) {
	if email == "" {
		return nil, model.ErrInvalidEmail
	}

	email = strings.TrimSpace(strings.ToLower(email))
	cacheKey := fmt.Sprintf("user:register:%s", email)

	var cachedInfo model.CachedUserInfo
	if err := s.cacheManager.Get(ctx, cacheKey, &cachedInfo); err != nil {
		return nil, fmt.Errorf("获取缓存的用户信息失败: %w", err)
	}

	return &cachedInfo, nil
}

// RegisterUserFromCache 从缓存中获取用户信息并注册
func (s *userService) RegisterUserFromCache(ctx context.Context, email string) (*model.User, error) {
	// 获取缓存的用户信息
	cachedInfo, err := s.GetCachedUserRegistrationInfo(ctx, email)
	if err != nil {
		return nil, err
	}

	// 再次检查邮箱是否已存在（防止并发注册）
	exists, err := s.userRepo.ExistsByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if exists {
		return nil, model.ErrUserAlreadyExists
	}

	// 创建用户
	user := &model.User{
		Email:    cachedInfo.Email,
		Password: cachedInfo.HashedPassword,
		Role:     cachedInfo.Role,
		Profile:  cachedInfo.Profile,
	}

	// 创建用户
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 清理缓存
	cacheKey := fmt.Sprintf("user:register:%s", email)
	if err := s.cacheManager.Delete(ctx, cacheKey); err != nil {
		// 记录错误但不影响注册流程
		log.Printf("清理用户注册缓存失败: %v", err)
	}

	return user, nil
}
