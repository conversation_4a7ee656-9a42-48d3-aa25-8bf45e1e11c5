package utils

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"
	"time"

	"beacon/cloud/internal/model"
)

// SystemMetricsCollector 系统指标收集器
type SystemMetricsCollector struct {
	lastCPUStats *cpuStats
}

// cpuStats CPU统计信息
type cpuStats struct {
	user   uint64
	nice   uint64
	system uint64
	idle   uint64
	iowait uint64
	irq    uint64
	softirq uint64
	steal  uint64
	guest  uint64
	guestNice uint64
	timestamp time.Time
}

// NewSystemMetricsCollector 创建系统指标收集器
func NewSystemMetricsCollector() *SystemMetricsCollector {
	return &SystemMetricsCollector{}
}

// GetSystemMetrics 获取完整的系统指标
func (c *SystemMetricsCollector) GetSystemMetrics() (*model.SystemMetricsDetail, error) {
	cpuMetrics, err := c.GetCPUMetrics()
	if err != nil {
		return nil, fmt.Errorf("获取CPU指标失败: %v", err)
	}

	memoryMetrics, err := c.GetMemoryMetrics()
	if err != nil {
		return nil, fmt.Errorf("获取内存指标失败: %v", err)
	}

	diskMetrics, err := c.GetDiskMetrics("/")
	if err != nil {
		return nil, fmt.Errorf("获取磁盘指标失败: %v", err)
	}

	networkMetrics, err := c.GetNetworkMetrics()
	if err != nil {
		return nil, fmt.Errorf("获取网络指标失败: %v", err)
	}

	return &model.SystemMetricsDetail{
		CPU:     cpuMetrics,
		Memory:  memoryMetrics,
		Disk:    diskMetrics,
		Network: networkMetrics,
	}, nil
}

// GetCPUMetrics 获取CPU指标
func (c *SystemMetricsCollector) GetCPUMetrics() (*model.CPUMetrics, error) {
	// 读取当前CPU统计
	currentStats, err := c.readCPUStats()
	if err != nil {
		return nil, err
	}

	var usage float64
	if c.lastCPUStats != nil {
		usage = c.calculateCPUUsage(c.lastCPUStats, currentStats)
	}

	// 更新最后的CPU统计
	c.lastCPUStats = currentStats

	// 读取负载平均值
	loadAvg1, loadAvg5, loadAvg15, err := c.readLoadAverage()
	if err != nil {
		return nil, err
	}

	return &model.CPUMetrics{
		Usage:     usage,
		LoadAvg1:  loadAvg1,
		LoadAvg5:  loadAvg5,
		LoadAvg15: loadAvg15,
	}, nil
}

// GetMemoryMetrics 获取内存指标
func (c *SystemMetricsCollector) GetMemoryMetrics() (*model.MemoryMetrics, error) {
	file, err := os.Open("/proc/meminfo")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var total, available, free, buffers, cached uint64
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) < 2 {
			continue
		}

		key := strings.TrimSuffix(fields[0], ":")
		value, err := strconv.ParseUint(fields[1], 10, 64)
		if err != nil {
			continue
		}
		
		// 转换为字节 (meminfo中的值是KB)
		value *= 1024

		switch key {
		case "MemTotal":
			total = value
		case "MemAvailable":
			available = value
		case "MemFree":
			free = value
		case "Buffers":
			buffers = value
		case "Cached":
			cached = value
		}
	}

	// 如果没有MemAvailable，计算可用内存
	if available == 0 {
		available = free + buffers + cached
	}

	used := total - available
	var usage float64
	if total > 0 {
		usage = float64(used) / float64(total) * 100
	}

	return &model.MemoryMetrics{
		Total:     total,
		Used:      used,
		Available: available,
		Usage:     usage,
	}, nil
}

// GetDiskMetrics 获取磁盘指标
func (c *SystemMetricsCollector) GetDiskMetrics(path string) (*model.DiskMetrics, error) {
	var stat syscall.Statfs_t
	err := syscall.Statfs(path, &stat)
	if err != nil {
		return nil, err
	}

	total := uint64(stat.Blocks) * uint64(stat.Bsize)
	available := uint64(stat.Bavail) * uint64(stat.Bsize)
	used := total - available
	
	var usage float64
	if total > 0 {
		usage = float64(used) / float64(total) * 100
	}

	return &model.DiskMetrics{
		Total:     total,
		Used:      used,
		Available: available,
		Usage:     usage,
	}, nil
}

// GetNetworkMetrics 获取网络指标
func (c *SystemMetricsCollector) GetNetworkMetrics() (*model.NetworkMetrics, error) {
	file, err := os.Open("/proc/net/dev")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var totalBytesIn, totalBytesOut, totalPacketsIn, totalPacketsOut uint64
	scanner := bufio.NewScanner(file)
	
	// 跳过前两行标题
	scanner.Scan()
	scanner.Scan()

	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) < 17 {
			continue
		}

		// 跳过回环接口
		if strings.HasPrefix(fields[0], "lo:") {
			continue
		}

		// 接收字节数 (字段1)
		if bytesIn, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
			totalBytesIn += bytesIn
		}

		// 接收包数 (字段2)
		if packetsIn, err := strconv.ParseUint(fields[2], 10, 64); err == nil {
			totalPacketsIn += packetsIn
		}

		// 发送字节数 (字段9)
		if bytesOut, err := strconv.ParseUint(fields[9], 10, 64); err == nil {
			totalBytesOut += bytesOut
		}

		// 发送包数 (字段10)
		if packetsOut, err := strconv.ParseUint(fields[10], 10, 64); err == nil {
			totalPacketsOut += packetsOut
		}
	}

	return &model.NetworkMetrics{
		BytesIn:    totalBytesIn,
		BytesOut:   totalBytesOut,
		PacketsIn:  totalPacketsIn,
		PacketsOut: totalPacketsOut,
	}, nil
}

// readCPUStats 读取CPU统计信息
func (c *SystemMetricsCollector) readCPUStats() (*cpuStats, error) {
	file, err := os.Open("/proc/stat")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	if !scanner.Scan() {
		return nil, fmt.Errorf("无法读取/proc/stat")
	}

	line := scanner.Text()
	fields := strings.Fields(line)
	if len(fields) < 11 || fields[0] != "cpu" {
		return nil, fmt.Errorf("无效的CPU统计格式")
	}

	stats := &cpuStats{timestamp: time.Now()}
	values := []*uint64{
		&stats.user, &stats.nice, &stats.system, &stats.idle,
		&stats.iowait, &stats.irq, &stats.softirq, &stats.steal,
		&stats.guest, &stats.guestNice,
	}

	for i, value := range values {
		if i+1 < len(fields) {
			if val, err := strconv.ParseUint(fields[i+1], 10, 64); err == nil {
				*value = val
			}
		}
	}

	return stats, nil
}

// calculateCPUUsage 计算CPU使用率
func (c *SystemMetricsCollector) calculateCPUUsage(prev, curr *cpuStats) float64 {
	prevIdle := prev.idle + prev.iowait
	currIdle := curr.idle + curr.iowait

	prevNonIdle := prev.user + prev.nice + prev.system + prev.irq + prev.softirq + prev.steal
	currNonIdle := curr.user + curr.nice + curr.system + curr.irq + curr.softirq + curr.steal

	prevTotal := prevIdle + prevNonIdle
	currTotal := currIdle + currNonIdle

	totalDiff := currTotal - prevTotal
	idleDiff := currIdle - prevIdle

	if totalDiff == 0 {
		return 0
	}

	return float64(totalDiff-idleDiff) / float64(totalDiff) * 100
}

// readLoadAverage 读取负载平均值
func (c *SystemMetricsCollector) readLoadAverage() (float64, float64, float64, error) {
	file, err := os.Open("/proc/loadavg")
	if err != nil {
		return 0, 0, 0, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	if !scanner.Scan() {
		return 0, 0, 0, fmt.Errorf("无法读取/proc/loadavg")
	}

	fields := strings.Fields(scanner.Text())
	if len(fields) < 3 {
		return 0, 0, 0, fmt.Errorf("无效的负载平均值格式")
	}

	loadAvg1, err1 := strconv.ParseFloat(fields[0], 64)
	loadAvg5, err2 := strconv.ParseFloat(fields[1], 64)
	loadAvg15, err3 := strconv.ParseFloat(fields[2], 64)

	if err1 != nil || err2 != nil || err3 != nil {
		return 0, 0, 0, fmt.Errorf("解析负载平均值失败")
	}

	return loadAvg1, loadAvg5, loadAvg15, nil
}
