package auth

import (
	"log"
	"path/filepath"
	"sync"

	"github.com/casbin/casbin/v2"
)

var (
	enforcer *casbin.Enforcer
	once     sync.Once
	mutex    sync.RWMutex // 添加读写锁保护并发访问
)

// InitCasbin initializes the Casbin enforcer
func InitCasbin() (*casbin.Enforcer, error) {
	var err error
	once.Do(func() {
		modelPath := filepath.Join("configs", "rbac_model.conf")
		policyPath := filepath.Join("configs", "rbac_policy.csv")
		enforcer, err = casbin.NewEnforcer(modelPath, policyPath)
		if err != nil {
			log.Printf("Failed to initialize Casbin: %v", err)
			return
		}

		// Load policies from DB or file
		err = enforcer.LoadPolicy()
		if err != nil {
			log.Printf("Failed to load Casbin policy: %v", err)
			return
		}

		log.Printf("Casbin initialized successfully with model: %s, policy: %s", modelPath, policyPath)
	})
	return enforcer, err
}

// ReInitCasbin re-initializes the Casbin enforcer (useful after model changes)
func ReInitCasbin() (*casbin.Enforcer, error) {
	mutex.Lock()
	defer mutex.Unlock()

	modelPath := filepath.Join("configs", "rbac_model.conf")
	policyPath := filepath.Join("configs", "rbac_policy.csv")

	newEnforcer, err := casbin.NewEnforcer(modelPath, policyPath)
	if err != nil {
		log.Printf("Failed to re-initialize Casbin: %v", err)
		return nil, err
	}

	// Load policies from DB or file
	err = newEnforcer.LoadPolicy()
	if err != nil {
		log.Printf("Failed to load Casbin policy: %v", err)
		return nil, err
	}

	enforcer = newEnforcer
	log.Printf("Casbin re-initialized successfully")
	return enforcer, nil
}

// ReloadPolicy reloads the Casbin policy from the storage
func ReloadPolicy() error {
	mutex.Lock()
	defer mutex.Unlock()

	if enforcer == nil {
		_, err := InitCasbin()
		return err
	}

	err := enforcer.LoadPolicy()
	if err != nil {
		log.Printf("Failed to reload Casbin policy: %v", err)
		return err
	}

	log.Printf("Casbin policy reloaded successfully")
	return nil
}

// GetEnforcer returns the current Casbin enforcer
func GetEnforcer() *casbin.Enforcer {
	mutex.RLock()
	defer mutex.RUnlock()
	return enforcer
}
