package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// Manager 通用缓存管理器
type Manager struct {
	client *redis.Client
}

// NewManager 创建缓存管理器
func NewManager(client *redis.Client) *Manager {
	return &Manager{
		client: client,
	}
}

// Set 设置缓存
func (m *Manager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %w", err)
	}
	
	return m.client.Set(ctx, key, data, expiration).Err()
}

// Get 获取缓存
func (m *Manager) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := m.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("缓存未命中")
		}
		return fmt.Erro<PERSON>("获取缓存失败: %w", err)
	}
	
	return json.Unmarshal([]byte(data), dest)
}

// Delete 删除缓存
func (m *Manager) Delete(ctx context.Context, key string) error {
	return m.client.Del(ctx, key).Err()
}

// Exists 检查缓存是否存在
func (m *Manager) Exists(ctx context.Context, key string) (bool, error) {
	count, err := m.client.Exists(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("检查缓存存在性失败: %w", err)
	}
	return count > 0, nil
}

// SetString 设置字符串缓存
func (m *Manager) SetString(ctx context.Context, key, value string, expiration time.Duration) error {
	return m.client.Set(ctx, key, value, expiration).Err()
}

// GetString 获取字符串缓存
func (m *Manager) GetString(ctx context.Context, key string) (string, error) {
	return m.client.Get(ctx, key).Result()
}

// SetInt 设置整数缓存
func (m *Manager) SetInt(ctx context.Context, key string, value int64, expiration time.Duration) error {
	return m.client.Set(ctx, key, value, expiration).Err()
}

// GetInt 获取整数缓存
func (m *Manager) GetInt(ctx context.Context, key string) (int64, error) {
	return m.client.Get(ctx, key).Int64()
}

// Increment 递增缓存值
func (m *Manager) Increment(ctx context.Context, key string) (int64, error) {
	return m.client.Incr(ctx, key).Result()
}

// IncrementBy 按指定值递增缓存
func (m *Manager) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return m.client.IncrBy(ctx, key, value).Result()
}

// Expire 设置缓存过期时间
func (m *Manager) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return m.client.Expire(ctx, key, expiration).Err()
}

// TTL 获取缓存剩余时间
func (m *Manager) TTL(ctx context.Context, key string) (time.Duration, error) {
	return m.client.TTL(ctx, key).Result()
}

// Keys 获取匹配模式的所有键
func (m *Manager) Keys(ctx context.Context, pattern string) ([]string, error) {
	return m.client.Keys(ctx, pattern).Result()
}

// DeletePattern 删除匹配模式的所有缓存
func (m *Manager) DeletePattern(ctx context.Context, pattern string) error {
	keys, err := m.Keys(ctx, pattern)
	if err != nil {
		return fmt.Errorf("获取匹配键失败: %w", err)
	}
	
	if len(keys) > 0 {
		return m.client.Del(ctx, keys...).Err()
	}
	
	return nil
}

// FlushDB 清空当前数据库的所有缓存
func (m *Manager) FlushDB(ctx context.Context) error {
	return m.client.FlushDB(ctx).Err()
}

// Ping 检查Redis连接
func (m *Manager) Ping(ctx context.Context) error {
	return m.client.Ping(ctx).Err()
}

// GetClient 获取Redis客户端
func (m *Manager) GetClient() *redis.Client {
	return m.client
}

// GetOrSet 获取缓存，如果不存在则设置
func (m *Manager) GetOrSet(ctx context.Context, key string, fetcher func() (interface{}, error), expiration time.Duration, dest interface{}) error {
	// 尝试获取缓存
	err := m.Get(ctx, key, dest)
	if err == nil {
		return nil // 缓存命中
	}
	
	// 缓存未命中，调用fetcher获取数据
	value, err := fetcher()
	if err != nil {
		return fmt.Errorf("获取数据失败: %w", err)
	}
	
	// 设置缓存
	if err := m.Set(ctx, key, value, expiration); err != nil {
		// 缓存设置失败不影响主流程，只记录错误
		fmt.Printf("设置缓存失败: %v\n", err)
	}
	
	// 将获取的数据赋值给dest
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %w", err)
	}
	
	return json.Unmarshal(data, dest)
}

// SetHash 设置哈希缓存
func (m *Manager) SetHash(ctx context.Context, key string, fields map[string]interface{}) error {
	return m.client.HMSet(ctx, key, fields).Err()
}

// GetHash 获取哈希缓存
func (m *Manager) GetHash(ctx context.Context, key string) (map[string]string, error) {
	return m.client.HGetAll(ctx, key).Result()
}

// SetHashField 设置哈希字段
func (m *Manager) SetHashField(ctx context.Context, key, field string, value interface{}) error {
	return m.client.HSet(ctx, key, field, value).Err()
}

// GetHashField 获取哈希字段
func (m *Manager) GetHashField(ctx context.Context, key, field string) (string, error) {
	return m.client.HGet(ctx, key, field).Result()
}

// DeleteHashField 删除哈希字段
func (m *Manager) DeleteHashField(ctx context.Context, key string, fields ...string) error {
	return m.client.HDel(ctx, key, fields...).Err()
}

// SetList 设置列表缓存
func (m *Manager) SetList(ctx context.Context, key string, values ...interface{}) error {
	return m.client.LPush(ctx, key, values...).Err()
}

// GetList 获取列表缓存
func (m *Manager) GetList(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return m.client.LRange(ctx, key, start, stop).Result()
}

// GetListLength 获取列表长度
func (m *Manager) GetListLength(ctx context.Context, key string) (int64, error) {
	return m.client.LLen(ctx, key).Result()
}

// SetSet 设置集合缓存
func (m *Manager) SetSet(ctx context.Context, key string, members ...interface{}) error {
	return m.client.SAdd(ctx, key, members...).Err()
}

// GetSet 获取集合缓存
func (m *Manager) GetSet(ctx context.Context, key string) ([]string, error) {
	return m.client.SMembers(ctx, key).Result()
}

// IsSetMember 检查是否为集合成员
func (m *Manager) IsSetMember(ctx context.Context, key string, member interface{}) (bool, error) {
	return m.client.SIsMember(ctx, key, member).Result()
}

// RemoveSetMember 移除集合成员
func (m *Manager) RemoveSetMember(ctx context.Context, key string, members ...interface{}) error {
	return m.client.SRem(ctx, key, members...).Err()
}

// GetSetSize 获取集合大小
func (m *Manager) GetSetSize(ctx context.Context, key string) (int64, error) {
	return m.client.SCard(ctx, key).Result()
}

// Lock 分布式锁
func (m *Manager) Lock(ctx context.Context, key string, expiration time.Duration) (bool, error) {
	return m.client.SetNX(ctx, key, "locked", expiration).Result()
}

// Unlock 释放分布式锁
func (m *Manager) Unlock(ctx context.Context, key string) error {
	return m.client.Del(ctx, key).Err()
}

// GetStats 获取缓存统计信息
func (m *Manager) GetStats(ctx context.Context) (map[string]interface{}, error) {
	info, err := m.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis统计信息失败: %w", err)
	}
	
	dbSize, err := m.client.DBSize(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("获取数据库大小失败: %w", err)
	}
	
	stats := map[string]interface{}{
		"info":    info,
		"db_size": dbSize,
	}
	
	return stats, nil
}
