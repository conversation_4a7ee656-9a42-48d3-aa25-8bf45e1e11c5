package database

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// BatchInserter 批量插入器
type BatchInserter struct {
	tdengine      *TDengineManager
	bufferSize    int
	flushInterval time.Duration

	// 各种事件的缓冲区
	performanceData []PerformanceMetricsData

	// 互斥锁保护缓冲区
	mu sync.Mutex

	// 定时器和停止信号
	flushTimer *time.Timer
	stopChan   chan struct{}
	wg         sync.WaitGroup
}

// NewBatchInserter 创建批量插入器
func NewBatchInserter(tdengine *TDengineManager, bufferSize int, flushInterval time.Duration) *BatchInserter {
	bi := &BatchInserter{
		tdengine:        tdengine,
		bufferSize:      bufferSize,
		flushInterval:   flushInterval,
		performanceData: make([]PerformanceMetricsData, 0, bufferSize),
		stopChan:        make(chan struct{}),
	}

	// 启动定时刷新goroutine
	bi.wg.Add(1)
	go bi.flushRoutine()

	return bi
}

// AddPerformanceMetrics 添加性能指标到缓冲区
func (bi *BatchInserter) AddPerformanceMetrics(data PerformanceMetricsData) {
	bi.mu.Lock()
	defer bi.mu.Unlock()

	bi.performanceData = append(bi.performanceData, data)

	if len(bi.performanceData) >= bi.bufferSize {
		go bi.flushPerformanceMetrics()
	}
}

// flushRoutine 定时刷新routine
func (bi *BatchInserter) flushRoutine() {
	defer bi.wg.Done()

	ticker := time.NewTicker(bi.flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			bi.FlushAll()
		case <-bi.stopChan:
			// 最后一次刷新
			bi.FlushAll()
			return
		}
	}
}

// flushPerformanceMetrics 刷新性能指标
func (bi *BatchInserter) flushPerformanceMetrics() {
	bi.mu.Lock()
	if len(bi.performanceData) == 0 {
		bi.mu.Unlock()
		return
	}

	data := make([]PerformanceMetricsData, len(bi.performanceData))
	copy(data, bi.performanceData)
	bi.performanceData = bi.performanceData[:0]
	bi.mu.Unlock()

	if err := bi.batchInsertPerformanceMetrics(context.Background(), data); err != nil {
		fmt.Printf("批量插入性能指标失败: %v\n", err)
	}
}

// FlushAll 刷新所有缓冲区
func (bi *BatchInserter) FlushAll() {
	bi.flushPerformanceMetrics()
}

// Stop 停止批量插入器
func (bi *BatchInserter) Stop() {
	close(bi.stopChan)
	bi.wg.Wait()
}

// 批量插入实现方法

// batchInsertPerformanceMetrics 批量插入性能指标
func (bi *BatchInserter) batchInsertPerformanceMetrics(ctx context.Context, data []PerformanceMetricsData) error {
	if len(data) == 0 {
		return nil
	}

	sql := fmt.Sprintf("INSERT INTO %s.mqtt_performance_metrics VALUES ", bi.tdengine.config.DBName)

	values := make([]string, len(data))
	for i, metrics := range data {
		values[i] = fmt.Sprintf("('%s', %d, %d, %d, %d, %d, %d, %f, %d, %d, %d)",
			metrics.Timestamp.Format("2006-01-02 15:04:05.000"),
			metrics.ClientsCount,
			metrics.Subscriptions,
			metrics.MessagesSent,
			metrics.MessagesReceived,
			metrics.BytesSent,
			metrics.BytesReceived,
			metrics.Uptime,
			metrics.MemoryUsage,
			metrics.NetworkInBytes,
			metrics.NetworkOutBytes,
		)
	}

	sql += fmt.Sprintf("(%s)", values[0])
	for i := 1; i < len(values); i++ {
		sql += fmt.Sprintf(" (%s)", values[i])
	}

	_, err := bi.tdengine.db.ExecContext(ctx, sql)
	return err
}
