package database

import (
	"context"
	"sync"

	"beacon/cloud/internal/config"
)

var (
	// 全局数据库管理器实例
	globalManager *DatabaseManager
	once          sync.Once
)

// InitializeGlobal 初始化全局数据库管理器
func InitializeGlobal(ctx context.Context, cfg *config.Config) error {
	var err error
	once.Do(func() {
		globalManager = NewDatabaseManager(cfg)
		err = globalManager.Initialize(ctx)
	})
	return err
}

// GetGlobalManager 获取全局数据库管理器
func GetGlobalManager() *DatabaseManager {
	return globalManager
}

// GetMongoDB 获取全局MongoDB管理器
func GetMongoDB() *MongoDBManager {
	if globalManager == nil {
		return nil
	}
	return globalManager.GetMongoDB()
}

// GetRedis 获取全局Redis管理器
func GetRedis() *RedisManager {
	if globalManager == nil {
		return nil
	}
	return globalManager.GetRedis()
}

// GetTDengine 获取全局TDengine管理器
func GetTDengine() *TDengineManager {
	if globalManager == nil {
		return nil
	}
	return globalManager.GetTDengine()
}

// CloseGlobal 关闭全局数据库连接
func CloseGlobal(ctx context.Context) error {
	if globalManager != nil {
		return globalManager.Close(ctx)
	}
	return nil
}

// IsGlobalHealthy 检查全局数据库是否健康
func IsGlobalHealthy() bool {
	if globalManager == nil {
		return false
	}
	return globalManager.IsHealthy()
}

// GetGlobalHealthSummary 获取全局健康状态摘要
func GetGlobalHealthSummary() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"initialized": false,
			"error":       "数据库管理器未初始化",
		}
	}
	return globalManager.GetHealthSummary()
}
