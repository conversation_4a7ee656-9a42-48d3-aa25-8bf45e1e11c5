package database

import (
	"context"
	"sync"
	"time"
)

// HealthStatus 健康状态枚举
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// DatabaseHealth 数据库健康状态
type DatabaseHealth struct {
	Name         string                 `json:"name"`
	Status       HealthStatus           `json:"status"`
	Message      string                 `json:"message"`
	ResponseTime time.Duration          `json:"response_time"`
	LastCheck    time.Time              `json:"last_check"`
	Details      map[string]interface{} `json:"details,omitempty"`
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	HealthCheck(ctx context.Context) error
	IsConnected(ctx context.Context) bool
}

// HealthManager 健康检查管理器
type HealthManager struct {
	checkers map[string]HealthChecker
	results  map[string]*DatabaseHealth
	mutex    sync.RWMutex
	interval time.Duration
	timeout  time.Duration
	stopCh   chan struct{}
	running  bool
}

// NewHealthManager 创建健康检查管理器
func NewHealthManager() *HealthManager {
	return &HealthManager{
		checkers: make(map[string]HealthChecker),
		results:  make(map[string]*DatabaseHealth),
		interval: 30 * time.Second, // 默认30秒检查一次
		timeout:  10 * time.Second, // 默认10秒超时
		stopCh:   make(chan struct{}),
	}
}

// RegisterChecker 注册健康检查器
func (h *HealthManager) RegisterChecker(name string, checker HealthChecker) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.checkers[name] = checker
	h.results[name] = &DatabaseHealth{
		Name:      name,
		Status:    HealthStatusUnknown,
		Message:   "未检查",
		LastCheck: time.Time{},
	}
}

// CheckSingle 检查单个数据库健康状态
func (h *HealthManager) CheckSingle(ctx context.Context, name string) *DatabaseHealth {
	h.mutex.RLock()
	checker, exists := h.checkers[name]
	h.mutex.RUnlock()

	if !exists {
		return &DatabaseHealth{
			Name:      name,
			Status:    HealthStatusUnknown,
			Message:   "检查器不存在",
			LastCheck: time.Now(),
		}
	}

	h.checkSingle(ctx, name, checker)

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	result := h.results[name]
	return &DatabaseHealth{
		Name:         result.Name,
		Status:       result.Status,
		Message:      result.Message,
		ResponseTime: result.ResponseTime,
		LastCheck:    result.LastCheck,
		Details:      copyMap(result.Details),
	}
}

// CheckAll 检查所有数据库健康状态
func (h *HealthManager) CheckAll(ctx context.Context) map[string]*DatabaseHealth {
	h.mutex.RLock()
	checkers := make(map[string]HealthChecker)
	for name, checker := range h.checkers {
		checkers[name] = checker
	}
	h.mutex.RUnlock()

	var wg sync.WaitGroup
	for name, checker := range checkers {
		wg.Add(1)
		go func(n string, c HealthChecker) {
			defer wg.Done()
			h.checkSingle(ctx, n, c)
		}(name, checker)
	}

	wg.Wait()

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	results := make(map[string]*DatabaseHealth)
	for name, result := range h.results {
		// 深拷贝结果
		results[name] = &DatabaseHealth{
			Name:         result.Name,
			Status:       result.Status,
			Message:      result.Message,
			ResponseTime: result.ResponseTime,
			LastCheck:    result.LastCheck,
			Details:      copyMap(result.Details),
		}
	}

	return results
}

// IsHealthy 检查所有数据库是否健康
func (h *HealthManager) IsHealthy() bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, result := range h.results {
		if result.Status != HealthStatusHealthy {
			return false
		}
	}

	return true
}

// GetSummary 获取健康状态摘要
func (h *HealthManager) GetSummary() map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	summary := map[string]interface{}{
		"total":     len(h.results),
		"healthy":   0,
		"unhealthy": 0,
		"unknown":   0,
	}

	for _, result := range h.results {
		switch result.Status {
		case HealthStatusHealthy:
			summary["healthy"] = summary["healthy"].(int) + 1
		case HealthStatusUnhealthy:
			summary["unhealthy"] = summary["unhealthy"].(int) + 1
		case HealthStatusUnknown:
			summary["unknown"] = summary["unknown"].(int) + 1
		}
	}

	summary["overall_healthy"] = summary["unhealthy"].(int) == 0 && summary["unknown"].(int) == 0

	return summary
}

// Start 启动定期健康检查
func (h *HealthManager) Start() {
	h.mutex.Lock()
	if h.running {
		h.mutex.Unlock()
		return
	}
	h.running = true
	h.mutex.Unlock()

	go h.runPeriodicCheck()
}

// Stop 停止定期健康检查
func (h *HealthManager) Stop() {
	h.mutex.Lock()
	if !h.running {
		h.mutex.Unlock()
		return
	}
	h.running = false
	h.mutex.Unlock()

	close(h.stopCh)
}

// SetInterval 设置检查间隔
func (h *HealthManager) SetInterval(interval time.Duration) {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.interval = interval
}

// SetTimeout 设置检查超时
func (h *HealthManager) SetTimeout(timeout time.Duration) {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.timeout = timeout
}

// runPeriodicCheck 运行定期检查
func (h *HealthManager) runPeriodicCheck() {
	ticker := time.NewTicker(h.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), h.timeout)
			h.CheckAll(ctx)
			cancel()
		case <-h.stopCh:
			return
		}
	}
}

// checkSingle 检查单个数据库
func (h *HealthManager) checkSingle(ctx context.Context, name string, checker HealthChecker) {
	start := time.Now()

	// 创建带超时的上下文
	checkCtx, cancel := context.WithTimeout(ctx, h.timeout)
	defer cancel()

	var status HealthStatus
	var message string
	var details map[string]interface{}

	// 执行健康检查
	err := checker.HealthCheck(checkCtx)
	responseTime := time.Since(start)

	if err != nil {
		status = HealthStatusUnhealthy
		message = err.Error()
	} else {
		status = HealthStatusHealthy
		message = "连接正常"

		// 尝试获取额外的详细信息
		if connected := checker.IsConnected(checkCtx); !connected {
			status = HealthStatusUnhealthy
			message = "连接已断开"
		}
	}

	// 更新结果
	h.mutex.Lock()
	h.results[name] = &DatabaseHealth{
		Name:         name,
		Status:       status,
		Message:      message,
		ResponseTime: responseTime,
		LastCheck:    time.Now(),
		Details:      details,
	}
	h.mutex.Unlock()
}

// copyMap 深拷贝map
func copyMap(original map[string]interface{}) map[string]interface{} {
	if original == nil {
		return nil
	}

	copied := make(map[string]interface{})
	for key, value := range original {
		copied[key] = value
	}

	return copied
}
