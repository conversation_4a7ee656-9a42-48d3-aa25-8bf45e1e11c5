package database

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"beacon/cloud/internal/config"
)

// DatabaseManager 数据库统一管理器
type DatabaseManager struct {
	mongodb       *MongoDBManager
	redis         *RedisManager
	tdengine      *TDengineManager
	healthManager *HealthManager
	config        *config.Config
	mutex         sync.RWMutex
	initialized   bool
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(cfg *config.Config) *DatabaseManager {
	return &DatabaseManager{
		config:        cfg,
		healthManager: NewHealthManager(),
	}
}

// Initialize 初始化所有数据库连接
func (dm *DatabaseManager) Initialize(ctx context.Context) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	if dm.initialized {
		return fmt.Errorf("数据库管理器已经初始化")
	}

	// 初始化MongoDB
	if err := dm.initMongoDB(ctx); err != nil {
		return fmt.Errorf("初始化MongoDB失败: %v", err)
	}

	// 初始化Redis
	if err := dm.initRedis(ctx); err != nil {
		return fmt.Errorf("初始化Redis失败: %v", err)
	}

	// 初始化TDengine
	if err := dm.initTDengine(ctx); err != nil {
		return fmt.Errorf("初始化TDengine失败: %v", err)
	}

	// 注册健康检查器
	dm.registerHealthCheckers()

	// 启动健康检查
	dm.healthManager.Start()

	dm.initialized = true
	return nil
}

// Close 关闭所有数据库连接
func (dm *DatabaseManager) Close(ctx context.Context) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	if !dm.initialized {
		return nil
	}

	// 停止健康检查
	dm.healthManager.Stop()

	var errors []error

	// 关闭MongoDB连接
	if dm.mongodb != nil {
		if err := dm.mongodb.Disconnect(ctx); err != nil {
			errors = append(errors, fmt.Errorf("关闭MongoDB连接失败: %v", err))
		}
	}

	// 关闭Redis连接
	if dm.redis != nil {
		if err := dm.redis.Disconnect(); err != nil {
			errors = append(errors, fmt.Errorf("关闭Redis连接失败: %v", err))
		}
	}

	// 关闭TDengine连接
	if dm.tdengine != nil {
		if err := dm.tdengine.Disconnect(); err != nil {
			errors = append(errors, fmt.Errorf("关闭TDengine连接失败: %v", err))
		}
	}

	dm.initialized = false

	if len(errors) > 0 {
		return fmt.Errorf("关闭数据库连接时发生错误: %v", errors)
	}

	return nil
}

// GetMongoDB 获取MongoDB管理器
func (dm *DatabaseManager) GetMongoDB() *MongoDBManager {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()
	return dm.mongodb
}

// GetRedis 获取Redis管理器
func (dm *DatabaseManager) GetRedis() *RedisManager {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()
	return dm.redis
}

// GetTDengine 获取TDengine管理器
func (dm *DatabaseManager) GetTDengine() *TDengineManager {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()
	return dm.tdengine
}

// GetHealthManager 获取健康检查管理器
func (dm *DatabaseManager) GetHealthManager() *HealthManager {
	return dm.healthManager
}

// IsInitialized 检查是否已初始化
func (dm *DatabaseManager) IsInitialized() bool {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()
	return dm.initialized
}

// HealthCheck 执行所有数据库的健康检查
func (dm *DatabaseManager) HealthCheck(ctx context.Context) map[string]*DatabaseHealth {
	return dm.healthManager.CheckAll(ctx)
}

// IsHealthy 检查所有数据库是否健康
func (dm *DatabaseManager) IsHealthy() bool {
	return dm.healthManager.IsHealthy()
}

// GetHealthSummary 获取健康状态摘要
func (dm *DatabaseManager) GetHealthSummary() map[string]interface{} {
	summary := dm.healthManager.GetSummary()
	summary["initialized"] = dm.IsInitialized()
	summary["timestamp"] = time.Now()
	return summary
}

// Reconnect 重新连接所有数据库
func (dm *DatabaseManager) Reconnect(ctx context.Context) error {
	// 先关闭现有连接
	if err := dm.Close(ctx); err != nil {
		return fmt.Errorf("关闭现有连接失败: %v", err)
	}

	// 等待一段时间
	time.Sleep(2 * time.Second)

	// 重新初始化
	return dm.Initialize(ctx)
}

// ReconnectDatabase 重新连接指定数据库
func (dm *DatabaseManager) ReconnectDatabase(ctx context.Context, dbName string) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	switch dbName {
	case "mongodb":
		if dm.mongodb != nil {
			dm.mongodb.Disconnect(ctx)
		}
		return dm.initMongoDB(ctx)
	case "redis":
		if dm.redis != nil {
			dm.redis.Disconnect()
		}
		return dm.initRedis(ctx)
	case "tdengine":
		if dm.tdengine != nil {
			dm.tdengine.Disconnect()
		}
		return dm.initTDengine(ctx)
	default:
		return fmt.Errorf("未知的数据库名称: %s", dbName)
	}
}

// initMongoDB 初始化MongoDB
func (dm *DatabaseManager) initMongoDB(ctx context.Context) error {
	dm.mongodb = NewMongoDBManager(&dm.config.MongoDB)

	if err := dm.mongodb.Connect(ctx); err != nil {
		return err
	}

	// 创建索引
	if err := dm.mongodb.CreateIndexes(ctx); err != nil {
		return fmt.Errorf("创建MongoDB索引失败: %v", err)
	}

	return nil
}

// initRedis 初始化Redis
func (dm *DatabaseManager) initRedis(ctx context.Context) error {
	dm.redis = NewRedisManager(&dm.config.Redis)
	return dm.redis.Connect(ctx)
}

// initTDengine 初始化TDengine
func (dm *DatabaseManager) initTDengine(ctx context.Context) error {
	// 默认使用REST API，可以通过环境变量切换到WebSocket
	useWebSocket := os.Getenv("TDENGINE_USE_WEBSOCKET") == "true"

	if useWebSocket {
		dm.tdengine = NewTDengineManagerWithWebSocket(&dm.config.TaosDB)
	} else {
		dm.tdengine = NewTDengineManager(&dm.config.TaosDB)
	}

	if err := dm.tdengine.Connect(ctx); err != nil {
		return err
	}

	// 创建数据库
	if err := dm.tdengine.CreateDatabase(ctx); err != nil {
		return fmt.Errorf("创建TDengine数据库失败: %v", err)
	}

	// 创建表结构
	if err := dm.tdengine.CreateTables(ctx); err != nil {
		return fmt.Errorf("创建TDengine表结构失败: %v", err)
	}

	return nil
}

// registerHealthCheckers 注册健康检查器
func (dm *DatabaseManager) registerHealthCheckers() {
	if dm.mongodb != nil {
		dm.healthManager.RegisterChecker("mongodb", dm.mongodb)
	}
	if dm.redis != nil {
		dm.healthManager.RegisterChecker("redis", dm.redis)
	}
	if dm.tdengine != nil {
		dm.healthManager.RegisterChecker("tdengine", dm.tdengine)
	}
}

// GetConnectionStats 获取连接统计信息
func (dm *DatabaseManager) GetConnectionStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// MongoDB统计
	if dm.mongodb != nil && dm.mongodb.IsConnected(ctx) {
		if mongoStats, err := dm.mongodb.GetStats(ctx); err == nil {
			stats["mongodb"] = mongoStats
		}
	}

	// Redis统计
	if dm.redis != nil && dm.redis.IsConnected(ctx) {
		if redisStats, err := dm.redis.GetStats(ctx); err == nil {
			stats["redis"] = redisStats
		}
	}

	// TDengine统计
	if dm.tdengine != nil && dm.tdengine.IsConnected(ctx) {
		if tdStats, err := dm.tdengine.GetStats(ctx); err == nil {
			stats["tdengine"] = tdStats
		}
	}

	return stats
}

// SetHealthCheckInterval 设置健康检查间隔
func (dm *DatabaseManager) SetHealthCheckInterval(interval time.Duration) {
	dm.healthManager.SetInterval(interval)
}

// SetHealthCheckTimeout 设置健康检查超时
func (dm *DatabaseManager) SetHealthCheckTimeout(timeout time.Duration) {
	dm.healthManager.SetTimeout(timeout)
}

// WaitForHealthy 等待所有数据库变为健康状态
func (dm *DatabaseManager) WaitForHealthy(ctx context.Context, maxWait time.Duration) error {
	timeout := time.After(maxWait)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-timeout:
			return fmt.Errorf("等待数据库健康状态超时")
		case <-ticker.C:
			if dm.IsHealthy() {
				return nil
			}
		}
	}
}
