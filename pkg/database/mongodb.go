package database

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"beacon/cloud/internal/config"
)

// MongoDBManager MongoDB连接管理器
type MongoDBManager struct {
	client   *mongo.Client
	database *mongo.Database
	config   *config.MongoDBConfig
}

// NewMongoDBManager 创建MongoDB管理器
func NewMongoDBManager(cfg *config.MongoDBConfig) *MongoDBManager {
	return &MongoDBManager{
		config: cfg,
	}
}

// Connect 连接到MongoDB
func (m *MongoDBManager) Connect(ctx context.Context) error {
	// 构建连接字符串
	uri := m.buildConnectionURI()

	// 设置客户端选项
	clientOptions := options.Client().ApplyURI(uri)

	// 设置连接池配置
	clientOptions.SetMaxPoolSize(100)
	clientOptions.SetMinPoolSize(10)
	clientOptions.SetMaxConnIdleTime(30 * time.Second)
	clientOptions.SetConnectTimeout(10 * time.Second)
	clientOptions.SetServerSelectionTimeout(5 * time.Second)

	// 创建客户端
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return fmt.Errorf("连接MongoDB失败: %v", err)
	}

	// 测试连接
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		return fmt.Errorf("MongoDB连接测试失败: %v", err)
	}

	m.client = client
	m.database = client.Database(m.config.DBName)

	return nil
}

// Disconnect 断开MongoDB连接
func (m *MongoDBManager) Disconnect(ctx context.Context) error {
	if m.client != nil {
		return m.client.Disconnect(ctx)
	}
	return nil
}

// GetClient 获取MongoDB客户端
func (m *MongoDBManager) GetClient() *mongo.Client {
	return m.client
}

// GetDatabase 获取数据库实例
func (m *MongoDBManager) GetDatabase() *mongo.Database {
	return m.database
}

// GetCollection 获取集合
func (m *MongoDBManager) GetCollection(name string) *mongo.Collection {
	if m.database == nil {
		return nil
	}
	return m.database.Collection(name)
}

// HealthCheck 健康检查
func (m *MongoDBManager) HealthCheck(ctx context.Context) error {
	if m.client == nil {
		return fmt.Errorf("MongoDB客户端未初始化")
	}

	// 使用ping检查连接状态
	if err := m.client.Ping(ctx, readpref.Primary()); err != nil {
		return fmt.Errorf("MongoDB健康检查失败: %v", err)
	}

	return nil
}

// GetStats 获取数据库统计信息
func (m *MongoDBManager) GetStats(ctx context.Context) (map[string]interface{}, error) {
	if m.database == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	// 运行dbStats命令获取数据库统计信息
	var result map[string]interface{}
	err := m.database.RunCommand(ctx, map[string]interface{}{"dbStats": 1}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("获取数据库统计信息失败: %v", err)
	}

	return result, nil
}

// CreateIndexes 创建索引
func (m *MongoDBManager) CreateIndexes(ctx context.Context) error {
	if m.database == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 用户集合索引
	usersCollection := m.GetCollection("users")
	userIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "created_at", Value: 1}},
		},
	}

	if _, err := usersCollection.Indexes().CreateMany(ctx, userIndexes); err != nil {
		return fmt.Errorf("创建用户索引失败: %v", err)
	}

	// 分组集合索引
	groupsCollection := m.GetCollection("groups")
	groupIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "group_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "name", Value: 1}, {Key: "creator_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "creator_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "created_at", Value: 1}},
		},
	}

	if _, err := groupsCollection.Indexes().CreateMany(ctx, groupIndexes); err != nil {
		return fmt.Errorf("创建分组索引失败: %v", err)
	}

	// 分组成员集合索引
	groupMembersCollection := m.GetCollection("group_members")
	memberIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "group_id", Value: 1}, {Key: "user_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "user_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "joined_at", Value: 1}},
		},
	}

	if _, err := groupMembersCollection.Indexes().CreateMany(ctx, memberIndexes); err != nil {
		return fmt.Errorf("创建分组成员索引失败: %v", err)
	}

	// Topic集合索引
	topicsCollection := m.GetCollection("topics")
	topicIndexes := []mongo.IndexModel{
		{
			// Topic完整名称全局唯一
			Keys:    bson.D{{Key: "full_name", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			// Topic名称在分组内唯一
			Keys:    bson.D{{Key: "topic_name", Value: 1}, {Key: "group_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "group_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "created_at", Value: 1}},
		},
	}

	if _, err := topicsCollection.Indexes().CreateMany(ctx, topicIndexes); err != nil {
		return fmt.Errorf("创建Topic索引失败: %v", err)
	}

	// Topic权限集合索引
	topicPermissionsCollection := m.GetCollection("topic_permissions")
	permissionIndexes := []mongo.IndexModel{
		{
			// 用户+Topic权限唯一
			Keys:    bson.D{{Key: "user_id", Value: 1}, {Key: "full_topic", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "full_topic", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "group_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "updated_at", Value: 1}},
		},
	}

	if _, err := topicPermissionsCollection.Indexes().CreateMany(ctx, permissionIndexes); err != nil {
		return fmt.Errorf("创建Topic权限索引失败: %v", err)
	}

	// 分组加入申请集合索引
	joinRequestsCollection := m.GetCollection("group_join_requests")
	joinRequestIndexes := []mongo.IndexModel{
		{
			// 用户+分组申请唯一
			Keys:    bson.D{{Key: "group_id", Value: 1}, {Key: "user_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "user_id", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "owner_id", Value: 1}}, // 优化查询性能的关键索引
		},
		{
			Keys: bson.D{{Key: "status", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "created_at", Value: 1}},
		},
	}

	if _, err := joinRequestsCollection.Indexes().CreateMany(ctx, joinRequestIndexes); err != nil {
		return fmt.Errorf("创建分组加入申请索引失败: %v", err)
	}

	return nil
}

// buildConnectionURI 构建连接URI
func (m *MongoDBManager) buildConnectionURI() string {
	if m.config.Username != "" && m.config.Password != "" {
		return fmt.Sprintf("mongodb://%s:%s@%s:%s/%s?authSource=admin",
			m.config.Username, m.config.Password,
			m.config.Host, m.config.Port, m.config.DBName)
	}
	return fmt.Sprintf("mongodb://%s:%s/%s",
		m.config.Host, m.config.Port, m.config.DBName)
}

// IsConnected 检查是否已连接
func (m *MongoDBManager) IsConnected(ctx context.Context) bool {
	if m.client == nil {
		return false
	}
	return m.client.Ping(ctx, readpref.Primary()) == nil
}
