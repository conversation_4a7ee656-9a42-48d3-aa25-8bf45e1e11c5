package database

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"beacon/cloud/internal/config"
)

// RedisManager Redis连接管理器
type RedisManager struct {
	client *redis.Client
	config *config.RedisConfig
}

// NewRedisManager 创建Redis管理器
func NewRedisManager(cfg *config.RedisConfig) *RedisManager {
	return &RedisManager{
		config: cfg,
	}
}

// Connect 连接到Redis
func (r *RedisManager) Connect(ctx context.Context) error {
	// 创建Redis客户端选项
	options := &redis.Options{
		Addr:            fmt.Sprintf("%s:%s", r.config.Host, r.config.Port),
		Password:        r.config.Password,
		DB:              r.config.DB,
		DialTimeout:     10 * time.Second,
		ReadTimeout:     5 * time.Second,
		WriteTimeout:    5 * time.Second,
		PoolSize:        100,
		MinIdleConns:    10,
		MaxIdleConns:    30,
		ConnMaxIdleTime: 5 * time.Minute,
	}

	// 创建客户端
	client := redis.NewClient(options)

	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis连接测试失败: %v", err)
	}

	r.client = client
	return nil
}

// Disconnect 断开Redis连接
func (r *RedisManager) Disconnect() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetClient 获取Redis客户端
func (r *RedisManager) GetClient() *redis.Client {
	return r.client
}

// HealthCheck 健康检查
func (r *RedisManager) HealthCheck(ctx context.Context) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	// 使用ping检查连接状态
	if err := r.client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis健康检查失败: %v", err)
	}

	return nil
}

// GetStats 获取Redis统计信息
func (r *RedisManager) GetStats(ctx context.Context) (map[string]string, error) {
	if r.client == nil {
		return nil, fmt.Errorf("Redis客户端未初始化")
	}

	// 获取Redis信息
	info, err := r.client.Info(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis统计信息失败: %v", err)
	}

	// 解析信息
	stats := make(map[string]string)
	lines := parseRedisInfo(info)
	for key, value := range lines {
		stats[key] = value
	}

	return stats, nil
}

// GetMemoryUsage 获取内存使用情况
func (r *RedisManager) GetMemoryUsage(ctx context.Context) (map[string]interface{}, error) {
	if r.client == nil {
		return nil, fmt.Errorf("Redis客户端未初始化")
	}

	result := make(map[string]interface{})

	// 获取内存使用信息
	memoryInfo, err := r.client.Info(ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("获取内存信息失败: %v", err)
	}

	memoryLines := parseRedisInfo(memoryInfo)
	for key, value := range memoryLines {
		result[key] = value
	}

	return result, nil
}

// FlushDB 清空当前数据库
func (r *RedisManager) FlushDB(ctx context.Context) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.FlushDB(ctx).Err()
}

// FlushAll 清空所有数据库
func (r *RedisManager) FlushAll(ctx context.Context) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.FlushAll(ctx).Err()
}

// Set 设置键值对
func (r *RedisManager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值
func (r *RedisManager) Get(ctx context.Context, key string) (string, error) {
	if r.client == nil {
		return "", fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.Get(ctx, key).Result()
}

// Delete 删除键
func (r *RedisManager) Delete(ctx context.Context, keys ...string) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func (r *RedisManager) Exists(ctx context.Context, keys ...string) (int64, error) {
	if r.client == nil {
		return 0, fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.Exists(ctx, keys...).Result()
}

// SetExpire 设置键的过期时间
func (r *RedisManager) SetExpire(ctx context.Context, key string, expiration time.Duration) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.Expire(ctx, key, expiration).Err()
}

// GetTTL 获取键的剩余生存时间
func (r *RedisManager) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	if r.client == nil {
		return 0, fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.TTL(ctx, key).Result()
}

// HSet 设置哈希字段
func (r *RedisManager) HSet(ctx context.Context, key string, values ...interface{}) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.HSet(ctx, key, values...).Err()
}

// HGet 获取哈希字段值
func (r *RedisManager) HGet(ctx context.Context, key, field string) (string, error) {
	if r.client == nil {
		return "", fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.HGet(ctx, key, field).Result()
}

// HGetAll 获取哈希所有字段
func (r *RedisManager) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	if r.client == nil {
		return nil, fmt.Errorf("Redis客户端未初始化")
	}

	return r.client.HGetAll(ctx, key).Result()
}

// IsConnected 检查是否已连接
func (r *RedisManager) IsConnected(ctx context.Context) bool {
	if r.client == nil {
		return false
	}
	return r.client.Ping(ctx).Err() == nil
}

// parseRedisInfo 解析Redis INFO命令的输出
func parseRedisInfo(info string) map[string]string {
	result := make(map[string]string)
	lines := []rune(info)

	var key, value []rune
	var inKey = true

	for i := 0; i < len(lines); i++ {
		char := lines[i]

		if char == '\r' || char == '\n' {
			if len(key) > 0 && len(value) > 0 {
				result[string(key)] = string(value)
			}
			key = key[:0]
			value = value[:0]
			inKey = true
			continue
		}

		if char == ':' && inKey {
			inKey = false
			continue
		}

		if inKey {
			key = append(key, char)
		} else {
			value = append(value, char)
		}
	}

	// 处理最后一行
	if len(key) > 0 && len(value) > 0 {
		result[string(key)] = string(value)
	}

	return result
}
