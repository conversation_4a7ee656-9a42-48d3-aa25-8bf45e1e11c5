package database

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"

	_ "github.com/taosdata/driver-go/v3/taosRestful"
	_ "github.com/taosdata/driver-go/v3/taosWS"

	"beacon/cloud/internal/config"
)

// TDengineManager TDengine连接管理器
type TDengineManager struct {
	db           *sql.DB
	config       *config.TaosDBConfig
	useWebSocket bool // 是否使用WebSocket连接
}

// NewTDengineManager 创建TDengine管理器
func NewTDengineManager(cfg *config.TaosDBConfig) *TDengineManager {
	return &TDengineManager{
		config:       cfg,
		useWebSocket: false, // 默认使用REST API
	}
}

// NewTDengineManagerWithWebSocket 创建使用WebSocket的TDengine管理器
func NewTDengineManagerWithWebSocket(cfg *config.TaosDBConfig) *TDengineManager {
	return &TDengineManager{
		config:       cfg,
		useWebSocket: true,
	}
}

// Connect 连接到TDengine
func (t *TDengineManager) Connect(ctx context.Context) error {
	// 构建连接字符串
	dsn := t.buildConnectionDSN()

	// 选择驱动类型
	driverName := "taosRestful"
	if t.useWebSocket {
		driverName = "taosWS"
	}

	// 打开数据库连接
	db, err := sql.Open(driverName, dsn)
	if err != nil {
		return fmt.Errorf("连接TDengine失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(100)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Hour)
	db.SetConnMaxIdleTime(30 * time.Minute)

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return fmt.Errorf("TDengine连接测试失败: %v", err)
	}

	t.db = db
	return nil
}

// Disconnect 断开TDengine连接
func (t *TDengineManager) Disconnect() error {
	if t.db != nil {
		return t.db.Close()
	}
	return nil
}

// GetDBName 获取数据库名称
func (t *TDengineManager) GetDBName() string {
	return t.config.DBName
}

// GetDB 获取数据库连接
func (t *TDengineManager) GetDB() *sql.DB {
	return t.db
}

// HealthCheck 健康检查
func (t *TDengineManager) HealthCheck(ctx context.Context) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 使用ping检查连接状态
	if err := t.db.PingContext(ctx); err != nil {
		return fmt.Errorf("TDengine健康检查失败: %v", err)
	}

	return nil
}

// CreateDatabase 创建数据库
func (t *TDengineManager) CreateDatabase(ctx context.Context) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 创建数据库的SQL语句 - 注意：不使用USE语句，因为REST API无状态
	createDBSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s", t.config.DBName)

	_, err := t.db.ExecContext(ctx, createDBSQL)
	if err != nil {
		return fmt.Errorf("创建数据库失败: %v", err)
	}

	// 注意：不使用USE语句，因为REST API是无状态的
	// 所有后续的SQL语句都需要使用数据库前缀

	return nil
}

// CreateTables 创建表结构
func (t *TDengineManager) CreateTables(ctx context.Context) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 创建MQTT消息超级表 - 使用数据库前缀，避免保留关键字
	createMqttTableSQL := fmt.Sprintf("CREATE STABLE IF NOT EXISTS %s.mqtt_messages (ts TIMESTAMP, topic_name NCHAR(256), payload_data NCHAR(4096), qos_level TINYINT,client_id NCHAR(128)) TAGS (group_id NCHAR(64))", t.config.DBName)

	_, err := t.db.ExecContext(ctx, createMqttTableSQL)
	if err != nil {
		return fmt.Errorf("创建MQTT消息表失败: %v", err)
	}

	// 创建监控相关的新表
	if err := t.createMonitoringTables(ctx); err != nil {
		return fmt.Errorf("创建监控表失败: %v", err)
	}

	// 创建预警记录表
	if err := t.createAlertTables(ctx); err != nil {
		return fmt.Errorf("创建预警表失败: %v", err)
	}

	return nil
}

// createAlertTables 创建预警相关的表
func (t *TDengineManager) createAlertTables(ctx context.Context) error {
	// 创建预警记录超级表
	createAlertRecordsSQL := fmt.Sprintf(`
		CREATE STABLE IF NOT EXISTS %s.alert_records (
			ts TIMESTAMP,
			rule_id NCHAR(64),
			topic_name NCHAR(256),
			trigger_value NCHAR(1024),
			message NCHAR(2048),
			level TINYINT
		) TAGS (
			group_id NCHAR(64),
			rule_type NCHAR(32)
		)
	`, t.config.DBName)

	if _, err := t.db.ExecContext(ctx, createAlertRecordsSQL); err != nil {
		return fmt.Errorf("创建预警记录表失败: %v", err)
	}

	return nil
}

// createMonitoringTables 创建监控相关的表
func (t *TDengineManager) createMonitoringTables(ctx context.Context) error {
	// 创建性能指标表
	createPerformanceMetricsSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s.mqtt_performance_metrics (
			ts TIMESTAMP,
			clients_count BIGINT,
			clients_subscriptions BIGINT,
			messages_sent BIGINT,
			messages_received BIGINT,
			bytes_sent BIGINT,
			bytes_received BIGINT,
			uptime FLOAT,
			memory_usage BIGINT,
			network_in_bytes BIGINT,
			network_out_bytes BIGINT
		)
	`, t.config.DBName)

	if _, err := t.db.ExecContext(ctx, createPerformanceMetricsSQL); err != nil {
		return fmt.Errorf("创建性能指标表失败: %v", err)
	}

	// 创建系统监控表
	createSystemMetricsSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s.system_metrics (
			ts TIMESTAMP,
			cpu_usage FLOAT,
			memory_usage BIGINT,
			memory_total BIGINT,
			disk_usage BIGINT,
			disk_total BIGINT,
			network_in_bytes BIGINT,
			network_out_bytes BIGINT,
			load_average FLOAT,
			goroutine_count BIGINT,
			heap_size BIGINT,
			heap_used BIGINT
		)
	`, t.config.DBName)

	if _, err := t.db.ExecContext(ctx, createSystemMetricsSQL); err != nil {
		return fmt.Errorf("创建系统指标表失败: %v", err)
	}

	return nil
}

// InsertMqttMessage 插入MQTT消息
func (t *TDengineManager) InsertMqttMessage(ctx context.Context, data MqttMessageData) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 替换topic中的/为_
	topic := strings.ReplaceAll(data.Topic, "/", "_")
	// 构建表名 - 使用数据库前缀
	tableName := fmt.Sprintf("%s.mqtt_msg_%s", t.config.DBName, topic)
	superTableName := fmt.Sprintf("%s.mqtt_messages", t.config.DBName)

	// 插入数据的SQL语句 - 不使用参数绑定，直接构建SQL
	// TDengine的INSERT语法：INSERT INTO table_name USING super_table TAGS(...) VALUES(...)
	// 使用新的字段名避免保留关键字冲突
	insertSQL := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s') VALUES ('%s', '%s', '%s', %d, '%s')`,
		tableName,
		superTableName,
		data.GroupID,
		data.Timestamp.Format("2006-01-02 15:04:05.000"),
		data.Topic,    // topic_name
		data.Payload,  // payload_data
		data.QoS,      // qos_level
		data.ClientID, // client_id
	)

	_, err := t.db.ExecContext(ctx, insertSQL)
	if err != nil {
		return fmt.Errorf("插入MQTT消息失败: %v", err)
	}

	return nil
}

// QueryMqttMessages 查询MQTT消息
func (t *TDengineManager) QueryMqttMessages(ctx context.Context, query MqttMessageQuery) ([]MqttMessageData, error) {
	if t.db == nil {
		return nil, fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 构建查询SQL - 使用数据库前缀
	sql := t.buildMqttMessageQuery(query)

	rows, err := t.db.QueryContext(ctx, sql)
	if err != nil {
		return nil, fmt.Errorf("查询MQTT消息失败: %v", err)
	}
	defer rows.Close()

	var messages []MqttMessageData
	for rows.Next() {
		var msg MqttMessageData
		err := rows.Scan(
			&msg.Timestamp, // ts
			&msg.Topic,     // topic_name
			&msg.Payload,   // payload_data
			&msg.QoS,       // qos_level
			&msg.ClientID,  // client_id
			&msg.GroupID,   // group_id (tag)
		)
		if err != nil {
			return nil, fmt.Errorf("扫描查询结果失败: %v", err)
		}
		messages = append(messages, msg)
	}

	return messages, nil
}

// GetStats 获取数据库统计信息
func (t *TDengineManager) GetStats(ctx context.Context) (map[string]interface{}, error) {
	if t.db == nil {
		return nil, fmt.Errorf("TDengine数据库连接未初始化")
	}

	stats := make(map[string]interface{})

	// 查询数据库信息
	showDBSQL := "SHOW DATABASES"
	rows, err := t.db.QueryContext(ctx, showDBSQL)
	if err == nil {
		dbCount := 0
		for rows.Next() {
			dbCount++
		}
		rows.Close()
		stats["database_count"] = dbCount
	}

	// 查询指定数据库的表数量
	showTablesSQL := fmt.Sprintf("SHOW %s.TABLES", t.config.DBName)
	rows, err = t.db.QueryContext(ctx, showTablesSQL)
	if err == nil {
		tableCount := 0
		for rows.Next() {
			tableCount++
		}
		rows.Close()
		stats["table_count"] = tableCount
	}

	// 查询超级表数量
	showStablesSQL := fmt.Sprintf("SHOW %s.STABLES", t.config.DBName)
	rows, err = t.db.QueryContext(ctx, showStablesSQL)
	if err == nil {
		stableCount := 0
		for rows.Next() {
			stableCount++
		}
		rows.Close()
		stats["stable_count"] = stableCount
	}

	return stats, nil
}

// buildConnectionDSN 构建连接DSN
func (t *TDengineManager) buildConnectionDSN() string {
	if t.useWebSocket {
		// WebSocket连接 - 不指定数据库
		return fmt.Sprintf("%s:%s@ws(%s:%s)/",
			t.config.Username,
			t.config.Password,
			t.config.Host,
			t.config.Port,
		)
	} else {
		// REST连接 - 不指定数据库
		return fmt.Sprintf("%s:%s@http(%s:%s)/",
			t.config.Username,
			t.config.Password,
			t.config.Host,
			t.config.Port,
		)
	}
}

// buildMqttMessageQuery 构建MQTT消息查询SQL
func (t *TDengineManager) buildMqttMessageQuery(query MqttMessageQuery) string {
	// 使用数据库前缀和新的字段名
	sql := fmt.Sprintf("SELECT ts, topic_name, payload_data, qos_level, retain_flag, msg_id, client_id, user_name, group_id, device_id FROM %s.mqtt_messages WHERE 1=1", t.config.DBName)

	// 基础过滤条件
	if query.GroupID != "" {
		sql += fmt.Sprintf(" AND group_id='%s'", query.GroupID)
	}
	if query.Topic != "" {
		sql += fmt.Sprintf(" AND topic_name='%s'", query.Topic)
	}
	if query.ClientID != "" {
		sql += fmt.Sprintf(" AND client_id='%s'", query.ClientID)
	}

	// 模糊匹配条件
	if query.TopicLike != "" {
		sql += fmt.Sprintf(" AND topic_name LIKE '%%%s%%'", query.TopicLike)
	}
	if query.PayloadLike != "" {
		sql += fmt.Sprintf(" AND payload_data LIKE '%%%s%%'", query.PayloadLike)
	}

	// QoS和Retain过滤
	if query.QoS != nil {
		sql += fmt.Sprintf(" AND qos_level=%d", *query.QoS)
	}

	// 时间范围过滤
	if !query.StartTime.IsZero() {
		sql += fmt.Sprintf(" AND ts >= '%s'", query.StartTime.Format("2006-01-02 15:04:05"))
	}
	if !query.EndTime.IsZero() {
		sql += fmt.Sprintf(" AND ts <= '%s'", query.EndTime.Format("2006-01-02 15:04:05"))
	}

	// 多值过滤条件
	if len(query.GroupIDs) > 0 {
		groupIDsStr := "'" + strings.Join(query.GroupIDs, "','") + "'"
		sql += fmt.Sprintf(" AND group_id IN (%s)", groupIDsStr)
	}
	if len(query.Topics) > 0 {
		topicsStr := "'" + strings.Join(query.Topics, "','") + "'"
		sql += fmt.Sprintf(" AND topic_name IN (%s)", topicsStr)
	}

	// 排序
	orderBy := "ts"
	if query.OrderBy != "" {
		// 映射排序字段
		switch query.OrderBy {
		case "timestamp", "ts":
			orderBy = "ts"
		case "topic", "topic_name":
			orderBy = "topic_name"
		case "client_id":
			orderBy = "client_id"
		case "qos", "qos_level":
			orderBy = "qos_level"
		default:
			orderBy = "ts"
		}
	}

	orderDirection := "DESC"
	if !query.OrderDesc {
		orderDirection = "ASC"
	}
	sql += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDirection)

	// 分页处理
	limit := query.Limit
	offset := query.Offset

	// 如果使用Page/PageSize，则转换为Limit/Offset
	if query.Page > 0 && query.PageSize > 0 {
		limit = query.PageSize
		offset = (query.Page - 1) * query.PageSize
	}

	// 设置默认限制
	if limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000
	}

	sql += fmt.Sprintf(" LIMIT %d", limit)
	if offset > 0 {
		sql += fmt.Sprintf(" OFFSET %d", offset)
	}

	return sql
}

// SetUseWebSocket 设置是否使用WebSocket连接
func (t *TDengineManager) SetUseWebSocket(useWS bool) {
	t.useWebSocket = useWS
}

// IsUsingWebSocket 检查是否使用WebSocket连接
func (t *TDengineManager) IsUsingWebSocket() bool {
	return t.useWebSocket
}

// ExecuteSQL 执行原生SQL语句 - 自动添加数据库前缀
func (t *TDengineManager) ExecuteSQL(ctx context.Context, sqlStr string) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 如果SQL中没有数据库前缀，自动添加
	processedSQL := t.addDatabasePrefix(sqlStr)

	_, err := t.db.ExecContext(ctx, processedSQL)
	return err
}

// QuerySQL 查询原生SQL语句 - 自动添加数据库前缀
func (t *TDengineManager) QuerySQL(ctx context.Context, sqlStr string) (*sql.Rows, error) {
	if t.db == nil {
		return nil, fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 如果SQL中没有数据库前缀，自动添加
	processedSQL := t.addDatabasePrefix(sqlStr)

	return t.db.QueryContext(ctx, processedSQL)
}

// QueryAggregation 执行聚合查询
func (t *TDengineManager) QueryAggregation(ctx context.Context, query AggregationQuery) ([]AggregationResult, error) {
	if t.db == nil {
		return nil, fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 构建聚合查询SQL
	sql := t.buildAggregationQuery(query)

	rows, err := t.db.QueryContext(ctx, sql)
	if err != nil {
		return nil, fmt.Errorf("执行聚合查询失败: %v", err)
	}
	defer rows.Close()

	return t.parseAggregationResults(rows, query)
}

// addDatabasePrefix 为SQL语句添加数据库前缀
func (t *TDengineManager) addDatabasePrefix(sqlStr string) string {
	// 简单的前缀检查和添加逻辑
	// 这里可以根据需要实现更复杂的SQL解析
	if strings.Contains(sqlStr, t.config.DBName+".") {
		return sqlStr // 已经包含数据库前缀
	}

	// 对于常见的表操作，自动添加数据库前缀
	// 这是一个简化的实现，实际使用中可能需要更复杂的SQL解析
	return sqlStr
}

// IsConnected 检查是否已连接
func (t *TDengineManager) IsConnected(ctx context.Context) bool {
	if t.db == nil {
		return false
	}
	return t.db.PingContext(ctx) == nil
}

// buildAggregationQuery 构建聚合查询SQL
func (t *TDengineManager) buildAggregationQuery(query AggregationQuery) string {
	// 构建SELECT子句
	var selectFields []string

	// 添加时间字段（如果有时间间隔）
	if query.Interval != "" {
		selectFields = append(selectFields, "_wstart as timestamp")
	} else {
		selectFields = append(selectFields, "ts as timestamp")
	}

	// 添加分组字段
	selectFields = append(selectFields, query.GroupBy...)

	// 添加聚合字段
	for _, agg := range query.Aggregations {
		switch agg {
		case AggregationCount:
			selectFields = append(selectFields, "COUNT(*) as count_value")
		case AggregationAvg:
			selectFields = append(selectFields, "AVG(qos_level) as avg_qos")
		case AggregationMax:
			selectFields = append(selectFields, "MAX(ts) as max_time")
		case AggregationMin:
			selectFields = append(selectFields, "MIN(ts) as min_time")
		case AggregationSum:
			selectFields = append(selectFields, "COUNT(*) as sum_messages")
		case AggregationFirst:
			selectFields = append(selectFields, "FIRST(payload_data) as first_payload")
		case AggregationLast:
			selectFields = append(selectFields, "LAST(payload_data) as last_payload")
		}
	}

	// 构建基础SQL
	sql := fmt.Sprintf("SELECT %s FROM %s.mqtt_messages WHERE 1=1",
		strings.Join(selectFields, ", "), t.config.DBName)

	// 添加过滤条件
	if query.GroupID != "" {
		sql += fmt.Sprintf(" AND group_id='%s'", query.GroupID)
	}
	if query.Topic != "" {
		sql += fmt.Sprintf(" AND topic_name='%s'", query.Topic)
	}
	if !query.StartTime.IsZero() {
		sql += fmt.Sprintf(" AND ts >= '%s'", query.StartTime.Format("2006-01-02 15:04:05"))
	}
	if !query.EndTime.IsZero() {
		sql += fmt.Sprintf(" AND ts <= '%s'", query.EndTime.Format("2006-01-02 15:04:05"))
	}

	// 添加时间窗口
	if query.Interval != "" {
		sql += fmt.Sprintf(" INTERVAL(%s)", query.Interval)
	}

	// 添加分组
	var groupByFields []string
	if query.Interval != "" {
		groupByFields = append(groupByFields, "_wstart")
	}
	groupByFields = append(groupByFields, query.GroupBy...)

	if len(groupByFields) > 0 {
		sql += fmt.Sprintf(" GROUP BY %s", strings.Join(groupByFields, ", "))
	}

	// 添加排序
	if query.OrderBy != "" {
		order := "ASC"
		if query.OrderDesc {
			order = "DESC"
		}
		sql += fmt.Sprintf(" ORDER BY %s %s", query.OrderBy, order)
	} else {
		sql += " ORDER BY timestamp DESC"
	}

	// 添加限制
	if query.Limit > 0 {
		sql += fmt.Sprintf(" LIMIT %d", query.Limit)
	}

	return sql
}

// parseAggregationResults 解析聚合查询结果
func (t *TDengineManager) parseAggregationResults(rows *sql.Rows, query AggregationQuery) ([]AggregationResult, error) {
	var results []AggregationResult

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("获取列信息失败: %v", err)
	}

	for rows.Next() {
		// 创建扫描目标
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			continue
		}

		result := AggregationResult{
			GroupValues:  make(map[string]string),
			Aggregations: make(map[string]interface{}),
		}

		// 解析每列数据
		for i, column := range columns {
			value := values[i]

			switch column {
			case "timestamp":
				if timeStr, ok := value.(string); ok {
					if parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
						result.Timestamp = parsedTime
					}
				}
			default:
				// 检查是否是分组字段
				isGroupBy := false
				for _, groupField := range query.GroupBy {
					if column == groupField {
						if strValue, ok := value.(string); ok {
							result.GroupValues[column] = strValue
						}
						isGroupBy = true
						break
					}
				}

				// 如果不是分组字段，则是聚合字段
				if !isGroupBy {
					result.Aggregations[column] = value
				}
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// MqttMessageData MQTT消息数据结构
type MqttMessageData struct {
	Timestamp time.Time `json:"timestamp"`
	Topic     string    `json:"topic"`
	Payload   string    `json:"payload"`
	QoS       int       `json:"qos"`
	ClientID  string    `json:"client_id"`
	GroupID   string    `json:"group_id"`
}

// MqttMessageQuery MQTT消息查询参数
type MqttMessageQuery struct {
	GroupID     string    `json:"group_id"`
	Topic       string    `json:"topic"`
	TopicLike   string    `json:"topic_like"`
	ClientID    string    `json:"client_id"`
	QoS         *int      `json:"qos"`
	PayloadLike string    `json:"payload_like"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`

	// 分页参数
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Limit    int `json:"limit"`
	Offset   int `json:"offset"`

	// 排序参数
	OrderBy   string `json:"order_by"`
	OrderDesc bool   `json:"order_desc"`

	// 高级过滤
	GroupIDs []string `json:"group_ids"`
	Topics   []string `json:"topics"`
}

// AggregationType 聚合类型
type AggregationType string

const (
	AggregationCount AggregationType = "COUNT"
	AggregationAvg   AggregationType = "AVG"
	AggregationMax   AggregationType = "MAX"
	AggregationMin   AggregationType = "MIN"
	AggregationSum   AggregationType = "SUM"
	AggregationFirst AggregationType = "FIRST"
	AggregationLast  AggregationType = "LAST"
)

// TimeInterval 时间间隔类型
type TimeInterval string

const (
	IntervalMinute TimeInterval = "1m"
	Interval5Min   TimeInterval = "5m"
	Interval15Min  TimeInterval = "15m"
	Interval30Min  TimeInterval = "30m"
	IntervalHour   TimeInterval = "1h"
	Interval6Hour  TimeInterval = "6h"
	Interval12Hour TimeInterval = "12h"
	IntervalDay    TimeInterval = "1d"
	IntervalWeek   TimeInterval = "1w"
	IntervalMonth  TimeInterval = "1n"
)

// AggregationQuery 聚合查询参数
type AggregationQuery struct {
	GroupID      string            `json:"group_id"`
	Topic        string            `json:"topic"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      time.Time         `json:"end_time"`
	Interval     TimeInterval      `json:"interval"`     // 时间间隔
	Aggregations []AggregationType `json:"aggregations"` // 聚合类型列表
	GroupBy      []string          `json:"group_by"`     // 分组字段
	OrderBy      string            `json:"order_by"`     // 排序字段
	OrderDesc    bool              `json:"order_desc"`   // 是否降序
	Limit        int               `json:"limit"`
}

// AggregationResult 聚合查询结果
type AggregationResult struct {
	Timestamp    time.Time              `json:"timestamp"`
	GroupValues  map[string]string      `json:"group_values"` // 分组字段值
	Aggregations map[string]interface{} `json:"aggregations"` // 聚合结果
}

// ===== 监控数据操作方法 =====

// InsertPerformanceMetrics 插入性能指标
func (t *TDengineManager) InsertPerformanceMetrics(ctx context.Context, metrics PerformanceMetricsData) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 插入数据
	insertSQL := fmt.Sprintf(`
		INSERT INTO %s.mqtt_performance_metrics (ts, clients_count, clients_subscriptions, messages_sent, messages_received, bytes_sent, bytes_received, uptime, memory_usage, network_in_bytes, network_out_bytes) 
		VALUES ('%s', %d, %d, %d, %d, %d, %d, %f, %d, %d, %d)`,
		t.config.DBName,
		metrics.Timestamp.Format("2006-01-02 15:04:05.000"),
		metrics.ClientsCount, metrics.Subscriptions, metrics.MessagesSent,
		metrics.MessagesReceived, metrics.BytesSent, metrics.BytesReceived,
		metrics.Uptime, metrics.MemoryUsage,
		metrics.NetworkInBytes, metrics.NetworkOutBytes,
	)

	_, err := t.db.ExecContext(ctx, insertSQL)
	if err != nil {
		return fmt.Errorf("插入性能指标失败: %v", err)
	}

	return nil
}

// InsertSystemMetrics 插入系统指标
func (t *TDengineManager) InsertSystemMetrics(ctx context.Context, metrics SystemMetricsData) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 插入数据
	insertSQL := fmt.Sprintf(`
		INSERT INTO %s.system_metrics VALUES ('%s', %f, %d, %d, %d, %d, %d, %d, %f, %d, %d, %d)`,
		t.config.DBName,
		metrics.Timestamp.Format("2006-01-02 15:04:05.000"),
		metrics.CPUUsage, metrics.MemoryUsage, metrics.MemoryTotal,
		metrics.DiskUsage, metrics.DiskTotal, metrics.NetworkInBytes,
		metrics.NetworkOutBytes, metrics.LoadAverage, metrics.GoroutineCount,
		metrics.HeapSize, metrics.HeapUsed,
	)

	_, err := t.db.ExecContext(ctx, insertSQL)
	if err != nil {
		return fmt.Errorf("插入系统指标失败: %v", err)
	}

	return nil
}

// ===== 监控数据结构定义 =====
// PerformanceMetricsData MQTT服务器性能指标数据
type PerformanceMetricsData struct {
	Timestamp        time.Time `json:"timestamp"`
	ClientsCount     int64     `json:"clients_count"`
	Subscriptions    int64     `json:"subscriptions"`
	MessagesSent     int64     `json:"messages_sent"`
	MessagesReceived int64     `json:"messages_received"`
	BytesSent        int64     `json:"bytes_sent"`
	BytesReceived    int64     `json:"bytes_received"`
	Uptime           float64   `json:"uptime"`
	MemoryUsage      int64     `json:"memory_usage"` // MQTT服务器内存使用量
	NetworkInBytes   int64     `json:"network_in_bytes"`
	NetworkOutBytes  int64     `json:"network_out_bytes"`
}

// SystemMetricsData 系统指标数据
type SystemMetricsData struct {
	Timestamp       time.Time `json:"timestamp"`
	CPUUsage        float64   `json:"cpu_usage"`
	MemoryUsage     int64     `json:"memory_usage"`
	MemoryTotal     int64     `json:"memory_total"`
	DiskUsage       int64     `json:"disk_usage"`
	DiskTotal       int64     `json:"disk_total"`
	NetworkInBytes  int64     `json:"network_in_bytes"`
	NetworkOutBytes int64     `json:"network_out_bytes"`
	LoadAverage     float64   `json:"load_average"`
	GoroutineCount  int64     `json:"goroutine_count"`
	HeapSize        int64     `json:"heap_size"`
	HeapUsed        int64     `json:"heap_used"`
}

// ===== 监控数据查询方法 =====

// QueryPerformanceMetrics 查询性能指标
func (t *TDengineManager) QueryPerformanceMetrics(ctx context.Context, query MonitorQuery) ([]PerformanceMetricsData, error) {
	if t.db == nil {
		return nil, fmt.Errorf("TDengine数据库连接未初始化")
	}

	sql := fmt.Sprintf(`
		SELECT ts, clients_count, clients_subscriptions, messages_sent, messages_received,
		       bytes_sent, bytes_received, uptime, memory_usage,
		       network_in_bytes, network_out_bytes
		FROM %s.mqtt_performance_metrics
		WHERE 1=1`, t.config.DBName)

	// 添加时间过滤
	if !query.StartTime.IsZero() {
		sql += fmt.Sprintf(" AND ts >= '%s'", query.StartTime.Format("2006-01-02 15:04:05"))
	}
	if !query.EndTime.IsZero() {
		sql += fmt.Sprintf(" AND ts <= '%s'", query.EndTime.Format("2006-01-02 15:04:05"))
	}

	// 添加时间间隔聚合
	if query.Interval != "" {
		sql += fmt.Sprintf(" INTERVAL(%s)", query.Interval)
	}

	// 添加排序和限制
	sql += " ORDER BY ts DESC"
	if query.Limit > 0 {
		sql += fmt.Sprintf(" LIMIT %d", query.Limit)
	}

	rows, err := t.db.QueryContext(ctx, sql)
	if err != nil {
		return nil, fmt.Errorf("查询性能指标失败: %v", err)
	}
	defer rows.Close()

	var metrics []PerformanceMetricsData
	for rows.Next() {
		var metric PerformanceMetricsData
		err := rows.Scan(
			&metric.Timestamp, &metric.ClientsCount, &metric.Subscriptions,
			&metric.MessagesSent, &metric.MessagesReceived, &metric.BytesSent,
			&metric.BytesReceived, &metric.Uptime, &metric.MemoryUsage,
			&metric.NetworkInBytes, &metric.NetworkOutBytes,
		)
		if err != nil {
			continue
		}
		metrics = append(metrics, metric)
	}

	return metrics, nil
}

// QueryTopicData 查询指定Topic的数据
func (t *TDengineManager) QueryTopicData(ctx context.Context, query TopicDataQuery) ([]MqttMessageData, int64, error) {
	if t.db == nil {
		return nil, 0, fmt.Errorf("TDengine数据库连接未初始化")
	}

	if query.Topic == "" {
		return nil, 0, fmt.Errorf("topic参数不能为空")
	}

	// 设置默认值
	if query.PageSize <= 0 {
		query.PageSize = 10
	}
	if query.Page <= 0 {
		query.Page = 1
	}

	// 设置默认时间范围为1天
	if query.StartTime.IsZero() && query.EndTime.IsZero() {
		query.EndTime = time.Now()
		query.StartTime = query.EndTime.Add(-24 * time.Hour)
	}

	// 构建查询条件
	whereClause := fmt.Sprintf("WHERE topic_name='%s'", query.Topic)

	if !query.StartTime.IsZero() {
		whereClause += fmt.Sprintf(" AND ts >= '%s'", query.StartTime.Format("2006-01-02 15:04:05"))
	}
	if !query.EndTime.IsZero() {
		whereClause += fmt.Sprintf(" AND ts <= '%s'", query.EndTime.Format("2006-01-02 15:04:05"))
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM %s.mqtt_messages %s", t.config.DBName, whereClause)
	var total int64
	err := t.db.QueryRowContext(ctx, countSQL).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Topic数据总数失败: %v", err)
	}

	// 构建分页查询SQL
	offset := (query.Page - 1) * query.PageSize
	dataSQL := fmt.Sprintf(`
		SELECT ts, topic_name, payload_data, qos_level, client_id, group_id
		FROM %s.mqtt_messages
		%s
		ORDER BY ts DESC
		LIMIT %d OFFSET %d`,
		t.config.DBName, whereClause, query.PageSize, offset)

	rows, err := t.db.QueryContext(ctx, dataSQL)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Topic数据失败: %v", err)
	}
	defer rows.Close()

	var messages []MqttMessageData
	for rows.Next() {
		var msg MqttMessageData
		err := rows.Scan(
			&msg.Timestamp,
			&msg.Topic,
			&msg.Payload,
			&msg.QoS,
			&msg.ClientID,
			&msg.GroupID,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描查询结果失败: %v", err)
		}
		messages = append(messages, msg)
	}

	return messages, total, nil
}

// DeleteTopicData 删除指定Topic在指定时间范围内的数据
func (t *TDengineManager) DeleteTopicData(ctx context.Context, query TopicDataDeleteQuery) (int64, error) {
	if t.db == nil {
		return 0, fmt.Errorf("TDengine数据库连接未初始化")
	}

	if query.Topic == "" {
		return 0, fmt.Errorf("topic参数不能为空")
	}

	if query.StartTime.IsZero() || query.EndTime.IsZero() {
		return 0, fmt.Errorf("开始时间和结束时间不能为空")
	}

	if query.StartTime.After(query.EndTime) {
		return 0, fmt.Errorf("开始时间不能晚于结束时间")
	}

	// 先查询要删除的记录数
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*) FROM %s.mqtt_messages
		WHERE topic_name='%s' AND ts >= '%s' AND ts <= '%s'`,
		t.config.DBName, query.Topic,
		query.StartTime.Format("2006-01-02 15:04:05"),
		query.EndTime.Format("2006-01-02 15:04:05"))

	var count int64
	err := t.db.QueryRowContext(ctx, countSQL).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询要删除的记录数失败: %v", err)
	}

	if count == 0 {
		return 0, nil // 没有记录需要删除
	}

	// 执行删除操作
	deleteSQL := fmt.Sprintf(`
		DELETE FROM %s.mqtt_messages
		WHERE topic_name='%s' AND ts >= '%s' AND ts <= '%s'`,
		t.config.DBName, query.Topic,
		query.StartTime.Format("2006-01-02 15:04:05"),
		query.EndTime.Format("2006-01-02 15:04:05"))

	result, err := t.db.ExecContext(ctx, deleteSQL)
	if err != nil {
		return 0, fmt.Errorf("删除Topic数据失败: %v", err)
	}

	// 获取实际删除的行数
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		// 如果无法获取影响行数，返回之前查询的数量
		return count, nil
	}

	return rowsAffected, nil
}

// InsertAlertRecord 插入预警记录
func (t *TDengineManager) InsertAlertRecord(ctx context.Context, record AlertRecordData) error {
	if t.db == nil {
		return fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 构建表名
	tableName := fmt.Sprintf("%s.alert_record_%s", t.config.DBName, record.GroupID)
	superTableName := fmt.Sprintf("%s.alert_records", t.config.DBName)

	// 插入数据的SQL语句
	insertSQL := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s', '%s') VALUES ('%s', '%s', '%s', '%s', '%s', %d)`,
		tableName,
		superTableName,
		record.GroupID,
		record.RuleType,
		record.Timestamp.Format("2006-01-02 15:04:05.000"),
		record.RuleID,
		record.TopicName,
		record.TriggerValue,
		record.Message,
		record.Level,
	)

	_, err := t.db.ExecContext(ctx, insertSQL)
	if err != nil {
		return fmt.Errorf("插入预警记录失败: %v", err)
	}

	return nil
}

// QueryAlertRecords 查询预警记录
func (t *TDengineManager) QueryAlertRecords(ctx context.Context, query AlertRecordQuery) ([]AlertRecordData, int64, error) {
	if t.db == nil {
		return nil, 0, fmt.Errorf("TDengine数据库连接未初始化")
	}

	// 设置默认值
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.Page <= 0 {
		query.Page = 1
	}

	// 构建查询条件
	whereClause := "WHERE 1=1"

	if query.GroupID != "" {
		whereClause += fmt.Sprintf(" AND group_id='%s'", query.GroupID)
	}
	if query.TopicName != "" {
		whereClause += fmt.Sprintf(" AND topic_name='%s'", query.TopicName)
	}
	if query.RuleID != "" {
		whereClause += fmt.Sprintf(" AND rule_id='%s'", query.RuleID)
	}
	if query.Level > 0 {
		whereClause += fmt.Sprintf(" AND level=%d", query.Level)
	}

	if query.TimeRange != "" {
		whereClause += fmt.Sprintf(" AND ts >= NOW - %s", query.TimeRange)
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM %s.alert_records %s", t.config.DBName, whereClause)
	log.Println(countSQL)
	var total int64
	err := t.db.QueryRowContext(ctx, countSQL).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询预警记录总数失败: %v", err)
	}

	// 构建分页查询SQL
	offset := (query.Page - 1) * query.PageSize
	dataSQL := fmt.Sprintf(`
		SELECT ts, rule_id, topic_name, trigger_value, message, level, group_id, rule_type
		FROM %s.alert_records
		%s
		ORDER BY ts DESC
		LIMIT %d OFFSET %d`,
		t.config.DBName, whereClause, query.PageSize, offset)

	rows, err := t.db.QueryContext(ctx, dataSQL)
	if err != nil {
		return nil, 0, fmt.Errorf("查询预警记录失败: %v", err)
	}
	defer rows.Close()

	var records []AlertRecordData
	for rows.Next() {
		var record AlertRecordData
		err := rows.Scan(
			&record.Timestamp,
			&record.RuleID,
			&record.TopicName,
			&record.TriggerValue,
			&record.Message,
			&record.Level,
			&record.GroupID,
			&record.RuleType,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描查询结果失败: %v", err)
		}
		records = append(records, record)
	}

	return records, total, nil
}

// MonitorQuery 监控查询参数
type MonitorQuery struct {
	GroupID   string    `json:"group_id,omitempty"`
	ClientID  string    `json:"client_id,omitempty"`
	Topic     string    `json:"topic,omitempty"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Interval  string    `json:"interval,omitempty"`
	Limit     int       `json:"limit,omitempty"`
	Offset    int       `json:"offset,omitempty"`
}

// TopicDataQuery Topic数据查询参数
type TopicDataQuery struct {
	Topic     string    `json:"topic"`      // 必填：topic名称
	PageSize  int       `json:"page_size"`  // 可选：每页大小，默认10
	Page      int       `json:"page"`       // 可选：页码，默认1
	StartTime time.Time `json:"start_time"` // 可选：开始时间
	EndTime   time.Time `json:"end_time"`   // 可选：结束时间
}

// TopicDataDeleteQuery Topic数据删除参数
type TopicDataDeleteQuery struct {
	Topic     string    `json:"topic"`      // 必填：topic名称
	StartTime time.Time `json:"start_time"` // 必填：开始时间
	EndTime   time.Time `json:"end_time"`   // 必填：结束时间
}

// AlertRecordData 预警记录数据结构
type AlertRecordData struct {
	Timestamp    time.Time `json:"timestamp"`     // 预警时间
	RuleID       string    `json:"rule_id"`       // 规则ID
	TopicName    string    `json:"topic_name"`    // Topic名称
	TriggerValue string    `json:"trigger_value"` // 触发值
	Message      string    `json:"message"`       // 预警消息
	Level        int       `json:"level"`         // 预警级别
	GroupID      string    `json:"group_id"`      // 分组ID(Tag)
	RuleType     string    `json:"rule_type"`     // 规则类型(Tag)
}

// AlertRecordQuery 预警记录查询参数
type AlertRecordQuery struct {
	GroupID   string `json:"group_id,omitempty"`
	TopicName string `json:"topic_name,omitempty"`
	RuleID    string `json:"rule_id,omitempty"`
	Level     int    `json:"level,omitempty"`
	TimeRange string `json:"time_range"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}
