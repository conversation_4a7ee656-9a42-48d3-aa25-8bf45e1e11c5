package utils

import (
	"errors"
	"fmt"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

const (
	// MinPasswordLength 最小密码长度
	MinPasswordLength = 8
	// MaxPasswordLength 最大密码长度
	MaxPasswordLength = 128
	// DefaultBcryptCost 默认bcrypt成本
	DefaultBcryptCost = 12
)

// PasswordStrength 密码强度枚举
type PasswordStrength int

const (
	PasswordWeak PasswordStrength = iota
	PasswordMedium
	PasswordStrong
)

// PasswordValidator 密码验证器
type PasswordValidator struct {
	MinLength        int
	MaxLength        int
	RequireUppercase bool
	RequireLowercase bool
	RequireDigit     bool
	RequireSpecial   bool
}

// DefaultPasswordValidator 默认密码验证器
func DefaultPasswordValidator() *PasswordValidator {
	return &PasswordValidator{
		MinLength:        MinPasswordLength,
		MaxLength:        MaxPasswordLength,
		RequireUppercase: true,
		RequireLowercase: true,
		RequireDigit:     true,
		RequireSpecial:   false,
	}
}

// HashPassword 使用bcrypt哈希密码
func HashPassword(password string) (string, error) {
	if len(password) == 0 {
		return "", errors.New("密码不能为空")
	}

	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), DefaultBcryptCost)
	if err != nil {
		return "", fmt.Errorf("密码哈希失败: %w", err)
	}

	return string(hashedBytes), nil
}

// VerifyPassword 验证密码是否匹配哈希值
func VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// ValidatePassword 验证密码强度
func (v *PasswordValidator) ValidatePassword(password string) error {
	if len(password) < v.MinLength {
		return fmt.Errorf("密码长度不能少于%d个字符", v.MinLength)
	}

	if len(password) > v.MaxLength {
		return fmt.Errorf("密码长度不能超过%d个字符", v.MaxLength)
	}

	var (
		hasUpper   bool
		hasLower   bool
		hasDigit   bool
		hasSpecial bool
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if v.RequireUppercase && !hasUpper {
		return errors.New("密码必须包含至少一个大写字母")
	}

	if v.RequireLowercase && !hasLower {
		return errors.New("密码必须包含至少一个小写字母")
	}

	if v.RequireDigit && !hasDigit {
		return errors.New("密码必须包含至少一个数字")
	}

	if v.RequireSpecial && !hasSpecial {
		return errors.New("密码必须包含至少一个特殊字符")
	}

	return nil
}

// GetPasswordStrength 获取密码强度
func GetPasswordStrength(password string) PasswordStrength {
	if len(password) < 6 {
		return PasswordWeak
	}

	var (
		hasUpper   bool
		hasLower   bool
		hasDigit   bool
		hasSpecial bool
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	score := 0
	if hasUpper {
		score++
	}
	if hasLower {
		score++
	}
	if hasDigit {
		score++
	}
	if hasSpecial {
		score++
	}

	if len(password) >= 12 {
		score++
	}

	switch {
	case score >= 4:
		return PasswordStrong
	case score >= 2:
		return PasswordMedium
	default:
		return PasswordWeak
	}
}

// IsPasswordSecure 检查密码是否安全
func IsPasswordSecure(password string) bool {
	validator := DefaultPasswordValidator()
	return validator.ValidatePassword(password) == nil && GetPasswordStrength(password) >= PasswordMedium
}

// GenerateRandomPassword 生成随机密码
func GenerateRandomPassword(length int) (string, error) {
	if length < MinPasswordLength {
		length = MinPasswordLength
	}
	if length > MaxPasswordLength {
		length = MaxPasswordLength
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	// 确保至少包含每种类型的字符
	password[0] = 'a' + byte(length%26)                    // 小写字母
	password[1] = 'A' + byte(length%26)                    // 大写字母
	password[2] = '0' + byte(length%10)                    // 数字
	password[3] = "!@#$%^&*"[length%8]                     // 特殊字符

	// 填充剩余位置
	for i := 4; i < length; i++ {
		password[i] = charset[i%len(charset)]
	}

	// 简单打乱
	for i := range password {
		j := (i + length) % len(password)
		password[i], password[j] = password[j], password[i]
	}

	return string(password), nil
}
