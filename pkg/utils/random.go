package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
)

const (
	// DefaultJoinCodeLength 默认邀请码长度
	DefaultJoinCodeLength = 8
	// JoinCodeCharset 邀请码字符集（排除容易混淆的字符）
	JoinCodeCharset = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
)

// GenerateJoinCode 生成分组邀请码
func GenerateJoinCode() (string, error) {
	return GenerateJoinCodeWithLength(DefaultJoinCodeLength)
}

// GenerateJoinCodeWithLength 生成指定长度的邀请码
func GenerateJoinCodeWithLength(length int) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("邀请码长度必须大于0")
	}

	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(JoinCodeCharset)))

	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", fmt.<PERSON><PERSON><PERSON>("生成随机数失败: %w", err)
		}
		result[i] = JoinCodeCharset[randomIndex.Int64()]
	}

	return string(result), nil
}

// GenerateHexCode 生成十六进制邀请码
func GenerateHexCode(byteLength int) (string, error) {
	if byteLength <= 0 {
		return "", fmt.Errorf("字节长度必须大于0")
	}

	bytes := make([]byte, byteLength)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("生成随机字节失败: %w", err)
	}

	return strings.ToUpper(hex.EncodeToString(bytes)), nil
}

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int, charset string) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("字符串长度必须大于0")
	}
	if charset == "" {
		charset = JoinCodeCharset
	}

	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", fmt.Errorf("生成随机数失败: %w", err)
		}
		result[i] = charset[randomIndex.Int64()]
	}

	return string(result), nil
}

// ValidateJoinCode 验证邀请码格式
func ValidateJoinCode(code string) error {
	if code == "" {
		return fmt.Errorf("邀请码不能为空")
	}

	if len(code) < 4 || len(code) > 20 {
		return fmt.Errorf("邀请码长度必须在4-20个字符之间")
	}

	// 检查字符是否都在允许的字符集中
	for _, char := range code {
		if !strings.ContainsRune(JoinCodeCharset+"abcdefghijklmnopqrstuvwxyz0123456789", char) {
			return fmt.Errorf("邀请码包含无效字符: %c", char)
		}
	}

	return nil
}

// IsJoinCodeValid 检查邀请码是否有效
func IsJoinCodeValid(code string) bool {
	return ValidateJoinCode(code) == nil
}
