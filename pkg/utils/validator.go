package utils

import (
	"fmt"
	"reflect"
	"strings"
)

// ValidateStruct 验证结构体字段
func ValidateStruct(s interface{}) error {
	if s == nil {
		return fmt.Errorf("验证对象不能为空")
	}

	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return fmt.Errorf("验证对象必须是结构体")
	}

	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 获取binding标签
		bindingTag := fieldType.Tag.Get("binding")
		if bindingTag == "" {
			continue
		}

		// 解析binding规则
		rules := strings.Split(bindingTag, ",")
		for _, rule := range rules {
			rule = strings.TrimSpace(rule)
			if err := validateFieldRule(field, fieldType.Name, rule); err != nil {
				return fmt.Errorf("字段 %s 验证失败: %w", fieldType.Name, err)
			}
		}
	}

	return nil
}

// validateFieldRule 验证单个字段规则
func validateFieldRule(field reflect.Value, fieldName, rule string) error {
	switch {
	case rule == "required":
		return validateRequired(field, fieldName)
	case strings.HasPrefix(rule, "min="):
		return validateMin(field, fieldName, rule)
	case strings.HasPrefix(rule, "max="):
		return validateMax(field, fieldName, rule)
	case strings.HasPrefix(rule, "oneof="):
		return validateOneOf(field, fieldName, rule)
	default:
		// 忽略不支持的规则
		return nil
	}
}

// validateRequired 验证必填字段
func validateRequired(field reflect.Value, fieldName string) error {
	switch field.Kind() {
	case reflect.String:
		if field.String() == "" {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	case reflect.Slice, reflect.Array, reflect.Map:
		if field.Len() == 0 {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	case reflect.Ptr, reflect.Interface:
		if field.IsNil() {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		// 对于数字类型，0值也认为是有效的
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		// 对于无符号数字类型，0值也认为是有效的
	case reflect.Float32, reflect.Float64:
		// 对于浮点类型，0值也认为是有效的
	case reflect.Bool:
		// 对于布尔类型，false也认为是有效的
	default:
		if field.IsZero() {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	}
	return nil
}

// validateMin 验证最小值/长度
func validateMin(field reflect.Value, fieldName, rule string) error {
	minStr := strings.TrimPrefix(rule, "min=")
	var min int
	if _, err := fmt.Sscanf(minStr, "%d", &min); err != nil {
		return fmt.Errorf("无效的min规则: %s", rule)
	}

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) < min {
			return fmt.Errorf("%s 长度不能少于 %d 个字符", fieldName, min)
		}
	case reflect.Slice, reflect.Array:
		if field.Len() < min {
			return fmt.Errorf("%s 长度不能少于 %d", fieldName, min)
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if field.Int() < int64(min) {
			return fmt.Errorf("%s 不能小于 %d", fieldName, min)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if field.Uint() < uint64(min) {
			return fmt.Errorf("%s 不能小于 %d", fieldName, min)
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() < float64(min) {
			return fmt.Errorf("%s 不能小于 %f", fieldName, float64(min))
		}
	}
	return nil
}

// validateMax 验证最大值/长度
func validateMax(field reflect.Value, fieldName, rule string) error {
	maxStr := strings.TrimPrefix(rule, "max=")
	var max int
	if _, err := fmt.Sscanf(maxStr, "%d", &max); err != nil {
		return fmt.Errorf("无效的max规则: %s", rule)
	}

	switch field.Kind() {
	case reflect.String:
		if len(field.String()) > max {
			return fmt.Errorf("%s 长度不能超过 %d 个字符", fieldName, max)
		}
	case reflect.Slice, reflect.Array:
		if field.Len() > max {
			return fmt.Errorf("%s 长度不能超过 %d", fieldName, max)
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if field.Int() > int64(max) {
			return fmt.Errorf("%s 不能大于 %d", fieldName, max)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if field.Uint() > uint64(max) {
			return fmt.Errorf("%s 不能大于 %d", fieldName, max)
		}
	case reflect.Float32, reflect.Float64:
		if field.Float() > float64(max) {
			return fmt.Errorf("%s 不能大于 %f", fieldName, float64(max))
		}
	}
	return nil
}

// validateOneOf 验证枚举值
func validateOneOf(field reflect.Value, fieldName, rule string) error {
	oneOfStr := strings.TrimPrefix(rule, "oneof=")
	validValues := strings.Split(oneOfStr, " ")
	
	fieldValue := ""
	switch field.Kind() {
	case reflect.String:
		fieldValue = field.String()
	default:
		fieldValue = fmt.Sprintf("%v", field.Interface())
	}

	for _, validValue := range validValues {
		if fieldValue == strings.TrimSpace(validValue) {
			return nil
		}
	}

	return fmt.Errorf("%s 必须是以下值之一: %s", fieldName, strings.Join(validValues, ", "))
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	
	// 简单的邮箱格式验证
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return fmt.Errorf("邮箱格式无效")
	}
	
	return nil
}

// ValidateUsername 验证用户名格式
func ValidateUsername(username string) error {
	if username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	
	if len(username) < 3 || len(username) > 20 {
		return fmt.Errorf("用户名长度必须在3-20个字符之间")
	}
	
	// 检查字符是否都是字母、数字或下划线
	for _, char := range username {
		if !((char >= 'a' && char <= 'z') || 
			 (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || 
			 char == '_') {
			return fmt.Errorf("用户名只能包含字母、数字和下划线")
		}
	}
	
	return nil
}

// ValidateRequired 验证必填字段
func ValidateRequired(value interface{}, fieldName string) error {
	if value == nil {
		return fmt.Errorf("%s 不能为空", fieldName)
	}
	
	v := reflect.ValueOf(value)
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
		v = v.Elem()
	}
	
	switch v.Kind() {
	case reflect.String:
		if v.String() == "" {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	case reflect.Slice, reflect.Array, reflect.Map:
		if v.Len() == 0 {
			return fmt.Errorf("%s 不能为空", fieldName)
		}
	}
	
	return nil
}
