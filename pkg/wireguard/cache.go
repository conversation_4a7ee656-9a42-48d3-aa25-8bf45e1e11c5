package wireguard

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type cacheService struct {
	redis *redis.Client
}

// NewCacheService 创建新的缓存服务
func NewCacheService(redis *redis.Client) CacheService {
	return &cacheService{redis: redis}
}

// CacheClientConfig 缓存客户端配置
func (cs *cacheService) CacheClientConfig(userID, clientID primitive.ObjectID, config string) error {
	key := fmt.Sprintf("wg:config:%s:%s", userID.Hex(), clientID.Hex())
	return cs.redis.Set(context.Background(), key, config, 24*time.Hour).Err()
}

// GetCachedClientConfig 获取缓存的客户端配置
func (cs *cacheService) GetCachedClientConfig(userID, clientID primitive.ObjectID) (string, error) {
	key := fmt.Sprintf("wg:config:%s:%s", userID.Hex(), clientID.Hex())
	return cs.redis.Get(context.Background(), key).Result()
}

// CacheClientStats 缓存客户端统计信息
func (cs *cacheService) CacheClientStats(userID, clientID primitive.ObjectID, stats *ClientStats) error {
	key := fmt.Sprintf("wg:stats:%s:%s", userID.Hex(), clientID.Hex())
	data, err := json.Marshal(stats)
	if err != nil {
		return err
	}
	return cs.redis.Set(context.Background(), key, data, 5*time.Minute).Err()
}

// GetCachedClientStats 获取缓存的客户端统计信息
func (cs *cacheService) GetCachedClientStats(userID, clientID primitive.ObjectID) (*ClientStats, error) {
	key := fmt.Sprintf("wg:stats:%s:%s", userID.Hex(), clientID.Hex())
	data, err := cs.redis.Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}

	var stats ClientStats
	err = json.Unmarshal([]byte(data), &stats)
	return &stats, err
}

// InvalidateUserCache 清除用户缓存
func (cs *cacheService) InvalidateUserCache(userID primitive.ObjectID) error {
	pattern := fmt.Sprintf("wg:*:%s:*", userID.Hex())
	keys, err := cs.redis.Keys(context.Background(), pattern).Result()
	if err != nil {
		return err
	}
	if len(keys) > 0 {
		return cs.redis.Del(context.Background(), keys...).Err()
	}
	return nil
}

// SetUserOnline 设置用户在线状态
func (cs *cacheService) SetUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) error {
	key := fmt.Sprintf("wg:online:%s:%s", userID.Hex(), clientID.Hex())
	return cs.redis.Set(context.Background(), key, time.Now().Unix(), 10*time.Minute).Err()
}

// IsUserOnline 检查用户是否在线
func (cs *cacheService) IsUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) bool {
	key := fmt.Sprintf("wg:online:%s:%s", userID.Hex(), clientID.Hex())
	_, err := cs.redis.Get(context.Background(), key).Result()
	return err == nil
}
