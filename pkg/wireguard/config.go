package wireguard

import (
	"context"
	"fmt"
	"net"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

type configService struct {
	db     *mongo.Database
	config *Config
}

// NewConfigService 创建新的配置服务
func NewConfigService(db *mongo.Database, config *Config) ConfigService {
	return &configService{
		db:     db,
		config: config,
	}
}

// GenerateKeys 生成 WireGuard 密钥对
func (cs *configService) GenerateKeys() (privateKey, publicKey string, err error) {
	key, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return "", "", err
	}

	privateKey = key.String()
	publicKey = key.PublicKey().String()
	return privateKey, publicKey, nil
}

// GeneratePreSharedKey 生成预共享密钥
func (cs *configService) GeneratePreSharedKey() (string, error) {
	key, err := wgtypes.GenerateKey()
	if err != nil {
		return "", err
	}
	return key.String(), nil
}

// AllocateIPAddress 分配 IP 地址
func (cs *configService) AllocateIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID) (ipv4, ipv6 string, err error) {
	// 获取服务器信息
	var server UserWGServer
	err = cs.db.Collection("wg_servers").FindOne(context.Background(), bson.M{
		"_id":     serverID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return "", "", err
	}

	// 解析 IPv4 CIDR
	_, ipv4Net, err := net.ParseCIDR(server.IPv4CIDR)
	if err != nil {
		return "", "", err
	}

	// 获取已分配的 IP 地址
	cursor, err := cs.db.Collection("wg_clients").Find(context.Background(), bson.M{
		"server_id": serverID,
	})
	if err != nil {
		return "", "", err
	}
	defer cursor.Close(context.Background())

	usedIPs := make(map[string]bool)
	for cursor.Next(context.Background()) {
		var client WGClient
		if err := cursor.Decode(&client); err != nil {
			continue
		}
		usedIPs[client.IPv4Address] = true
	}

	// 分配新的 IP 地址
	ip := make(net.IP, len(ipv4Net.IP))
	copy(ip, ipv4Net.IP)

	for ip := ip.Mask(ipv4Net.Mask); ipv4Net.Contains(ip); inc(ip) {
		ipStr := ip.String()
		// 跳过网络地址、广播地址和网关地址
		if ip.Equal(ipv4Net.IP) || ip[3] == 1 || ip[3] == 255 {
			continue
		}
		if !usedIPs[ipStr] {
			ipv4 = ipStr
			break
		}
	}

	if ipv4 == "" {
		return "", "", fmt.Errorf("no available IPv4 addresses")
	}

	// 生成对应的 IPv6 地址
	if server.IPv6CIDR != "" {
		_, ipv6Net, err := net.ParseCIDR(server.IPv6CIDR)
		if err == nil {
			// 使用 IPv4 的最后一个字节作为 IPv6 的最后一个字节
			ipv6Addr := make(net.IP, 16)
			copy(ipv6Addr, ipv6Net.IP)
			ipv6Addr[15] = ip[3]
			ipv6 = ipv6Addr.String()
		}
	}

	return ipv4, ipv6, nil
}

// inc 递增 IP 地址
func inc(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// ReleaseIPAddress 释放 IP 地址
func (cs *configService) ReleaseIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID, ipv4, ipv6 string) error {
	// IP 地址会在客户端删除时自动释放，这里可以添加额外的清理逻辑
	return nil
}
