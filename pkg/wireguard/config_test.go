package wireguard

import (
	"fmt"
	"net"
	"testing"
)

func TestConfig_Validation(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &Config{
				Host:                "vpn.example.com",
				BasePort:            51820,
				MaxUsers:            1000,
				MaxServersPerUser:   5,
				MaxClientsPerServer: 100,
				BaseNetwork:         "********/16",
				DefaultDNS:          []string{"*******", "*******"},
				DefaultMTU:          1420,
				EncryptionKey:       "your-32-byte-encryption-key-here!!",
			},
			wantErr: false,
		},
		{
			name: "missing host",
			config: &Config{
				BasePort:            51820,
				MaxUsers:            1000,
				MaxServersPerUser:   5,
				MaxClientsPerServer: 100,
				BaseNetwork:         "********/16",
				DefaultDNS:          []string{"*******", "*******"},
				DefaultMTU:          1420,
				EncryptionKey:       "your-32-byte-encryption-key-here!!",
			},
			wantErr: true,
		},
		{
			name: "invalid base network",
			config: &Config{
				Host:                "vpn.example.com",
				BasePort:            51820,
				MaxUsers:            1000,
				MaxServersPerUser:   5,
				MaxClientsPerServer: 100,
				BaseNetwork:         "invalid-network",
				DefaultDNS:          []string{"*******", "*******"},
				DefaultMTU:          1420,
				EncryptionKey:       "your-32-byte-encryption-key-here!!",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateConfig(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCreateServerRequest_WithoutEndpoint(t *testing.T) {
	req := &CreateServerRequest{
		Name: "Test Server",
		DNS:  []string{"*******", "*******"},
		MTU:  1420,
	}

	// 验证请求结构体不包含 Endpoint 字段
	if req.Name == "" {
		t.Error("Name should not be empty")
	}

	if len(req.DNS) == 0 {
		t.Error("DNS should not be empty")
	}

	if req.MTU == 0 {
		t.Error("MTU should not be zero")
	}

	// 这个测试确保我们移除了 Endpoint 字段
	// 如果编译通过，说明结构体正确
}

// validateConfig 验证配置的辅助函数
func validateConfig(config *Config) error {
	if config.Host == "" {
		return fmt.Errorf("host is required")
	}

	if config.BaseNetwork == "" {
		return fmt.Errorf("base_network is required")
	}

	// 验证网络格式
	_, _, err := net.ParseCIDR(config.BaseNetwork)
	if err != nil {
		return fmt.Errorf("invalid base_network format: %w", err)
	}

	if config.EncryptionKey == "" {
		return fmt.Errorf("encryption_key is required")
	}

	if len(config.EncryptionKey) != 32 {
		return fmt.Errorf("encryption_key must be 32 bytes long")
	}

	return nil
}
