package wireguard

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// WireGuardManager WireGuard 管理器主接口
type WireGuardManager interface {
	// 服务器管理
	CreateUserServer(ctx context.Context, userID primitive.ObjectID, config *CreateServerRequest) (*UserWGServer, error)
	DeleteUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error
	GetUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) (*UserWGServer, error)
	ListUserServers(ctx context.Context, userID primitive.ObjectID) ([]*UserWGServer, error)
	UpdateUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID, updates *UpdateServerRequest) error

	// 客户端管理
	CreateClient(ctx context.Context, userID primitive.ObjectID, req *CreateClientRequest) (*WGClient, error)
	UpdateClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID, req *UpdateClientRequest) error
	DeleteClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
	GetClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*WGClient, error)
	ListClients(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) ([]*WGClient, error)

	// 配置管理
	GenerateClientConfig(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (string, error)
	GenerateQRCode(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) ([]byte, error)
	ApplyServerConfig(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error

	// 状态管理
	EnableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
	DisableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error
	GetClientStats(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*ClientStats, error)
}

// NamespaceManager 网络命名空间管理器
type NamespaceManager interface {
	CreateNamespace(userID primitive.ObjectID) error
	DeleteNamespace(userID primitive.ObjectID) error
	ExecInNamespace(userID primitive.ObjectID, cmd []string) ([]byte, error)
	SetupWireGuardInterface(userID primitive.ObjectID, config *NetworkConfig) error
	TeardownWireGuardInterface(userID primitive.ObjectID, interfaceName string) error
}

// ConfigService 配置服务
type ConfigService interface {
	GenerateKeys() (privateKey, publicKey string, err error)
	GeneratePreSharedKey() (string, error)
	AllocateIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID) (ipv4, ipv6 string, err error)
	ReleaseIPAddress(userID primitive.ObjectID, serverID primitive.ObjectID, ipv4, ipv6 string) error
}

// CacheService 缓存服务接口
type CacheService interface {
	CacheClientConfig(userID, clientID primitive.ObjectID, config string) error
	GetCachedClientConfig(userID, clientID primitive.ObjectID) (string, error)
	CacheClientStats(userID, clientID primitive.ObjectID, stats *ClientStats) error
	GetCachedClientStats(userID, clientID primitive.ObjectID) (*ClientStats, error)
	InvalidateUserCache(userID primitive.ObjectID) error
	SetUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) error
	IsUserOnline(userID primitive.ObjectID, clientID primitive.ObjectID) bool
}
