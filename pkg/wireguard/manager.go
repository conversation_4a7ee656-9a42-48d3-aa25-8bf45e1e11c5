package wireguard

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"net"
	"time"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.zx2c4.com/wireguard/wgctrl"
)

type wireGuardManager struct {
	db               *mongo.Database
	redis            *redis.Client
	config           *Config
	namespaceManager NamespaceManager
	configService    ConfigService
	cacheService     CacheService
	wgClient         *wgctrl.Client
	gcm              cipher.AEAD
}

// NewWireGuardManager 创建新的 WireGuard 管理器
func NewWireGuardManager(db *mongo.Database, redis *redis.Client, config *Config) (WireGuardManager, error) {
	wgClient, err := wgctrl.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create wireguard client: %w", err)
	}

	// 初始化加密
	block, err := aes.NewCipher([]byte(config.EncryptionKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	manager := &wireGuardManager{
		db:               db,
		redis:            redis,
		config:           config,
		namespaceManager: NewNamespaceManager(config.BasePort),
		configService:    NewConfigService(db, config),
		cacheService:     NewCacheService(redis),
		wgClient:         wgClient,
		gcm:              gcm,
	}

	return manager, nil
}

// CreateUserServer 创建用户服务器
func (wm *wireGuardManager) CreateUserServer(ctx context.Context, userID primitive.ObjectID, req *CreateServerRequest) (*UserWGServer, error) {
	// 检查用户服务器数量限制
	count, err := wm.db.Collection("wg_servers").CountDocuments(ctx, bson.M{"user_id": userID})
	if err != nil {
		return nil, err
	}
	if count >= int64(wm.config.MaxServersPerUser) {
		return nil, fmt.Errorf("maximum servers per user exceeded")
	}

	// 生成密钥对
	privateKey, publicKey, err := wm.configService.GenerateKeys()
	if err != nil {
		return nil, err
	}

	// 分配端口
	port := wm.config.BasePort + int(count) + 1

	// 分配网络段
	baseNetwork := wm.config.BaseNetwork
	_, _, err = net.ParseCIDR(baseNetwork)
	if err != nil {
		return nil, err
	}

	// 为用户分配一个 /24 子网
	userNetworkIndex := int(count) + 1
	ipv4CIDR := fmt.Sprintf("10.8.%d.0/24", userNetworkIndex)
	ipv6CIDR := fmt.Sprintf("fd42:42:%d::0/64", userNetworkIndex)

	// 加密私钥
	encryptedPrivateKey, err := wm.encrypt(privateKey)
	if err != nil {
		return nil, err
	}

	server := &UserWGServer{
		ID:         primitive.NewObjectID(),
		UserID:     userID,
		Name:       req.Name,
		Interface:  fmt.Sprintf("wg-user-%s", userID.Hex()[:8]),
		Namespace:  fmt.Sprintf("ns-user-%s", userID.Hex()[:8]),
		IPv4CIDR:   ipv4CIDR,
		IPv6CIDR:   ipv6CIDR,
		Port:       port,
		PrivateKey: encryptedPrivateKey,
		PublicKey:  publicKey,
		Endpoint:   wm.config.Host,
		DNS:        req.DNS,
		MTU:        req.MTU,
		Enabled:    true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if len(server.DNS) == 0 {
		server.DNS = wm.config.DefaultDNS
	}
	if server.MTU == 0 {
		server.MTU = wm.config.DefaultMTU
	}

	// 保存到数据库
	_, err = wm.db.Collection("wg_servers").InsertOne(ctx, server)
	if err != nil {
		return nil, err
	}

	// 创建命名空间
	if err := wm.namespaceManager.CreateNamespace(userID); err != nil {
		return nil, err
	}

	// 设置 WireGuard 接口
	networkConfig := &NetworkConfig{
		Interface:  server.Interface,
		PrivateKey: privateKey,
		IPv4CIDR:   server.IPv4CIDR,
		IPv6CIDR:   server.IPv6CIDR,
		Port:       server.Port,
	}

	if err := wm.namespaceManager.SetupWireGuardInterface(userID, networkConfig); err != nil {
		return nil, err
	}

	return server, nil
}

// encrypt 加密字符串
func (wm *wireGuardManager) encrypt(plaintext string) (string, error) {
	nonce := make([]byte, wm.gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := wm.gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt 解密字符串
func (wm *wireGuardManager) decrypt(ciphertext string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	nonceSize := wm.gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertextBytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := wm.gcm.Open(nil, nonce, ciphertextBytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
