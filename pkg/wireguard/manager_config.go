package wireguard

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/skip2/go-qrcode"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GenerateClientConfig 生成客户端配置
func (wm *wireGuardManager) GenerateClientConfig(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (string, error) {
	// 检查缓存
	if config, err := wm.cacheService.GetCachedClientConfig(userID, clientID); err == nil {
		return config, nil
	}

	// 获取客户端信息
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	if err != nil {
		return "", err
	}

	// 获取服务器信息
	var server UserWGServer
	err = wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     client.ServerID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return "", err
	}

	// 解密私钥
	privateKey, err := wm.decrypt(client.PrivateKey)
	if err != nil {
		return "", err
	}

	// 解密预共享密钥
	preSharedKey, err := wm.decrypt(client.PreSharedKey)
	if err != nil {
		return "", err
	}

	// 生成配置文件
	var configBuilder strings.Builder
	configBuilder.WriteString("[Interface]\n")
	configBuilder.WriteString(fmt.Sprintf("PrivateKey = %s\n", privateKey))
	configBuilder.WriteString(fmt.Sprintf("Address = %s", client.IPv4Address))
	if client.IPv6Address != "" {
		configBuilder.WriteString(fmt.Sprintf(", %s", client.IPv6Address))
	}
	configBuilder.WriteString("\n")

	if len(server.DNS) > 0 {
		configBuilder.WriteString(fmt.Sprintf("DNS = %s\n", strings.Join(server.DNS, ", ")))
	}

	if server.MTU > 0 {
		configBuilder.WriteString(fmt.Sprintf("MTU = %d\n", server.MTU))
	}

	configBuilder.WriteString("\n[Peer]\n")
	configBuilder.WriteString(fmt.Sprintf("PublicKey = %s\n", server.PublicKey))
	configBuilder.WriteString(fmt.Sprintf("PresharedKey = %s\n", preSharedKey))
	configBuilder.WriteString(fmt.Sprintf("Endpoint = %s:%d\n", server.Endpoint, server.Port))
	configBuilder.WriteString(fmt.Sprintf("AllowedIPs = %s\n", strings.Join(client.AllowedIPs, ", ")))

	if client.PersistentKeepalive > 0 {
		configBuilder.WriteString(fmt.Sprintf("PersistentKeepalive = %d\n", client.PersistentKeepalive))
	}

	config := configBuilder.String()

	// 缓存配置
	wm.cacheService.CacheClientConfig(userID, clientID, config)

	return config, nil
}

// GenerateQRCode 生成 QR 码
func (wm *wireGuardManager) GenerateQRCode(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) ([]byte, error) {
	config, err := wm.GenerateClientConfig(ctx, userID, clientID)
	if err != nil {
		return nil, err
	}

	return qrcode.Encode(config, qrcode.Medium, 256)
}

// ApplyServerConfig 应用服务器配置
func (wm *wireGuardManager) ApplyServerConfig(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error {
	// 获取服务器信息
	var server UserWGServer
	err := wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     serverID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return err
	}

	// 获取所有启用的客户端
	cursor, err := wm.db.Collection("wg_clients").Find(ctx, bson.M{
		"server_id": serverID,
		"user_id":   userID,
		"enabled":   true,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var clients []WGClient
	for cursor.Next(ctx) {
		var client WGClient
		if err := cursor.Decode(&client); err != nil {
			continue
		}
		clients = append(clients, client)
	}

	// 生成 WireGuard 配置
	var configBuilder strings.Builder

	// 添加每个客户端作为 peer
	for _, client := range clients {
		// 解密预共享密钥
		preSharedKey, err := wm.decrypt(client.PreSharedKey)
		if err != nil {
			continue
		}

		configBuilder.WriteString(fmt.Sprintf("wg set %s peer %s", server.Interface, client.PublicKey))
		configBuilder.WriteString(fmt.Sprintf(" preshared-key <(echo %s)", preSharedKey))
		configBuilder.WriteString(fmt.Sprintf(" allowed-ips %s", client.IPv4Address+"/32"))
		if client.IPv6Address != "" {
			configBuilder.WriteString(fmt.Sprintf(",%s/128", client.IPv6Address))
		}
		if client.PersistentKeepalive > 0 {
			configBuilder.WriteString(fmt.Sprintf(" persistent-keepalive %d", client.PersistentKeepalive))
		}
		configBuilder.WriteString("\n")
	}

	// 在命名空间中执行配置命令
	if configBuilder.Len() > 0 {
		commands := strings.Split(strings.TrimSpace(configBuilder.String()), "\n")
		for _, cmd := range commands {
			cmdParts := strings.Fields(cmd)
			if len(cmdParts) > 0 {
				_, err := wm.namespaceManager.ExecInNamespace(userID, cmdParts)
				if err != nil {
					return fmt.Errorf("failed to execute command %s: %w", cmd, err)
				}
			}
		}
	}

	return nil
}

// EnableClient 启用客户端
func (wm *wireGuardManager) EnableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error {
	// 获取客户端信息
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	if err != nil {
		return err
	}

	// 更新状态
	_, err = wm.db.Collection("wg_clients").UpdateOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}, bson.M{"$set": bson.M{"enabled": true}})
	if err != nil {
		return err
	}

	// 应用服务器配置
	if err := wm.ApplyServerConfig(ctx, userID, client.ServerID); err != nil {
		return err
	}

	// 清除缓存
	return wm.cacheService.InvalidateUserCache(userID)
}

// DisableClient 禁用客户端
func (wm *wireGuardManager) DisableClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error {
	// 获取客户端信息
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	if err != nil {
		return err
	}

	// 更新状态
	_, err = wm.db.Collection("wg_clients").UpdateOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}, bson.M{"$set": bson.M{"enabled": false}})
	if err != nil {
		return err
	}

	// 从 WireGuard 接口移除 peer
	var server UserWGServer
	err = wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     client.ServerID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return err
	}

	// 移除 peer
	cmd := []string{"wg", "set", server.Interface, "peer", client.PublicKey, "remove"}
	_, err = wm.namespaceManager.ExecInNamespace(userID, cmd)
	if err != nil {
		return err
	}

	// 清除缓存
	return wm.cacheService.InvalidateUserCache(userID)
}

// GetClientStats 获取客户端统计信息
func (wm *wireGuardManager) GetClientStats(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*ClientStats, error) {
	// 检查缓存
	if stats, err := wm.cacheService.GetCachedClientStats(userID, clientID); err == nil {
		return stats, nil
	}

	// 获取客户端信息
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	if err != nil {
		return nil, err
	}

	// 获取服务器信息
	var server UserWGServer
	err = wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     client.ServerID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return nil, err
	}

	// 获取 WireGuard 统计信息
	cmd := []string{"wg", "show", server.Interface, "dump"}
	output, err := wm.namespaceManager.ExecInNamespace(userID, cmd)
	if err != nil {
		return nil, err
	}

	// 解析输出
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 6 && fields[0] == client.PublicKey {
			// 解析统计信息
			rxBytes, _ := strconv.ParseInt(fields[5], 10, 64)
			txBytes, _ := strconv.ParseInt(fields[6], 10, 64)

			stats := &ClientStats{
				ClientID:  clientID,
				RxBytes:   rxBytes,
				TxBytes:   txBytes,
				IsOnline:  wm.cacheService.IsUserOnline(userID, clientID),
				Timestamp: time.Now(),
			}

			// 缓存统计信息
			wm.cacheService.CacheClientStats(userID, clientID, stats)

			return stats, nil
		}
	}

	// 如果没有找到统计信息，返回默认值
	stats := &ClientStats{
		ClientID:  clientID,
		RxBytes:   0,
		TxBytes:   0,
		IsOnline:  false,
		Timestamp: time.Now(),
	}

	return stats, nil
}
