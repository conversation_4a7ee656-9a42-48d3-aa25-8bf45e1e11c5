package wireguard

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DeleteUserServer 删除用户服务器
func (wm *wireGuardManager) DeleteUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) error {
	// 获取服务器信息
	var server UserWGServer
	err := wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     serverID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return err
	}

	// 删除所有客户端
	_, err = wm.db.Collection("wg_clients").DeleteMany(ctx, bson.M{
		"server_id": serverID,
		"user_id":   userID,
	})
	if err != nil {
		return err
	}

	// 删除服务器
	_, err = wm.db.Collection("wg_servers").DeleteOne(ctx, bson.M{
		"_id":     serverID,
		"user_id": userID,
	})
	if err != nil {
		return err
	}

	// 拆除 WireGuard 接口
	if err := wm.namespaceManager.TeardownWireGuardInterface(userID, server.Interface); err != nil {
		return err
	}

	// 删除命名空间
	if err := wm.namespaceManager.DeleteNamespace(userID); err != nil {
		return err
	}

	// 清除缓存
	return wm.cacheService.InvalidateUserCache(userID)
}

// GetUserServer 获取用户服务器
func (wm *wireGuardManager) GetUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) (*UserWGServer, error) {
	var server UserWGServer
	err := wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     serverID,
		"user_id": userID,
	}).Decode(&server)
	return &server, err
}

// ListUserServers 列出用户服务器
func (wm *wireGuardManager) ListUserServers(ctx context.Context, userID primitive.ObjectID) ([]*UserWGServer, error) {
	cursor, err := wm.db.Collection("wg_servers").Find(ctx, bson.M{"user_id": userID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var servers []*UserWGServer
	for cursor.Next(ctx) {
		var server UserWGServer
		if err := cursor.Decode(&server); err != nil {
			continue
		}
		servers = append(servers, &server)
	}

	return servers, nil
}

// UpdateUserServer 更新用户服务器
func (wm *wireGuardManager) UpdateUserServer(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID, updates *UpdateServerRequest) error {
	updateDoc := bson.M{"updated_at": time.Now()}

	if updates.Name != nil {
		updateDoc["name"] = *updates.Name
	}
	if updates.DNS != nil {
		updateDoc["dns"] = *updates.DNS
	}
	if updates.MTU != nil {
		updateDoc["mtu"] = *updates.MTU
	}
	if updates.Enabled != nil {
		updateDoc["enabled"] = *updates.Enabled
	}

	_, err := wm.db.Collection("wg_servers").UpdateOne(ctx, bson.M{
		"_id":     serverID,
		"user_id": userID,
	}, bson.M{"$set": updateDoc})

	if err == nil {
		// 清除缓存
		wm.cacheService.InvalidateUserCache(userID)
	}

	return err
}

// CreateClient 创建客户端
func (wm *wireGuardManager) CreateClient(ctx context.Context, userID primitive.ObjectID, req *CreateClientRequest) (*WGClient, error) {
	// 检查服务器是否存在
	var server UserWGServer
	err := wm.db.Collection("wg_servers").FindOne(ctx, bson.M{
		"_id":     req.ServerID,
		"user_id": userID,
	}).Decode(&server)
	if err != nil {
		return nil, err
	}

	// 检查客户端数量限制
	count, err := wm.db.Collection("wg_clients").CountDocuments(ctx, bson.M{
		"server_id": req.ServerID,
		"user_id":   userID,
	})
	if err != nil {
		return nil, err
	}
	if count >= int64(wm.config.MaxClientsPerServer) {
		return nil, fmt.Errorf("maximum clients per server exceeded")
	}

	// 生成密钥对
	privateKey, publicKey, err := wm.configService.GenerateKeys()
	if err != nil {
		return nil, err
	}

	// 生成预共享密钥
	preSharedKey, err := wm.configService.GeneratePreSharedKey()
	if err != nil {
		return nil, err
	}

	// 分配 IP 地址
	ipv4, ipv6, err := wm.configService.AllocateIPAddress(userID, req.ServerID)
	if err != nil {
		return nil, err
	}

	// 加密私钥和预共享密钥
	encryptedPrivateKey, err := wm.encrypt(privateKey)
	if err != nil {
		return nil, err
	}

	encryptedPreSharedKey, err := wm.encrypt(preSharedKey)
	if err != nil {
		return nil, err
	}

	client := &WGClient{
		ID:                  primitive.NewObjectID(),
		UserID:              userID,
		ServerID:            req.ServerID,
		Name:                req.Name,
		IPv4Address:         ipv4,
		IPv6Address:         ipv6,
		PrivateKey:          encryptedPrivateKey,
		PublicKey:           publicKey,
		PreSharedKey:        encryptedPreSharedKey,
		AllowedIPs:          req.AllowedIPs,
		PersistentKeepalive: req.PersistentKeepalive,
		Enabled:             true,
		ExpiresAt:           req.ExpiresAt,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	if len(client.AllowedIPs) == 0 {
		client.AllowedIPs = []string{"0.0.0.0/0", "::/0"}
	}

	// 保存到数据库
	_, err = wm.db.Collection("wg_clients").InsertOne(ctx, client)
	if err != nil {
		return nil, err
	}

	// 应用服务器配置
	if err := wm.ApplyServerConfig(ctx, userID, req.ServerID); err != nil {
		return nil, err
	}

	return client, nil
}

// UpdateClient 更新客户端
func (wm *wireGuardManager) UpdateClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID, req *UpdateClientRequest) error {
	updateDoc := bson.M{"updated_at": time.Now()}

	if req.Name != nil {
		updateDoc["name"] = *req.Name
	}
	if req.AllowedIPs != nil {
		updateDoc["allowed_ips"] = *req.AllowedIPs
	}
	if req.PersistentKeepalive != nil {
		updateDoc["persistent_keepalive"] = *req.PersistentKeepalive
	}
	if req.Enabled != nil {
		updateDoc["enabled"] = *req.Enabled
	}
	if req.ExpiresAt != nil {
		updateDoc["expires_at"] = *req.ExpiresAt
	}

	_, err := wm.db.Collection("wg_clients").UpdateOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}, bson.M{"$set": updateDoc})

	if err == nil {
		// 清除缓存
		wm.cacheService.InvalidateUserCache(userID)
	}

	return err
}

// DeleteClient 删除客户端
func (wm *wireGuardManager) DeleteClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) error {
	// 获取客户端信息
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	if err != nil {
		return err
	}

	// 删除客户端
	_, err = wm.db.Collection("wg_clients").DeleteOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	})
	if err != nil {
		return err
	}

	// 释放 IP 地址
	if err := wm.configService.ReleaseIPAddress(userID, client.ServerID, client.IPv4Address, client.IPv6Address); err != nil {
		return err
	}

	// 应用服务器配置
	if err := wm.ApplyServerConfig(ctx, userID, client.ServerID); err != nil {
		return err
	}

	// 清除缓存
	return wm.cacheService.InvalidateUserCache(userID)
}

// GetClient 获取客户端
func (wm *wireGuardManager) GetClient(ctx context.Context, userID primitive.ObjectID, clientID primitive.ObjectID) (*WGClient, error) {
	var client WGClient
	err := wm.db.Collection("wg_clients").FindOne(ctx, bson.M{
		"_id":     clientID,
		"user_id": userID,
	}).Decode(&client)
	return &client, err
}

// ListClients 列出客户端
func (wm *wireGuardManager) ListClients(ctx context.Context, userID primitive.ObjectID, serverID primitive.ObjectID) ([]*WGClient, error) {
	filter := bson.M{"user_id": userID}
	if !serverID.IsZero() {
		filter["server_id"] = serverID
	}

	cursor, err := wm.db.Collection("wg_clients").Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var clients []*WGClient
	for cursor.Next(ctx) {
		var client WGClient
		if err := cursor.Decode(&client); err != nil {
			continue
		}
		clients = append(clients, &client)
	}

	return clients, nil
}
