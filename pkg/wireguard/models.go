package wireguard

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserWGServer 用户 WireGuard 服务器配置
type UserWGServer struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	UserID     primitive.ObjectID `bson:"user_id" json:"user_id" binding:"required"`
	Name       string             `bson:"name" json:"name" binding:"required"`
	Interface  string             `bson:"interface" json:"interface"` // wg-user1
	Namespace  string             `bson:"namespace" json:"namespace"` // ns-user1
	IPv4CIDR   string             `bson:"ipv4_cidr" json:"ipv4_cidr"` // ********/24
	IPv6CIDR   string             `bson:"ipv6_cidr" json:"ipv6_cidr"` // fd42:42:1::0/64
	Port       int                `bson:"port" json:"port"`           // 51821, 51822...
	PrivateKey string             `bson:"private_key" json:"-"`       // 加密存储
	PublicKey  string             `bson:"public_key" json:"public_key"`
	Endpoint   string             `bson:"endpoint" json:"endpoint"` // your-server.com
	DNS        []string           `bson:"dns" json:"dns"`
	MTU        int                `bson:"mtu" json:"mtu"`
	Enabled    bool               `bson:"enabled" json:"enabled"`
	CreatedAt  time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt  time.Time          `bson:"updated_at" json:"updated_at"`
}

// WGClient WireGuard 客户端配置
type WGClient struct {
	ID                  primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	UserID              primitive.ObjectID `bson:"user_id" json:"user_id"`
	ServerID            primitive.ObjectID `bson:"server_id" json:"server_id"`
	Name                string             `bson:"name" json:"name" binding:"required"`
	IPv4Address         string             `bson:"ipv4_address" json:"ipv4_address"`
	IPv6Address         string             `bson:"ipv6_address" json:"ipv6_address"`
	PrivateKey          string             `bson:"private_key" json:"-"`
	PublicKey           string             `bson:"public_key" json:"public_key"`
	PreSharedKey        string             `bson:"pre_shared_key" json:"-"`
	AllowedIPs          []string           `bson:"allowed_ips" json:"allowed_ips"`
	PersistentKeepalive int                `bson:"persistent_keepalive" json:"persistent_keepalive"`
	Enabled             bool               `bson:"enabled" json:"enabled"`
	ExpiresAt           *time.Time         `bson:"expires_at,omitempty" json:"expires_at,omitempty"`
	LastHandshake       *time.Time         `bson:"last_handshake,omitempty" json:"last_handshake,omitempty"`
	TransferRx          int64              `bson:"transfer_rx" json:"transfer_rx"`
	TransferTx          int64              `bson:"transfer_tx" json:"transfer_tx"`
	CreatedAt           time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt           time.Time          `bson:"updated_at" json:"updated_at"`
}

// ClientStats 统计信息
type ClientStats struct {
	ClientID      primitive.ObjectID `bson:"client_id" json:"client_id"`
	RxBytes       int64              `bson:"rx_bytes" json:"rx_bytes"`
	TxBytes       int64              `bson:"tx_bytes" json:"tx_bytes"`
	LastHandshake *time.Time         `bson:"last_handshake" json:"last_handshake"`
	IsOnline      bool               `bson:"is_online" json:"is_online"`
	Timestamp     time.Time          `bson:"timestamp" json:"timestamp"`
}

// CreateServerRequest 创建服务器请求
type CreateServerRequest struct {
	Name string   `json:"name" binding:"required,min=1,max=50"`
	DNS  []string `json:"dns"`
	MTU  int      `json:"mtu" binding:"min=1280,max=9000"`
}

// UpdateServerRequest 更新服务器请求
type UpdateServerRequest struct {
	Name    *string   `json:"name,omitempty"`
	DNS     *[]string `json:"dns,omitempty"`
	MTU     *int      `json:"mtu,omitempty"`
	Enabled *bool     `json:"enabled,omitempty"`
}

// CreateClientRequest 创建客户端请求
type CreateClientRequest struct {
	ServerID            primitive.ObjectID `json:"server_id" binding:"required"`
	Name                string             `json:"name" binding:"required,min=1,max=50"`
	AllowedIPs          []string           `json:"allowed_ips"`
	PersistentKeepalive int                `json:"persistent_keepalive" binding:"min=0,max=65535"`
	ExpiresAt           *time.Time         `json:"expires_at,omitempty"`
}

// UpdateClientRequest 更新客户端请求
type UpdateClientRequest struct {
	Name                *string    `json:"name,omitempty"`
	AllowedIPs          *[]string  `json:"allowed_ips,omitempty"`
	PersistentKeepalive *int       `json:"persistent_keepalive,omitempty"`
	Enabled             *bool      `json:"enabled,omitempty"`
	ExpiresAt           *time.Time `json:"expires_at,omitempty"`
}

// NetworkConfig 网络配置
type NetworkConfig struct {
	Interface  string
	PrivateKey string
	IPv4CIDR   string
	IPv6CIDR   string
	Port       int
}

// Config WireGuard 配置
type Config struct {
	Host                string   `yaml:"host"` // WireGuard服务器的公网域名或IP
	BasePort            int      `yaml:"base_port"`
	MaxUsers            int      `yaml:"max_users"`
	MaxServersPerUser   int      `yaml:"max_servers_per_user"`
	MaxClientsPerServer int      `yaml:"max_clients_per_server"`
	BaseNetwork         string   `yaml:"base_network"`
	DefaultDNS          []string `yaml:"default_dns"`
	DefaultMTU          int      `yaml:"default_mtu"`
	EncryptionKey       string   `yaml:"encryption_key"`
}
