package wireguard

import (
	"fmt"
	"os/exec"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type namespaceManager struct {
	basePort int
}

// NewNamespaceManager 创建新的命名空间管理器
func NewNamespaceManager(basePort int) NamespaceManager {
	return &namespaceManager{basePort: basePort}
}

// CreateNamespace 创建网络命名空间
func (nm *namespaceManager) CreateNamespace(userID primitive.ObjectID) error {
	namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])

	// 1. 创建网络命名空间
	cmd := exec.Command("ip", "netns", "add", namespace)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to create namespace %s: %w", namespace, err)
	}

	// 2. 启用 loopback 接口
	cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "link", "set", "lo", "up")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to enable loopback in namespace %s: %w", namespace, err)
	}

	return nil
}

// DeleteNamespace 删除网络命名空间
func (nm *namespaceManager) DeleteNamespace(userID primitive.ObjectID) error {
	namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])
	cmd := exec.Command("ip", "netns", "del", namespace)
	return cmd.Run()
}

// ExecInNamespace 在命名空间中执行命令
func (nm *namespaceManager) ExecInNamespace(userID primitive.ObjectID, cmd []string) ([]byte, error) {
	namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])
	args := append([]string{"netns", "exec", namespace}, cmd...)
	execCmd := exec.Command("ip", args...)
	return execCmd.Output()
}

// SetupWireGuardInterface 设置 WireGuard 接口
func (nm *namespaceManager) SetupWireGuardInterface(userID primitive.ObjectID, config *NetworkConfig) error {
	namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])

	// 1. 创建 WireGuard 接口
	cmd := exec.Command("ip", "netns", "exec", namespace, "ip", "link", "add", "dev", config.Interface, "type", "wireguard")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to create interface: %w", err)
	}

	// 2. 设置私钥
	cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "set", config.Interface, "private-key", "/dev/stdin")
	cmd.Stdin = strings.NewReader(config.PrivateKey)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set private key: %w", err)
	}

	// 3. 配置 IP 地址
	cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "addr", "add", config.IPv4CIDR, "dev", config.Interface)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set IPv4 address: %w", err)
	}

	if config.IPv6CIDR != "" {
		cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "addr", "add", config.IPv6CIDR, "dev", config.Interface)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("failed to set IPv6 address: %w", err)
		}
	}

	// 4. 启用接口
	cmd = exec.Command("ip", "netns", "exec", namespace, "ip", "link", "set", config.Interface, "up")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to bring up interface: %w", err)
	}

	// 5. 设置监听端口
	cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "set", config.Interface, "listen-port", strconv.Itoa(config.Port))
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set listen port: %w", err)
	}

	// 6. 设置防火墙规则（网络隔离）
	if err := nm.setupFirewallRules(namespace, config); err != nil {
		return fmt.Errorf("failed to setup firewall rules: %w", err)
	}

	return nil
}

// TeardownWireGuardInterface 拆除 WireGuard 接口
func (nm *namespaceManager) TeardownWireGuardInterface(userID primitive.ObjectID, interfaceName string) error {
	namespace := fmt.Sprintf("ns-user-%s", userID.Hex()[:8])

	// 删除接口
	cmd := exec.Command("ip", "netns", "exec", namespace, "ip", "link", "del", interfaceName)
	return cmd.Run()
}

// setupFirewallRules 设置防火墙规则
func (nm *namespaceManager) setupFirewallRules(namespace string, config *NetworkConfig) error {
	// 允许 WireGuard 流量
	cmd := exec.Command("ip", "netns", "exec", namespace, "iptables", "-A", "INPUT", "-i", config.Interface, "-j", "ACCEPT")
	if err := cmd.Run(); err != nil {
		return err
	}

	cmd = exec.Command("ip", "netns", "exec", namespace, "iptables", "-A", "OUTPUT", "-o", config.Interface, "-j", "ACCEPT")
	if err := cmd.Run(); err != nil {
		return err
	}

	// 启用 NAT
	cmd = exec.Command("ip", "netns", "exec", namespace, "iptables", "-t", "nat", "-A", "POSTROUTING", "-s", config.IPv4CIDR, "-j", "MASQUERADE")
	return cmd.Run()
}
