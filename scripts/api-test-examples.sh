#!/bin/bash

# WireGuard API 测试示例脚本
# 基于API文档的完整测试用例

set -e

# 配置
SERVER_URL="http://localhost:8080"
API_BASE="$SERVER_URL/api/v1/wireguard"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请安装: sudo apt install jq"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 等待服务器启动
wait_for_server() {
    log_info "等待服务器启动..."
    
    for i in {1..30}; do
        if curl -s "$SERVER_URL/health" > /dev/null 2>&1; then
            log_success "服务器已启动"
            return 0
        fi
        if [ $i -eq 30 ]; then
            log_error "服务器启动超时"
            exit 1
        fi
        sleep 1
    done
}

# 获取认证令牌
get_auth_token() {
    log_info "获取认证令牌..."
    
    local login_response=$(curl -s -X POST "$SERVER_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "admin123"
        }')
    
    TOKEN=$(echo $login_response | jq -r '.data.access_token // empty')
    
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        log_error "无法获取认证令牌"
        echo "登录响应: $login_response"
        exit 1
    fi
    
    log_success "成功获取认证令牌"
}

# 测试创建服务器
test_create_server() {
    log_info "测试创建WireGuard服务器..."
    
    local response=$(curl -s -X POST "$API_BASE/servers" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "API Test Server",
            "dns": ["1.1.1.1", "8.8.8.8"],
            "mtu": 1420
        }')
    
    echo "创建服务器响应:"
    echo $response | jq '.'
    
    SERVER_ID=$(echo $response | jq -r '.data.id // empty')
    
    if [ -z "$SERVER_ID" ] || [ "$SERVER_ID" = "null" ]; then
        log_warning "服务器创建可能失败"
        return 1
    else
        log_success "服务器创建成功，ID: $SERVER_ID"
        return 0
    fi
}

# 测试获取服务器列表
test_list_servers() {
    log_info "测试获取服务器列表..."
    
    local response=$(curl -s -X GET "$API_BASE/servers?page=1&limit=10" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "服务器列表响应:"
    echo $response | jq '.'
    
    local status=$(echo $response | jq -r '.status // empty')
    if [ "$status" = "success" ]; then
        log_success "获取服务器列表成功"
        return 0
    else
        log_error "获取服务器列表失败"
        return 1
    fi
}

# 测试获取服务器详情
test_get_server() {
    if [ -z "$SERVER_ID" ] || [ "$SERVER_ID" = "null" ]; then
        log_warning "跳过服务器详情测试（无有效服务器ID）"
        return 1
    fi
    
    log_info "测试获取服务器详情..."
    
    local response=$(curl -s -X GET "$API_BASE/servers/$SERVER_ID" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "服务器详情响应:"
    echo $response | jq '.'
    
    local status=$(echo $response | jq -r '.status // empty')
    if [ "$status" = "success" ]; then
        log_success "获取服务器详情成功"
        return 0
    else
        log_error "获取服务器详情失败"
        return 1
    fi
}

# 测试创建客户端
test_create_client() {
    if [ -z "$SERVER_ID" ] || [ "$SERVER_ID" = "null" ]; then
        log_warning "跳过客户端创建测试（无有效服务器ID）"
        return 1
    fi
    
    log_info "测试创建客户端..."
    
    local response=$(curl -s -X POST "$API_BASE/clients" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"server_id\": \"$SERVER_ID\",
            \"name\": \"API Test Client\",
            \"email\": \"<EMAIL>\"
        }")
    
    echo "创建客户端响应:"
    echo $response | jq '.'
    
    CLIENT_ID=$(echo $response | jq -r '.data.id // empty')
    
    if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" = "null" ]; then
        log_warning "客户端创建可能失败"
        return 1
    else
        log_success "客户端创建成功，ID: $CLIENT_ID"
        return 0
    fi
}

# 测试获取客户端列表
test_list_clients() {
    log_info "测试获取客户端列表..."
    
    local url="$API_BASE/clients?page=1&limit=10"
    if [ ! -z "$SERVER_ID" ] && [ "$SERVER_ID" != "null" ]; then
        url="$url&server_id=$SERVER_ID"
    fi
    
    local response=$(curl -s -X GET "$url" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "客户端列表响应:"
    echo $response | jq '.'
    
    local status=$(echo $response | jq -r '.status // empty')
    if [ "$status" = "success" ]; then
        log_success "获取客户端列表成功"
        return 0
    else
        log_error "获取客户端列表失败"
        return 1
    fi
}

# 测试获取客户端配置
test_get_client_config() {
    if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" = "null" ]; then
        log_warning "跳过客户端配置测试（无有效客户端ID）"
        return 1
    fi
    
    log_info "测试获取客户端配置..."
    
    local response=$(curl -s -X GET "$API_BASE/configs/clients/$CLIENT_ID" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "客户端配置响应:"
    echo $response | jq '.'
    
    local status=$(echo $response | jq -r '.status // empty')
    if [ "$status" = "success" ]; then
        log_success "获取客户端配置成功"
        return 0
    else
        log_error "获取客户端配置失败"
        return 1
    fi
}

# 测试获取QR码
test_get_qr_code() {
    if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" = "null" ]; then
        log_warning "跳过QR码测试（无有效客户端ID）"
        return 1
    fi
    
    log_info "测试获取QR码..."
    
    local response=$(curl -s -X GET "$API_BASE/configs/clients/$CLIENT_ID/qr" \
        -H "Authorization: Bearer $TOKEN")
    
    # 不打印完整响应（QR码数据太长）
    local status=$(echo $response | jq -r '.status // empty')
    local has_qr=$(echo $response | jq -r '.data.qr_code // empty' | grep -q "data:image" && echo "yes" || echo "no")
    
    if [ "$status" = "success" ] && [ "$has_qr" = "yes" ]; then
        log_success "获取QR码成功"
        return 0
    else
        log_error "获取QR码失败"
        echo "响应状态: $status"
        return 1
    fi
}

# 测试客户端统计
test_get_client_stats() {
    if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" = "null" ]; then
        log_warning "跳过客户端统计测试（无有效客户端ID）"
        return 1
    fi
    
    log_info "测试获取客户端统计..."
    
    local response=$(curl -s -X GET "$API_BASE/stats/clients/$CLIENT_ID" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "客户端统计响应:"
    echo $response | jq '.'
    
    local status=$(echo $response | jq -r '.status // empty')
    if [ "$status" = "success" ]; then
        log_success "获取客户端统计成功"
        return 0
    else
        log_error "获取客户端统计失败"
        return 1
    fi
}

# 主函数
main() {
    echo "🧪 WireGuard API 测试开始..."
    echo "================================"
    
    # 初始化
    check_dependencies
    wait_for_server
    get_auth_token
    
    # 测试计数器
    local total_tests=0
    local passed_tests=0
    
    # 执行测试
    echo ""
    echo "📋 执行API测试..."
    echo "--------------------------------"
    
    # 服务器管理测试
    total_tests=$((total_tests + 1))
    if test_create_server; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    total_tests=$((total_tests + 1))
    if test_list_servers; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    total_tests=$((total_tests + 1))
    if test_get_server; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    # 客户端管理测试
    total_tests=$((total_tests + 1))
    if test_create_client; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    total_tests=$((total_tests + 1))
    if test_list_clients; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    # 配置管理测试
    total_tests=$((total_tests + 1))
    if test_get_client_config; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    total_tests=$((total_tests + 1))
    if test_get_qr_code; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    # 统计信息测试
    total_tests=$((total_tests + 1))
    if test_get_client_stats; then
        passed_tests=$((passed_tests + 1))
    fi
    echo ""
    
    # 测试总结
    echo "📊 测试总结"
    echo "================================"
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $((total_tests - passed_tests))"
    echo "成功率: $(( passed_tests * 100 / total_tests ))%"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "🎉 所有测试通过！"
        exit 0
    else
        log_warning "⚠️ 部分测试失败，请检查服务器配置和权限"
        exit 1
    fi
}

# 运行主函数
main "$@"
