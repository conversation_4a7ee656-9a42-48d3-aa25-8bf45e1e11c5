#!/bin/bash

# MQTT预警系统API测试脚本
# 使用前请确保：
# 1. 服务器正在运行
# 2. 已获取有效的JWT token
# 3. MongoDB和TDengine服务正常运行

# 配置
BASE_URL="http://localhost:8080"
JWT_TOKEN="YOUR_JWT_TOKEN_HERE"  # 请替换为实际的JWT token

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== MQTT预警系统API测试脚本 ===${NC}"
echo

# 检查JWT token是否设置
if [ "$JWT_TOKEN" = "YOUR_JWT_TOKEN_HERE" ]; then
    echo -e "${RED}错误: 请先设置有效的JWT_TOKEN${NC}"
    echo "请编辑脚本并替换 JWT_TOKEN 变量的值"
    exit 1
fi

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local description=$3
    local data=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "请求体: $data"
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X "$method" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X "$method" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json" \
            "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | sed '$d')
    
    echo "响应码: $http_code"
    echo "响应体:"
    echo "$body" | jq . 2>/dev/null || echo "$body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    echo "----------------------------------------"
}

# 1. 创建预警规则 - 温度过高预警
echo -e "${YELLOW}=== 预警规则管理测试 ===${NC}"

create_rule_data='{
  "group_id": "test_group",
  "topic": "sensor/temperature",
  "rule_name": "温度过高预警",
  "rule_type": "threshold",
  "description": "当温度超过35度时触发预警",
  "conditions": [
    {
      "field": "temperature",
      "operator": ">",
      "value": "35",
      "data_type": "number"
    }
  ],
  "level": 3,
  "frequency_limit": {
    "max_count": 3,
    "time_window": 3600
  },
  "notification": {
    "enabled": true,
    "channels": ["email"],
    "recipients": ["<EMAIL>"]
  },
  "enabled": true
}'

test_api "POST" \
    "$BASE_URL/api/groups/alert-rules" \
    "创建预警规则 - 温度过高预警" \
    "$create_rule_data"

# 2. 获取预警规则列表
test_api "GET" \
    "$BASE_URL/api/groups/alert-rules?group_id=test_group" \
    "获取预警规则列表"

# 3. 创建另一个预警规则 - 湿度过低预警
create_rule_data2='{
  "group_id": "test_group",
  "topic": "sensor/humidity",
  "rule_name": "湿度过低预警",
  "rule_type": "threshold",
  "description": "当湿度低于30%时触发预警",
  "conditions": [
    {
      "field": "humidity",
      "operator": "<",
      "value": "30",
      "data_type": "number"
    }
  ],
  "level": 2,
  "frequency_limit": {
    "max_count": 5,
    "time_window": 1800
  },
  "notification": {
    "enabled": false,
    "channels": [],
    "recipients": []
  },
  "enabled": true
}'

test_api "POST" \
    "$BASE_URL/api/groups/alert-rules" \
    "创建预警规则 - 湿度过低预警" \
    "$create_rule_data2"

# 4. 搜索预警规则
test_api "GET" \
    "$BASE_URL/api/groups/alert-rules?search=温度" \
    "搜索预警规则 - 关键词: 温度"

# 5. 按级别过滤预警规则
test_api "GET" \
    "$BASE_URL/api/groups/alert-rules?level=3" \
    "按级别过滤预警规则 - 级别3"

# 6. 测试预警规则（需要先获取规则ID）
echo -e "${YELLOW}=== 预警规则测试 ===${NC}"

# 获取第一个规则的ID（这里简化处理，实际使用中需要解析JSON）
echo "注意: 测试预警规则需要先获取规则ID，请手动替换下面的rule_id"

test_rule_data='{
  "temperature": 40,
  "humidity": 60
}'

# 这里使用占位符，实际测试时需要替换为真实的规则ID
test_api "POST" \
    "$BASE_URL/api/groups/alert-rules/RULE_ID_PLACEHOLDER/test" \
    "测试预警规则 - 温度40度" \
    "$test_rule_data"

# 7. 预警记录查询测试
echo -e "${YELLOW}=== 预警记录查询测试 ===${NC}"

test_api "GET" \
    "$BASE_URL/api/groups/alert-records?group_id=test_group" \
    "获取预警记录列表"

# 8. 按时间范围查询预警记录
start_time=$(date -u -d '1 day ago' +%Y-%m-%dT%H:%M:%SZ)
end_time=$(date -u +%Y-%m-%dT%H:%M:%SZ)

test_api "GET" \
    "$BASE_URL/api/groups/alert-records?start_time=$start_time&end_time=$end_time" \
    "按时间范围查询预警记录"

# 9. 按级别查询预警记录
test_api "GET" \
    "$BASE_URL/api/groups/alert-records?level=3" \
    "按级别查询预警记录 - 级别3"

# 10. 预警统计测试
echo -e "${YELLOW}=== 预警统计测试 ===${NC}"

test_api "GET" \
    "$BASE_URL/api/groups/alert-statistics" \
    "获取预警统计信息"

test_api "GET" \
    "$BASE_URL/api/groups/alert-statistics?group_id=test_group" \
    "获取指定分组的预警统计信息"

# 11. 更新预警规则测试
echo -e "${YELLOW}=== 预警规则更新测试 ===${NC}"

update_rule_data='{
  "description": "更新后的描述：当温度超过35度时触发预警",
  "enabled": false
}'

test_api "PUT" \
    "$BASE_URL/api/groups/alert-rules/RULE_ID_PLACEHOLDER" \
    "更新预警规则 - 禁用规则" \
    "$update_rule_data"

# 12. 错误测试
echo -e "${YELLOW}=== 错误处理测试 ===${NC}"

# 测试无效的预警条件
invalid_rule_data='{
  "group_id": "test_group",
  "topic": "sensor/temperature",
  "rule_name": "无效规则",
  "rule_type": "threshold",
  "conditions": [],
  "level": 3,
  "enabled": true
}'

test_api "POST" \
    "$BASE_URL/api/groups/alert-rules" \
    "创建无效预警规则 - 空条件(应该失败)" \
    "$invalid_rule_data"

# 测试获取不存在的规则
test_api "GET" \
    "$BASE_URL/api/groups/alert-rules/nonexistent_rule_id" \
    "获取不存在的预警规则(应该失败)"

echo -e "${YELLOW}=== 测试完成 ===${NC}"
echo
echo "注意事项："
echo "1. 某些测试需要替换RULE_ID_PLACEHOLDER为实际的规则ID"
echo "2. 预警记录查询可能返回空结果，这是正常的"
echo "3. 确保JWT token有足够的权限访问这些API"
echo "4. 如果遇到503错误，请检查MongoDB和TDengine服务是否正常运行"
echo "5. 预警引擎需要MQTT消息触发才会产生预警记录"
