#!/bin/bash

# MQTT预警集成测试脚本
# 测试MQTT消息发布时是否能正确触发预警检测

# 配置
BASE_URL="http://localhost:8080"
MQTT_HOST="localhost"
MQTT_PORT="1883"
JWT_TOKEN="YOUR_JWT_TOKEN_HERE"  # 请替换为实际的JWT token
TEST_USER="test_user_123"        # 测试用户ID
TEST_PASSWORD="test_password"    # 测试密码

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== MQTT预警集成测试脚本 ===${NC}"
echo

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖工具...${NC}"
    
    if ! command -v mosquitto_pub &> /dev/null; then
        echo -e "${RED}错误: mosquitto_pub 未安装${NC}"
        echo "请安装 mosquitto-clients: sudo apt-get install mosquitto-clients"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}错误: curl 未安装${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}错误: jq 未安装${NC}"
        echo "请安装 jq: sudo apt-get install jq"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 检查JWT token
check_token() {
    if [ "$JWT_TOKEN" = "YOUR_JWT_TOKEN_HERE" ]; then
        echo -e "${RED}错误: 请先设置有效的JWT_TOKEN${NC}"
        echo "请编辑脚本并替换 JWT_TOKEN 变量的值"
        exit 1
    fi
}

# 测试API调用
test_api() {
    local method=$1
    local url=$2
    local description=$3
    local data=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X "$method" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X "$method" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json" \
            "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✓ API调用成功 (HTTP $http_code)${NC}"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        return 0
    else
        echo -e "${RED}✗ API调用失败 (HTTP $http_code)${NC}"
        echo "$body"
        return 1
    fi
}

# 发布MQTT消息
publish_mqtt_message() {
    local topic=$1
    local message=$2
    local description=$3
    
    echo -e "${BLUE}发布MQTT消息: $description${NC}"
    echo "Topic: $topic"
    echo "Message: $message"
    
    mosquitto_pub -h "$MQTT_HOST" -p "$MQTT_PORT" \
        -t "$topic" -m "$message" \
        -u "$TEST_USER" -P "$TEST_PASSWORD" \
        -q 1
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ MQTT消息发布成功${NC}"
        return 0
    else
        echo -e "${RED}✗ MQTT消息发布失败${NC}"
        return 1
    fi
}

# 等待处理
wait_for_processing() {
    local seconds=$1
    echo -e "${YELLOW}等待 $seconds 秒以便系统处理...${NC}"
    sleep "$seconds"
}

# 主测试流程
main() {
    echo "开始MQTT预警集成测试..."
    echo
    
    # 检查依赖和配置
    check_dependencies
    check_token
    
    echo
    echo -e "${YELLOW}=== 步骤1: 创建预警规则 ===${NC}"
    
    # 创建温度过高预警规则
    create_rule_data='{
      "group_id": "'$TEST_USER'",
      "topic": "sensor/temperature",
      "rule_name": "温度过高预警测试",
      "rule_type": "threshold",
      "description": "测试用的温度预警规则",
      "conditions": [
        {
          "field": "temperature",
          "operator": ">",
          "value": "30",
          "data_type": "number"
        }
      ],
      "level": 3,
      "frequency_limit": {
        "max_count": 5,
        "time_window": 300
      },
      "notification": {
        "enabled": true,
        "channels": ["email"],
        "recipients": ["<EMAIL>"]
      },
      "enabled": true
    }'
    
    if test_api "POST" "$BASE_URL/api/groups/alert-rules" "创建温度预警规则" "$create_rule_data"; then
        echo -e "${GREEN}✓ 预警规则创建成功${NC}"
    else
        echo -e "${RED}✗ 预警规则创建失败，继续测试...${NC}"
    fi
    
    wait_for_processing 2
    
    echo
    echo -e "${YELLOW}=== 步骤2: 发布不触发预警的MQTT消息 ===${NC}"
    
    # 发布正常温度消息（不应触发预警）
    normal_message='{"temperature": 25, "humidity": 60, "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}'
    publish_mqtt_message "sensor/temperature" "$normal_message" "正常温度消息 (25°C)"
    
    wait_for_processing 3
    
    echo
    echo -e "${YELLOW}=== 步骤3: 发布触发预警的MQTT消息 ===${NC}"
    
    # 发布高温消息（应该触发预警）
    alert_message='{"temperature": 35, "humidity": 50, "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}'
    publish_mqtt_message "sensor/temperature" "$alert_message" "高温预警消息 (35°C)"
    
    wait_for_processing 5
    
    echo
    echo -e "${YELLOW}=== 步骤4: 查询预警记录 ===${NC}"
    
    # 查询预警记录
    test_api "GET" "$BASE_URL/api/groups/alert-records?group_id=$TEST_USER&topic=sensor/temperature" "查询预警记录"
    
    wait_for_processing 2
    
    echo
    echo -e "${YELLOW}=== 步骤5: 发布多条消息测试频率控制 ===${NC}"
    
    # 发布多条高温消息测试频率控制
    for i in {1..3}; do
        temp=$((35 + i))
        freq_message='{"temperature": '$temp', "humidity": 45, "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'", "sequence": '$i'}'
        publish_mqtt_message "sensor/temperature" "$freq_message" "频率测试消息 $i (${temp}°C)"
        sleep 1
    done
    
    wait_for_processing 5
    
    echo
    echo -e "${YELLOW}=== 步骤6: 再次查询预警记录 ===${NC}"
    
    # 再次查询预警记录，应该看到新的预警
    test_api "GET" "$BASE_URL/api/groups/alert-records?group_id=$TEST_USER&topic=sensor/temperature" "查询更新后的预警记录"
    
    echo
    echo -e "${YELLOW}=== 步骤7: 测试其他数据类型 ===${NC}"
    
    # 创建字符串类型的预警规则
    string_rule_data='{
      "group_id": "'$TEST_USER'",
      "topic": "device/status",
      "rule_name": "设备状态预警测试",
      "rule_type": "threshold",
      "description": "测试字符串类型的预警规则",
      "conditions": [
        {
          "field": "status",
          "operator": "equals",
          "value": "error",
          "data_type": "string"
        }
      ],
      "level": 4,
      "enabled": true
    }'
    
    if test_api "POST" "$BASE_URL/api/groups/alert-rules" "创建设备状态预警规则" "$string_rule_data"; then
        echo -e "${GREEN}✓ 字符串预警规则创建成功${NC}"
        
        wait_for_processing 2
        
        # 发布错误状态消息
        error_message='{"status": "error", "device_id": "device001", "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}'
        publish_mqtt_message "device/status" "$error_message" "设备错误状态消息"
        
        wait_for_processing 3
        
        # 查询设备状态预警记录
        test_api "GET" "$BASE_URL/api/groups/alert-records?group_id=$TEST_USER&topic=device/status" "查询设备状态预警记录"
    fi
    
    echo
    echo -e "${YELLOW}=== 步骤8: 获取预警统计 ===${NC}"
    
    # 获取预警统计信息
    test_api "GET" "$BASE_URL/api/groups/alert-statistics?group_id=$TEST_USER" "获取预警统计信息"
    
    echo
    echo -e "${GREEN}=== 测试完成 ===${NC}"
    echo
    echo "测试总结："
    echo "1. ✓ 创建了温度和设备状态预警规则"
    echo "2. ✓ 发布了正常和异常的MQTT消息"
    echo "3. ✓ 验证了预警触发和记录生成"
    echo "4. ✓ 测试了频率控制机制"
    echo "5. ✓ 验证了多种数据类型的预警"
    echo
    echo "注意事项："
    echo "- 确保MQTT服务器正在运行并监听 $MQTT_HOST:$MQTT_PORT"
    echo "- 确保用户 $TEST_USER 有权限发布消息到测试Topic"
    echo "- 预警检测是异步的，可能需要几秒钟才能看到结果"
    echo "- 检查服务器日志以获取详细的预警处理信息"
}

# 运行主测试
main "$@"
