#!/bin/bash

# WireGuard功能测试脚本
# 用于测试WireGuard API集成

set -e

echo "🧪 开始测试WireGuard功能集成..."

# 检查服务器是否运行
SERVER_URL="http://localhost:8080"
API_BASE="$SERVER_URL/api/v1/wireguard"

# 等待服务器启动
echo "⏳ 等待服务器启动..."
for i in {1..30}; do
    if curl -s "$SERVER_URL/health" > /dev/null 2>&1; then
        echo "✅ 服务器已启动"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 服务器启动超时"
        exit 1
    fi
    sleep 1
done

# 获取认证令牌（需要先登录）
echo "🔐 获取认证令牌..."
LOGIN_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "admin123"
    }')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.access_token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "❌ 无法获取认证令牌"
    echo "登录响应: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ 成功获取认证令牌"

# 测试创建WireGuard服务器
echo "🔧 测试创建WireGuard服务器..."
CREATE_SERVER_RESPONSE=$(curl -s -X POST "$API_BASE/servers" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Test Server",
        "dns": ["1.1.1.1", "8.8.8.8"],
        "mtu": 1420
    }')

echo "创建服务器响应: $CREATE_SERVER_RESPONSE"

SERVER_ID=$(echo $CREATE_SERVER_RESPONSE | jq -r '.data.id // empty')

if [ -z "$SERVER_ID" ] || [ "$SERVER_ID" = "null" ]; then
    echo "⚠️ 创建服务器可能失败，但继续测试其他功能"
else
    echo "✅ 成功创建服务器，ID: $SERVER_ID"
fi

# 测试获取服务器列表
echo "📋 测试获取服务器列表..."
LIST_SERVERS_RESPONSE=$(curl -s -X GET "$API_BASE/servers" \
    -H "Authorization: Bearer $TOKEN")

echo "服务器列表响应: $LIST_SERVERS_RESPONSE"

# 如果服务器创建成功，测试创建客户端
if [ ! -z "$SERVER_ID" ] && [ "$SERVER_ID" != "null" ]; then
    echo "👤 测试创建客户端..."
    CREATE_CLIENT_RESPONSE=$(curl -s -X POST "$API_BASE/clients" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"server_id\": \"$SERVER_ID\",
            \"name\": \"Test Client\",
            \"email\": \"<EMAIL>\"
        }")

    echo "创建客户端响应: $CREATE_CLIENT_RESPONSE"

    CLIENT_ID=$(echo $CREATE_CLIENT_RESPONSE | jq -r '.data.id // empty')

    if [ ! -z "$CLIENT_ID" ] && [ "$CLIENT_ID" != "null" ]; then
        echo "✅ 成功创建客户端，ID: $CLIENT_ID"

        # 测试获取客户端配置
        echo "📄 测试获取客户端配置..."
        CONFIG_RESPONSE=$(curl -s -X GET "$API_BASE/configs/clients/$CLIENT_ID" \
            -H "Authorization: Bearer $TOKEN")

        echo "客户端配置响应: $CONFIG_RESPONSE"

        # 测试获取QR码
        echo "📱 测试获取QR码..."
        QR_RESPONSE=$(curl -s -X GET "$API_BASE/configs/clients/$CLIENT_ID/qr" \
            -H "Authorization: Bearer $TOKEN")

        if echo "$QR_RESPONSE" | grep -q "data:image"; then
            echo "✅ 成功获取QR码"
        else
            echo "⚠️ QR码获取可能失败"
        fi
    else
        echo "⚠️ 客户端创建失败"
    fi
else
    echo "⚠️ 跳过客户端测试（服务器创建失败）"
fi

# 测试获取客户端列表
echo "👥 测试获取客户端列表..."
LIST_CLIENTS_RESPONSE=$(curl -s -X GET "$API_BASE/clients" \
    -H "Authorization: Bearer $TOKEN")

echo "客户端列表响应: $LIST_CLIENTS_RESPONSE"

echo "🎉 WireGuard功能测试完成！"
echo ""
echo "📊 测试总结："
echo "- 服务器创建: $([ ! -z "$SERVER_ID" ] && [ "$SERVER_ID" != "null" ] && echo "✅ 成功" || echo "❌ 失败")"
echo "- 服务器列表: ✅ 已测试"
echo "- 客户端创建: $([ ! -z "$CLIENT_ID" ] && [ "$CLIENT_ID" != "null" ] && echo "✅ 成功" || echo "❌ 失败或跳过")"
echo "- 客户端列表: ✅ 已测试"
echo "- 配置获取: ✅ 已测试"
echo "- QR码生成: ✅ 已测试"
echo ""
echo "💡 提示："
echo "- 如果某些测试失败，请检查配置文件中的WireGuard设置"
echo "- 确保系统具有WireGuard相关权限"
echo "- 检查网络命名空间支持"
