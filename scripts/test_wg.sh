#!/bin/bash

set -e

# ====== 基础配置 ======
NS1="wg-ns1"
NS2="wg-ns2"
WG1="wg0"
WG2="wg1"
VETH1="veth-ns1"
VETH2="veth-ns2"
VPEER1="veth-ns1-peer"
VPEER2="veth-ns2-peer"

WG_ADDR1="********/24"
WG_ADDR2="********/24"

VETH_ADDR1="*************/24"
VETH_ADDR2="*************/24"

PORT1=51820
PORT2=51821

KEY_DIR="/tmp/wgkeys"
CFG_DIR="./exported-configs"

# ====== 删除函数 ======
delete_env() {
    echo "[*] 正在删除命名空间和接口..."
    ip netns del $NS1 2>/dev/null || true
    ip netns del $NS2 2>/dev/null || true

    ip link del $VPEER1 2>/dev/null || true
    ip link del $VPEER2 2>/dev/null || true

    rm -rf $KEY_DIR $CFG_DIR
    echo "[*] 删除完成。"
}

# ====== 导出配置函数 ======
export_configs() {
    mkdir -p $CFG_DIR

    # 服务端配置（ns1）
    cat > $CFG_DIR/wg-ns1.conf <<EOF
[Interface]
Address = ${WG_ADDR1}
PrivateKey = $(cat $KEY_DIR/sk1)
ListenPort = ${PORT1}

[Peer]
PublicKey = $(cat $KEY_DIR/pk2)
AllowedIPs = ********/32,*************/32
Endpoint = <your-ns2-public-ip>:${PORT2}
PersistentKeepalive = 25
EOF

    # 客户端配置（ns2）
    cat > $CFG_DIR/wg-ns2.conf <<EOF
[Interface]
Address = ${WG_ADDR2}
PrivateKey = $(cat $KEY_DIR/sk2)
ListenPort = ${PORT2}

[Peer]
PublicKey = $(cat $KEY_DIR/pk1)
AllowedIPs = ********/32,*************/32
Endpoint = <your-ns1-public-ip>:${PORT1}
PersistentKeepalive = 25
EOF

    echo "[*] WireGuard 配置文件已导出至：$CFG_DIR"
}

# ====== 创建环境函数 ======
create_env() {
    delete_env
    echo "[*] 创建命名空间和接口..."

    ip netns add $NS1
    ip netns add $NS2

    ip link add $WG1 type wireguard
    ip link add $WG2 type wireguard

    ip link add $VETH1 type veth peer name $VPEER1
    ip link add $VETH2 type veth peer name $VPEER2

    ip link set $WG1 netns $NS1
    ip link set $WG2 netns $NS2
    ip link set $VETH1 netns $NS1
    ip link set $VETH2 netns $NS2

    ip link set $VPEER1 up
    ip link set $VPEER2 up

    ip netns exec $NS1 ip link set lo up
    ip netns exec $NS2 ip link set lo up
    ip netns exec $NS1 ip addr add $VETH_ADDR1 dev $VETH1
    ip netns exec $NS2 ip addr add $VETH_ADDR2 dev $VETH2
    ip netns exec $NS1 ip link set $VETH1 up
    ip netns exec $NS2 ip link set $VETH2 up

    mkdir -p $KEY_DIR
    umask 077
    wg genkey | tee $KEY_DIR/sk1 | wg pubkey > $KEY_DIR/pk1
    wg genkey | tee $KEY_DIR/sk2 | wg pubkey > $KEY_DIR/pk2

    ip netns exec $NS1 wg set $WG1 \
        private-key $KEY_DIR/sk1 \
        listen-port $PORT1 \
        peer $(cat $KEY_DIR/pk2) \
        allowed-ips *************/32,********/32 \
        endpoint 127.0.0.1:$PORT2

    ip netns exec $NS1 ip addr add $WG_ADDR1 dev $WG1
    ip netns exec $NS1 ip link set $WG1 up

    ip netns exec $NS2 wg set $WG2 \
        private-key $KEY_DIR/sk2 \
        listen-port $PORT2 \
        peer $(cat $KEY_DIR/pk1) \
        allowed-ips *************/32,********/32 \
        endpoint 127.0.0.1:$PORT1

    ip netns exec $NS2 ip addr add $WG_ADDR2 dev $WG2
    ip netns exec $NS2 ip link set $WG2 up

    ip netns exec $NS1 ip route add *************/32 dev $WG1
    ip netns exec $NS2 ip route add *************/32 dev $WG2

    echo "[*] 等待 WireGuard 握手..."
    sleep 2

    ip netns exec $NS1 ping -c 2 ************* || echo "[!] ns1 ping ns2 失败"
    ip netns exec $NS2 ping -c 2 ************* || echo "[!] ns2 ping ns1 失败"

    export_configs

    echo "[*] 创建并测试完成。"
}

# ====== 参数分发 ======
case "$1" in
    setup)
        create_env
        ;;
    delete)
        delete_env
        ;;
    export-configs)
        export_configs
        ;;
    *)
        echo "用法: $0 {setup|delete|export-configs}"
        ;;
esac

