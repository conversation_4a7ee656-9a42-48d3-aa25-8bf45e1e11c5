package test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/handler"
	"beacon/cloud/internal/model"
	"beacon/cloud/internal/repository"
	"beacon/cloud/internal/service"
	"beacon/cloud/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// setupTestDeviceHandler 设置测试用的设备处理器
func setupTestDeviceHandler(t *testing.T) (*handler.DeviceHandler, *database.DatabaseManager, func()) {
	// 加载测试配置
	cfg, err := config.LoadConfig("../configs/config.yaml")
	require.NoError(t, err)

	// 使用测试数据库
	cfg.MongoDB.DBName = "beacon_test_device"

	// 初始化数据库管理器
	dbManager := database.NewDatabaseManager(cfg)
	ctx := context.Background()
	err = dbManager.Initialize(ctx)
	require.NoError(t, err)

	// 初始化仓储
	deviceRepo := repository.NewDeviceRepository(dbManager.GetMongoDB().GetDatabase())
	deviceTypeRepo := repository.NewDeviceTypeRepository(dbManager.GetMongoDB().GetDatabase())

	// 初始化服务
	deviceService := service.NewDeviceService(deviceRepo, deviceTypeRepo)

	// 初始化处理器
	deviceHandler := handler.NewDeviceHandler(deviceService)

	// 初始化预定义设备类型
	err = deviceService.InitializePredefinedDeviceTypes(ctx)
	require.NoError(t, err)

	// 返回清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupCtx := context.Background()
		db := dbManager.GetMongoDB().GetDatabase()
		db.Collection("devices").Drop(cleanupCtx)
		db.Collection("device_types").Drop(cleanupCtx)
		dbManager.Close(cleanupCtx)
	}

	return deviceHandler, dbManager, cleanup
}

// TestCreateDeviceType 测试创建设备类型
func TestCreateDeviceType(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/device-types", deviceHandler.CreateDeviceType)

	// 测试创建设备类型
	req := model.CreateDeviceTypeRequest{
		Name:        "TEST-DEVICE",
		Description: "测试设备类型",
	}

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/device-types", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "设备类型创建成功", response.Message)
}

// TestCreateDevice 测试创建设备
func TestCreateDevice(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加用户ID中间件模拟认证
	router.Use(func(c *gin.Context) {
		c.Set("user_id", primitive.NewObjectID().Hex())
		c.Next()
	})

	router.POST("/api/admin/devices", deviceHandler.AdminCreateDevice)

	// 测试创建设备
	req := model.CreateDeviceRequest{
		DeviceType:   "BC-3GM-R", // 使用预定义的设备类型
		DeviceName:   "测试设备001",
		SerialNumber: "SN001",
		IMEICode:     "IMEI001",
	}

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/admin/devices", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "设备创建成功", response.Message)
}

// TestListDeviceTypes 测试获取设备类型列表
func TestListDeviceTypes(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/api/device-types", deviceHandler.ListDeviceTypes)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/device-types", nil)

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusOK, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "获取设备类型列表成功", response.Message)

	// 验证预定义设备类型是否存在
	data := response.Data.(map[string]interface{})
	deviceTypes := data["device_types"].([]interface{})
	assert.GreaterOrEqual(t, len(deviceTypes), 4) // 至少有4个预定义设备类型
}

// TestListDevices 测试获取设备列表
func TestListDevices(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加用户ID中间件模拟认证
	userID := primitive.NewObjectID().Hex()
	router.Use(func(c *gin.Context) {
		c.Set("user_id", userID)
		c.Next()
	})

	router.GET("/api/admin/devices", deviceHandler.AdminListDevices)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/admin/devices", nil)

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusOK, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "获取设备列表成功", response.Message)
}

// TestDeviceValidation 测试设备验证
func TestDeviceValidation(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加用户ID中间件模拟认证
	router.Use(func(c *gin.Context) {
		c.Set("user_id", primitive.NewObjectID().Hex())
		c.Next()
	})

	router.POST("/api/admin/devices", deviceHandler.AdminCreateDevice)

	// 测试无效的设备类型
	req := model.CreateDeviceRequest{
		DeviceType:   "INVALID-TYPE",
		DeviceName:   "测试设备",
		SerialNumber: "SN001",
		IMEICode:     "IMEI001",
	}

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/admin/devices", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "设备类型无效", response.Message)
}

// TestUserAssignDevice 测试用户添加设备
func TestUserAssignDevice(t *testing.T) {
	deviceHandler, _, cleanup := setupTestDeviceHandler(t)
	defer cleanup()

	gin.SetMode(gin.TestMode)
	router := gin.New()

	userID := primitive.NewObjectID().Hex()

	// 添加用户ID中间件模拟认证
	router.Use(func(c *gin.Context) {
		c.Set("user_id", userID)
		c.Next()
	})

	// 设置路由
	router.POST("/api/admin/devices", deviceHandler.AdminCreateDevice)
	router.POST("/api/user/devices", deviceHandler.UserAssignDevice)
	router.GET("/api/user/devices", deviceHandler.UserListDevices)

	// 首先管理员创建一个设备
	createReq := model.CreateDeviceRequest{
		DeviceType:   "BC-3GM-R",
		DeviceName:   "测试设备001",
		SerialNumber: "SN001",
		IMEICode:     "IMEI001",
	}

	reqBody, _ := json.Marshal(createReq)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/admin/devices", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)
	assert.Equal(t, http.StatusCreated, w.Code)

	// 然后用户添加这个设备
	assignReq := model.AssignDeviceRequest{
		SerialNumber: "SN001",
		IMEICode:     "IMEI001",
	}

	reqBody, _ = json.Marshal(assignReq)
	w = httptest.NewRecorder()
	httpReq, _ = http.NewRequest("POST", "/api/user/devices", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)
	assert.Equal(t, http.StatusOK, w.Code)

	var response handler.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "设备添加成功", response.Message)

	// 验证用户设备列表
	w = httptest.NewRecorder()
	httpReq, _ = http.NewRequest("GET", "/api/user/devices", nil)

	router.ServeHTTP(w, httpReq)
	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)

	data := response.Data.(map[string]interface{})
	devices := data["devices"].([]interface{})
	assert.Equal(t, 1, len(devices))
}
