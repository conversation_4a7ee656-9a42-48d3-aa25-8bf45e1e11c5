package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"beacon/cloud/internal/config"
	"beacon/cloud/internal/handler"
	"beacon/cloud/internal/middleware"
	"beacon/cloud/pkg/database"
	"beacon/cloud/pkg/wireguard"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWireGuardIntegration(t *testing.T) {
	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 创建测试配置
	cfg := &config.Config{
		WireGuard: config.WireGuardConfig{
			Host:                "test.example.com",
			BasePort:            51820,
			MaxUsers:            100,
			MaxServersPerUser:   5,
			MaxClientsPerServer: 10,
			BaseNetwork:         "********/16",
			DefaultDNS:          []string{"*******", "*******"},
			DefaultMTU:          1420,
			EncryptionKey:       "test-32-byte-encryption-key-here!",
		},
	}

	// 注意：在实际测试中，你需要设置真实的数据库连接
	// 这里只是演示集成结构
	t.<PERSON><PERSON>("需要真实的数据库连接才能运行此测试")

	// 创建WireGuard配置
	wgConfig := &wireguard.Config{
		Host:                cfg.WireGuard.Host,
		BasePort:            cfg.WireGuard.BasePort,
		MaxUsers:            cfg.WireGuard.MaxUsers,
		MaxServersPerUser:   cfg.WireGuard.MaxServersPerUser,
		MaxClientsPerServer: cfg.WireGuard.MaxClientsPerServer,
		BaseNetwork:         cfg.WireGuard.BaseNetwork,
		DefaultDNS:          cfg.WireGuard.DefaultDNS,
		DefaultMTU:          cfg.WireGuard.DefaultMTU,
		EncryptionKey:       cfg.WireGuard.EncryptionKey,
	}

	// 初始化数据库管理器（需要真实连接）
	dbManager := database.NewDatabaseManager(cfg)
	ctx := context.Background()
	err := dbManager.Initialize(ctx)
	require.NoError(t, err)

	// 创建WireGuard管理器
	wgManager, err := wireguard.NewWireGuardManager(
		dbManager.GetMongoDB().GetDatabase(),
		dbManager.GetRedis().GetClient(),
		wgConfig,
	)
	require.NoError(t, err)

	// 创建处理器
	wireGuardHandler := handler.NewWireGuardHandler(wgManager)

	// 创建路由
	router := gin.New()
	authMiddleware := &middleware.AuthMiddleware{} // 简化的中间件

	// 设置WireGuard路由
	wgGroup := router.Group("/api/v1/wireguard")
	serverGroup := wgGroup.Group("/servers")
	{
		serverGroup.POST("", wireGuardHandler.CreateServer)
		serverGroup.GET("", wireGuardHandler.ListServers)
	}

	// 测试创建服务器
	t.Run("CreateServer", func(t *testing.T) {
		createReq := map[string]interface{}{
			"name": "Test Server",
			"dns":  []string{"*******", "*******"},
			"mtu":  1420,
		}

		reqBody, _ := json.Marshal(createReq)
		req := httptest.NewRequest("POST", "/api/v1/wireguard/servers", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "success", response["status"])
	})

	// 测试获取服务器列表
	t.Run("ListServers", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/wireguard/servers", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "success", response["status"])
	})
}

func TestWireGuardConfigValidation(t *testing.T) {
	// 测试配置验证
	tests := []struct {
		name    string
		config  config.WireGuardConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: config.WireGuardConfig{
				Host:                "vpn.example.com",
				BasePort:            51820,
				MaxUsers:            1000,
				MaxServersPerUser:   5,
				MaxClientsPerServer: 100,
				BaseNetwork:         "********/16",
				DefaultDNS:          []string{"*******", "*******"},
				DefaultMTU:          1420,
				EncryptionKey:       "test-32-byte-encryption-key-here!",
			},
			wantErr: false,
		},
		{
			name: "missing host",
			config: config.WireGuardConfig{
				BasePort:            51820,
				MaxUsers:            1000,
				MaxServersPerUser:   5,
				MaxClientsPerServer: 100,
				BaseNetwork:         "********/16",
				DefaultDNS:          []string{"*******", "*******"},
				DefaultMTU:          1420,
				EncryptionKey:       "test-32-byte-encryption-key-here!",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证配置的基本字段
			if tt.config.Host == "" && !tt.wantErr {
				t.Error("Expected host to be set for valid config")
			}
			if tt.config.Host != "" && tt.wantErr && tt.name == "missing host" {
				t.Error("Expected host to be empty for missing host test")
			}
		})
	}
}
